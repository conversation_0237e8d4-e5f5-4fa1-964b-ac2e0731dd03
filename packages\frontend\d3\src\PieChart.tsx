import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface PieChartData {
  label: string;
  value: number;
}

export interface PieChartProps {
  data: PieChartData[];
  width?: number;
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
  colors?: string[];
  className?: string;
  showLabels?: boolean;
  showLegend?: boolean;
  centerLabel?: string;
}

export const PieChart: React.FC<PieChartProps> = ({
  data,
  width = 300,
  height = 300,
  innerRadius = 0,
  outerRadius,
  colors = d3.schemeCategory10,
  className = '',
  showLabels = true,
  showLegend = true,
  centerLabel = 'Total',
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const defaultOuterRadius = Math.min(width, height) / 2 - 10;

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const radius = outerRadius || defaultOuterRadius;
    const colorScale = d3.scaleOrdinal(colors);

    const pie = d3
      .pie<PieChartData>()
      .value((d) => d.value)
      .sort(null);

    const arc = d3
      .arc<d3.PieArcDatum<PieChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(radius);

    const labelArc = d3
      .arc<d3.PieArcDatum<PieChartData>>()
      .innerRadius(radius + 10)
      .outerRadius(radius + 10);

    const g = svg
      .append('g')
      .attr('transform', `translate(${width / 2},${height / 2})`);

    const arcs = g
      .selectAll('.arc')
      .data(pie(data))
      .enter()
      .append('g')
      .attr('class', 'arc');

    // Add pie slices
    arcs
      .append('path')
      .attr('d', arc)
      .attr('fill', (_d: d3.PieArcDatum<PieChartData>, i: number) =>
        colorScale(i.toString()),
      )
      .style('cursor', 'pointer')
      .on('mouseover', function (event, d: d3.PieArcDatum<PieChartData>) {
        d3.select(this).attr('opacity', 0.8);

        // Add tooltip
        const tooltip = d3
          .select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        const percentage = (
          (d.data.value / d3.sum(data, (d: PieChartData) => d.value)) *
          100
        ).toFixed(1);
        tooltip
          .html(`${d.data.label}: ${d.data.value} (${percentage}%)`)
          .style('left', event.pageX + 10 + 'px')
          .style('top', event.pageY - 10 + 'px');
      })
      .on('mouseout', function () {
        d3.select(this).attr('opacity', 1);
        d3.selectAll('.tooltip').remove();
      });

    // Add labels
    if (showLabels) {
      arcs
        .append('text')
        .attr(
          'transform',
          (d: d3.PieArcDatum<PieChartData>) =>
            `translate(${labelArc.centroid(d)})`,
        )
        .attr('dy', '0.35em')
        .style('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text((d: d3.PieArcDatum<PieChartData>) => {
          const percentage =
            (d.data.value / d3.sum(data, (d: PieChartData) => d.value)) * 100;
          return percentage > 5 ? d.data.label : ''; // Only show label if slice is > 5%
        });
    }

    // Add center total (only if innerRadius > 0 for doughnut charts)
    if (innerRadius > 0) {
      const total = d3.sum(data, (d: PieChartData) => d.value);

      // Add total number
      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '-0.2em')
        .style('font-size', '24px')
        .style('font-weight', 'bold')
        .style('fill', 'var(--color-text-primary)')
        .text(total.toString());

      // Add "Total" label
      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '1.2em')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-secondary)')
        .text(centerLabel);
    }

    // Add legend
    if (showLegend) {
      const legend = svg
        .selectAll('.legend')
        .data(data)
        .enter()
        .append('g')
        .attr('class', 'legend')
        .attr('transform', (_d, i) => `translate(10, ${i * 20 + 20})`);

      legend
        .append('rect')
        .attr('width', 15)
        .attr('height', 15)
        .attr('fill', (_d: PieChartData, i: number) =>
          colorScale(i.toString()),
        );

      legend
        .append('text')
        .attr('x', 20)
        .attr('y', 12)
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text((d: PieChartData) => d.label);
    }
  }, [
    data,
    width,
    height,
    innerRadius,
    outerRadius,
    colors,
    showLabels,
    showLegend,
    centerLabel,
    defaultOuterRadius,
  ]);

  return (
    <div className={className}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ display: 'block' }}
      />
    </div>
  );
};
