import React from 'react';
import { TelesoftLogo, CompanyLogo, BrandIcon, LOGO_ASSETS } from '../../index';

/**
 * Example usage of the branding package components
 */
export function BrandingDemo() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold mb-6">Telesoft Branding Components</h1>

      {/* TelesoftLogo Examples */}
      <section>
        <h2 className="text-xl font-semibold mb-4">TelesoftLogo Component</h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Full (Small):</span>
            <TelesoftLogo size="small" variant="full" />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Full (Medium):</span>
            <TelesoftLogo size="medium" variant="full" />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Full (Large):</span>
            <TelesoftLogo size="large" variant="full" />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Icon Only:</span>
            <TelesoftLogo size="medium" variant="icon" />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Text Only:</span>
            <TelesoftLogo size="medium" variant="text" />
          </div>
        </div>
      </section>

      {/* CompanyLogo Examples */}
      <section>
        <h2 className="text-xl font-semibold mb-4">CompanyLogo Component</h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Small:</span>
            <CompanyLogo size="small" />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Medium:</span>
            <CompanyLogo size="medium" />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Large:</span>
            <CompanyLogo size="large" />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Custom Size:</span>
            <CompanyLogo size={40} />
          </div>
        </div>
      </section>

      {/* BrandIcon Examples */}
      <section>
        <h2 className="text-xl font-semibold mb-4">BrandIcon Component</h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Default:</span>
            <BrandIcon />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Large:</span>
            <BrandIcon size={48} />
          </div>
          <div className="flex items-center space-x-4">
            <span className="w-20 text-sm">Colored:</span>
            <BrandIcon size={32} color="#3B82F6" />
          </div>
        </div>
      </section>

      {/* Logo Assets Info */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Available Logo Assets</h2>
        <div className="bg-gray-50 p-4 rounded">
          <pre className="text-sm">{JSON.stringify(LOGO_ASSETS, null, 2)}</pre>
        </div>
      </section>
    </div>
  );
}
