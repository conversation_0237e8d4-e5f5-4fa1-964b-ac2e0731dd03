import { ThreatIncident, ThreatsResponse } from '@telesoft/types';
import { apiClient } from '../api-client';
import { getGlobalConfig } from '../contexts/config-provider';

export interface ThreatsWebSocketMessage {
  type:
    | 'threats-update'
    | 'threats-initial'
    | 'ping'
    | 'pong'
    | 'subscribe'
    | 'unsubscribe';
  data?: {
    incidents?: ThreatIncident[];
    action?: 'append'; // Simplified - only append action for updates
  };
  timestamp: string;
}

/**
 * Service for managing threat incidents data
 */
export class ThreatsService {
  private wsConnection: WebSocket | null = null;
  private wsUrl: string | null = null;
  private reconnectTimeoutId: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private currentReconnectInterval = 5000; // Start with 5 seconds
  private maxReconnectInterval = 30000; // Cap at 30 seconds
  private shouldReconnect = false;

  // Callback storage for reconnection
  private onMessageCallback:
    | ((message: ThreatsWebSocketMessage) => void)
    | null = null;
  private onErrorCallback: ((error: Event) => void) | null = null;
  private onCloseCallback: ((event: CloseEvent) => void) | null = null;
  private onOpenCallback: (() => void) | null = null;

  /**
   * Get WebSocket URL for threats
   * Note: WebSocket connections are ALWAYS direct to backend (no Next.js proxy)
   * Always gets fresh URL from current config to handle runtime config changes
   */
  private getWebSocketUrl(): string {
    const config = getGlobalConfig().api;
    // WebSocket connections bypass Next.js and connect directly to backend
    const wsUrl = config.wsUrl.replace(/^http/, 'ws');
    const fullWsUrl = `${wsUrl}/threats`;

    return fullWsUrl;
  }

  /**
   * Connect to WebSocket for real-time threat updates with automatic reconnection
   * First fetches initial data over HTTP, then establishes WebSocket for incremental updates
   */
  async connectWebSocket(
    onMessage: (message: ThreatsWebSocketMessage) => void,
    onError?: (error: Event) => void,
    onClose?: (event: CloseEvent) => void,
    onOpen?: () => void,
  ): Promise<{ ws: WebSocket; initialData: ThreatIncident[] }> {
    // Check if already connected or connecting
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected, returning existing connection');
      const existingData = await this.getThreats();
      return { ws: this.wsConnection, initialData: existingData };
    }

    if (this.wsConnection?.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket connection already in progress');
      throw new Error('WebSocket connection already in progress');
    }

    // Store callbacks for reconnection
    this.onMessageCallback = onMessage;
    this.onErrorCallback = onError || null;
    this.onCloseCallback = onClose || null;
    this.onOpenCallback = onOpen || null;
    this.shouldReconnect = true;

    // First, fetch initial data over HTTP to avoid WebSocket message size limits
    console.log('Fetching initial threat data over HTTP...');
    const initialData = await this.getThreats();

    // Send initial data through the message callback
    const initialMessage: ThreatsWebSocketMessage = {
      type: 'threats-initial',
      data: {
        incidents: initialData,
      },
      timestamp: new Date().toISOString(),
    };
    onMessage(initialMessage);

    // Then establish WebSocket connection for incremental updates
    const ws = this.establishConnection();

    return { ws, initialData };
  }

  /**
   * Internal method to establish WebSocket connection
   */
  private establishConnection(): WebSocket {
    // Close any existing connection first
    if (this.wsConnection) {
      if (this.wsConnection.readyState === WebSocket.OPEN) {
        console.log(
          'Closing existing WebSocket connection before creating new one',
        );
        this.wsConnection.close(1000, 'Creating new connection');
      }
      this.wsConnection = null;
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    console.log(
      'Creating new WebSocket connection to:',
      this.getWebSocketUrl(),
    );
    const ws = new WebSocket(this.getWebSocketUrl());
    this.wsConnection = ws;

    ws.onopen = () => {
      console.log('Threats WebSocket connected');
      this.reconnectAttempts = 0;
      this.currentReconnectInterval = 5000; // Reset interval on successful connection

      // Subscribe to threats updates
      const subscribeMessage: ThreatsWebSocketMessage = {
        type: 'subscribe',
        timestamp: new Date().toISOString(),
      };
      ws.send(JSON.stringify(subscribeMessage));
      this.onOpenCallback?.();
    };

    ws.onmessage = (event) => {
      try {
        const message: ThreatsWebSocketMessage = JSON.parse(event.data);
        this.onMessageCallback?.(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('Threats WebSocket error:', error);
      this.onErrorCallback?.(error);
    };

    ws.onclose = (event) => {
      console.log('Threats WebSocket closed:', event.code, event.reason);
      this.wsConnection = null;
      this.onCloseCallback?.(event);

      // Only attempt reconnection if it wasn't a clean close and we should reconnect
      if (event.code !== 1000 && this.shouldReconnect) {
        console.log(
          'Threats WebSocket closed unexpectedly, attempting reconnection...',
        );
        this.attemptReconnection();
      } else if (event.code === 1000) {
        console.log('Threats WebSocket closed cleanly, not reconnecting');
      } else {
        console.log('Threats WebSocket reconnection disabled');
      }
    };

    return ws;
  }

  /**
   * Attempt to reconnect with exponential backoff
   */
  private attemptReconnection(): void {
    // Don't reconnect if we shouldn't or if already reconnecting
    if (!this.shouldReconnect || this.reconnectTimeoutId !== null) {
      console.log('Reconnection disabled or already in progress');
      return;
    }

    // Don't reconnect if we already have a connection
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected, skipping reconnection');
      return;
    }

    this.reconnectAttempts++;

    // Calculate exponential backoff with jitter
    const exponentialDelay = Math.min(
      this.currentReconnectInterval *
        Math.pow(2, Math.min(this.reconnectAttempts - 1, 6)),
      this.maxReconnectInterval,
    );
    const jitter = Math.random() * 1000; // Add up to 1 second of jitter
    const delay = exponentialDelay + jitter;

    this.currentReconnectInterval = delay;

    console.log(
      `Threats WebSocket disconnected. Attempting to reconnect in ${Math.round(delay)}ms (attempt ${this.reconnectAttempts})...`,
    );

    this.reconnectTimeoutId = setTimeout(async () => {
      // Clear the timeout ID since we're now executing
      this.reconnectTimeoutId = null;

      if (this.shouldReconnect && this.onMessageCallback) {
        try {
          // Check again if we should still reconnect
          if (this.wsConnection?.readyState === WebSocket.OPEN) {
            console.log(
              'WebSocket connected during reconnection delay, aborting reconnect',
            );
            return;
          }

          // Fetch fresh initial data on reconnection
          console.log('Fetching fresh threat data on reconnection...');
          const initialData = await this.getThreats();

          // Send initial data through the message callback
          const initialMessage: ThreatsWebSocketMessage = {
            type: 'threats-initial',
            data: {
              incidents: initialData,
            },
            timestamp: new Date().toISOString(),
          };
          this.onMessageCallback(initialMessage);

          // Then re-establish WebSocket connection
          this.establishConnection();
        } catch (error) {
          console.error('Failed to fetch initial data on reconnection:', error);
          // Continue with WebSocket connection even if HTTP fetch fails
          this.establishConnection();
        }
      }
    }, delay);
  }

  /**
   * Disconnect from WebSocket and stop reconnection attempts
   */
  disconnectWebSocket(): void {
    this.shouldReconnect = false;

    // Clear reconnection timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    // Close connection
    if (this.wsConnection) {
      this.wsConnection.close();
      this.wsConnection = null;
    }

    // Clear callbacks
    this.onMessageCallback = null;
    this.onErrorCallback = null;
    this.onCloseCallback = null;
    this.onOpenCallback = null;
  }

  /**
   * Send a message through the WebSocket connection
   */
  sendWebSocketMessage(message: ThreatsWebSocketMessage): boolean {
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      this.wsConnection.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  /**
   * Check if WebSocket is connected
   */
  isWebSocketConnected(): boolean {
    return this.wsConnection?.readyState === WebSocket.OPEN;
  }

  /**
   * Fetch all threat incidents from the backend API
   */
  async getThreats(): Promise<ThreatIncident[]> {
    try {
      const response = await apiClient.get<ThreatsResponse>('/api/v1/threats', {
        skipCache: true, // Force skip cache for threats data
      });
      return response.data.incidents;
    } catch (error) {
      console.error('Failed to fetch threats:', error);

      // Handle specific error types for better user experience
      if (error instanceof Error) {
        // Check for backend connectivity issues
        if (
          error.message.includes('Backend Unavailable') ||
          error.message.includes('Service Unavailable') ||
          error.message.includes('503')
        ) {
          console.warn(
            'Backend service is unavailable, returning empty threats array',
          );
          // Return empty array instead of throwing error to allow app to continue
          return [];
        }

        // Check for connection failures
        if (
          error.message.includes('Failed to connect') ||
          error.message.includes('Connection refused') ||
          error.message.includes('ECONNREFUSED')
        ) {
          console.warn(
            'Backend connection failed, returning empty threats array',
          );
          return [];
        }
      }

      // For other errors, still throw to maintain error handling behavior
      throw error;
    }
  }

  /**
   * Filter threats by incident type
   */
  filterByType(
    threats: ThreatIncident[],
    type: ThreatIncident['incident_type'],
  ): ThreatIncident[] {
    return threats.filter((threat) => threat.incident_type === type);
  }

  /**
   * Filter threats by severity level
   */
  filterByLevel(
    threats: ThreatIncident[],
    level: ThreatIncident['risk_severity'],
  ): ThreatIncident[] {
    return threats.filter((threat) => threat.risk_severity === level);
  }

  /**
   * Filter threats by progress status
   */
  filterByProgress(
    threats: ThreatIncident[],
    progress: ThreatIncident['investigation_status'],
  ): ThreatIncident[] {
    return threats.filter((threat) => threat.investigation_status === progress);
  }

  /**
   * Get threat counts by type
   */
  getTypeDistribution(
    threats: ThreatIncident[],
  ): Record<ThreatIncident['incident_type'], number> {
    return threats.reduce(
      (acc, threat) => {
        acc[threat.incident_type] = (acc[threat.incident_type] || 0) + 1;
        return acc;
      },
      {} as Record<ThreatIncident['incident_type'], number>,
    );
  }

  /**
   * Get threat counts by severity level
   */
  getLevelDistribution(
    threats: ThreatIncident[],
  ): Record<ThreatIncident['risk_severity'], number> {
    return threats.reduce(
      (acc, threat) => {
        acc[threat.risk_severity] = (acc[threat.risk_severity] || 0) + 1;
        return acc;
      },
      {} as Record<ThreatIncident['risk_severity'], number>,
    );
  }

  /**
   * Get most recent threats (sorted by time)
   */
  getRecentThreats(
    threats: ThreatIncident[],
    limit: number = 10,
  ): ThreatIncident[] {
    return [...threats]
      .sort((a, b) => (b.time || 0) - (a.time || 0))
      .slice(0, limit);
  }

  /**
   * Get critical threats that need immediate attention
   */
  getCriticalThreats(threats: ThreatIncident[]): ThreatIncident[] {
    return threats.filter(
      (threat) =>
        threat.risk_severity === 'critical' &&
        (threat.investigation_status === 'created' ||
          threat.investigation_status === 'running'),
    );
  }

  /**
   * Refresh WebSocket connection with updated configuration
   * This should be called when configuration changes
   */
  async refreshConnectionWithNewConfig(): Promise<void> {
    console.log('[ThreatsService] Refreshing connection with new config');

    // If there's an active connection, disconnect first
    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
      console.log(
        '[ThreatsService] Disconnecting existing connection for config refresh',
      );
      this.disconnectWebSocket();

      // Reconnect with new config after a short delay
      setTimeout(async () => {
        if (this.onMessageCallback) {
          console.log('[ThreatsService] Reconnecting with new config');
          try {
            await this.connectWebSocket(
              this.onMessageCallback,
              this.onErrorCallback || undefined,
              this.onCloseCallback || undefined,
              this.onOpenCallback || undefined,
            );
          } catch (error) {
            console.error(
              '[ThreatsService] Failed to reconnect with new config:',
              error,
            );
          }
        }
      }, 100);
    }
  }
}

// Singleton instance
export const threatsService = new ThreatsService();
