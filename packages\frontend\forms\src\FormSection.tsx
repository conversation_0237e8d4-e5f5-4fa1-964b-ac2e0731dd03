import React from 'react';
import { classNames } from '@telesoft/utils';

export interface FormSectionProps {
  title: string;
  description?: string;
  icon?: string;
  children: React.ReactNode;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  icon,
  children,
  className,
}) => (
  <div className={classNames('space-y-4', className)}>
    <div>
      <h3 className="text-lg font-medium text-text-primary flex items-center gap-2">
        {icon && <span>{icon}</span>}
        {title}
      </h3>
      {description && <p className="text-text-secondary mt-1">{description}</p>}
    </div>
    <div className="space-y-4">{children}</div>
  </div>
);
