import type { LogoProps } from './TelesoftLogo';

/**
 * Company logo - icon only version
 * Simplified alias for TelesoftLogo with icon variant
 */
export function CompanyLogo({
  size = 'medium',
  className = '',
}: Omit<LogoProps, 'variant'>) {
  const sizeMap = {
    small: '24px',
    medium: '32px',
    large: '48px',
    xlarge: '64px',
  };

  const logoSize = typeof size === 'number' ? `${size}px` : sizeMap[size];

  return (
    <div
      className={`bg-primary-500 rounded flex items-center justify-center ${className}`}
      style={{
        width: logoSize,
        height: logoSize,
        minWidth: logoSize,
        minHeight: logoSize,
      }}
    >
      <span
        className="text-white font-bold"
        style={{ fontSize: `calc(${logoSize} * 0.6)` }}
      >
        TS
      </span>
    </div>
  );
}
