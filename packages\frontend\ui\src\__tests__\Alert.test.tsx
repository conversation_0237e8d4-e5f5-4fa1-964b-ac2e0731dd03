import React from 'react';
import { render, screen } from '@testing-library/react';
import { Alert, AlertTitle, AlertDescription } from '../Alert';

describe('Alert', () => {
  describe('Basic rendering', () => {
    it('renders with default props', () => {
      render(<Alert>Alert content</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toBeInTheDocument();
      expect(alert).toHaveTextContent('Alert content');
    });

    it('renders children correctly', () => {
      render(
        <Alert>
          <span>Custom alert content</span>
        </Alert>,
      );
      expect(screen.getByText('Custom alert content')).toBeInTheDocument();
    });

    it('has proper role attribute', () => {
      render(<Alert>Alert</Alert>);
      expect(screen.getByRole('alert')).toBeInTheDocument();
    });
  });

  describe('Variants', () => {
    const variants = [
      'default',
      'success',
      'warning',
      'danger',
      'info',
    ] as const;

    variants.forEach((variant) => {
      it(`renders ${variant} variant correctly`, () => {
        render(<Alert variant={variant}>Alert content</Alert>);
        const alert = screen.getByRole('alert');
        expect(alert).toBeInTheDocument();
        expect(alert).toHaveTextContent('Alert content');
      });
    });

    it('applies default variant when no variant is specified', () => {
      render(<Alert>Default alert</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('border-border-primary');
    });

    it('applies success variant classes', () => {
      render(<Alert variant="success">Success alert</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('border-cyber-matrix-500/50');
    });

    it('applies warning variant classes', () => {
      render(<Alert variant="warning">Warning alert</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('border-cyber-warning-500/50');
    });

    it('applies danger variant classes', () => {
      render(<Alert variant="danger">Danger alert</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('border-cyber-danger-500/50');
    });

    it('applies info variant classes', () => {
      render(<Alert variant="info">Info alert</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('border-primary-500/50');
    });
  });

  describe('Custom props', () => {
    it('applies custom className', () => {
      render(<Alert className="custom-class">Alert</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('custom-class');
    });

    it('passes through additional props', () => {
      render(<Alert data-testid="custom-alert">Alert</Alert>);
      expect(screen.getByTestId('custom-alert')).toBeInTheDocument();
    });

    it('forwards ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<Alert ref={ref}>Alert</Alert>);
      expect(ref.current).toBeInstanceOf(HTMLDivElement);
    });
  });

  describe('Base classes', () => {
    it('applies base classes', () => {
      render(<Alert>Alert</Alert>);
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass(
        'relative',
        'w-full',
        'rounded-lg',
        'border',
        'p-4',
      );
    });
  });
});

describe('AlertTitle', () => {
  it('renders as h5 element', () => {
    render(<AlertTitle>Alert Title</AlertTitle>);
    const title = screen.getByRole('heading', { level: 5 });
    expect(title).toBeInTheDocument();
    expect(title).toHaveTextContent('Alert Title');
  });

  it('applies default classes', () => {
    render(<AlertTitle>Title</AlertTitle>);
    const title = screen.getByRole('heading', { level: 5 });
    expect(title).toHaveClass(
      'mb-1',
      'font-medium',
      'leading-none',
      'tracking-tight',
    );
  });

  it('applies custom className', () => {
    render(<AlertTitle className="custom-title">Title</AlertTitle>);
    const title = screen.getByRole('heading', { level: 5 });
    expect(title).toHaveClass('custom-title');
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLParagraphElement>();
    render(<AlertTitle ref={ref}>Title</AlertTitle>);
    expect(ref.current).toBeInstanceOf(HTMLHeadingElement);
  });

  it('passes through additional props', () => {
    render(<AlertTitle data-testid="alert-title">Title</AlertTitle>);
    expect(screen.getByTestId('alert-title')).toBeInTheDocument();
  });
});

describe('AlertDescription', () => {
  it('renders as div element', () => {
    render(<AlertDescription>Alert Description</AlertDescription>);
    const description = screen.getByText('Alert Description');
    expect(description).toBeInTheDocument();
    expect(description.tagName).toBe('DIV');
  });

  it('applies default classes', () => {
    render(<AlertDescription>Description</AlertDescription>);
    const description = screen.getByText('Description');
    expect(description).toHaveClass('text-sm', 'opacity-90');
  });

  it('applies custom className', () => {
    render(
      <AlertDescription className="custom-desc">Description</AlertDescription>,
    );
    const description = screen.getByText('Description');
    expect(description).toHaveClass('custom-desc');
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLParagraphElement>();
    render(<AlertDescription ref={ref}>Description</AlertDescription>);
    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('passes through additional props', () => {
    render(
      <AlertDescription data-testid="alert-desc">Description</AlertDescription>,
    );
    expect(screen.getByTestId('alert-desc')).toBeInTheDocument();
  });
});

describe('Alert Composition', () => {
  it('renders complete alert with title and description', () => {
    render(
      <Alert variant="success">
        <AlertTitle>Success!</AlertTitle>
        <AlertDescription>
          Your operation was completed successfully.
        </AlertDescription>
      </Alert>,
    );

    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByRole('heading', { level: 5 })).toHaveTextContent(
      'Success!',
    );
    expect(
      screen.getByText('Your operation was completed successfully.'),
    ).toBeInTheDocument();
  });

  it('works with mixed content', () => {
    render(
      <Alert variant="warning">
        <AlertTitle>Warning</AlertTitle>
        <AlertDescription>This is a warning message.</AlertDescription>
        <div>Additional content</div>
      </Alert>,
    );

    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByText('Warning')).toBeInTheDocument();
    expect(screen.getByText('This is a warning message.')).toBeInTheDocument();
    expect(screen.getByText('Additional content')).toBeInTheDocument();
  });
});
