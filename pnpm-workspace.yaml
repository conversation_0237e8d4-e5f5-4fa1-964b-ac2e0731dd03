packages:
  - 'apps/*'
  - 'apps/backend/*'
  - 'apps/frontend/*'
  - 'packages/*'
  - 'packages/backend/*'
  - 'packages/common/*'
  - 'packages/config/*'
  - 'packages/frontend/*'
  - 'packages/node/*'

catalogs:
  build:
    "turbo": "^2.5.4"
    "tsup": "^8.5.0"

  node-24:
    "@types/node": "^22"

  next-15:
    "@tailwindcss/cli": "^4.1.8"
    "@tailwindcss/postcss": "^4.1.8"
    "@types/react": "^18.3.0"
    "@types/react-dom": "^18.3.0"
    "autoprefixer": "^10.0.1"
    "next": "15.3.3"
    "postcss": "^8"
    "tailwindcss": "^4.1.8"
    "react": "18.3.0"
    "react-dom": "18.3.0"

  typescript-5:
    "typescript": "^5.6.0"
    "@types/react": "^18.3.0"
    "@types/react-dom": "^18.3.0"

  testing:
    "jest": "^29.7.0"
    "@types/jest": "^29.5.14"
    "@testing-library/jest-dom": "^6.6.3"
    "@testing-library/react": "^16.3.0"
    "@testing-library/user-event": "^14.6.1"
    "ts-jest": "^29.2.5"

  react-18:
    "react": "^18.3.0"
    "react-dom": "^18.3.0"

  eslint:
    "@eslint/eslintrc": "^3.3.1"
    "@eslint/js": "^9.28.0"
    "@typescript-eslint/eslint-plugin": "^8.33.1"
    "@typescript-eslint/parser": "^8.33.1"
    "eslint": "^9.0.0"
    "eslint-config-prettier": "^9.1.0"
    "eslint-config-next": "^15.3.3"