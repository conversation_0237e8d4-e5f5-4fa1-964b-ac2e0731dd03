{"name": "@telesoft/eslint-config", "version": "0.0.0", "private": true, "type": "module", "main": "./index.mjs", "exports": {".": "./index.mjs", "./react-internal": "./react-internal.mjs", "./next": "./next.mjs"}, "scripts": {"build": "pnpm run validate", "validate": "pnpm run validate:configs && pnpm run validate:syntax", "validate:configs": "node scripts/validate-configs.mjs", "validate:syntax": "eslint --config ./index.mjs *.mjs --no-error-on-unmatched-pattern --no-warn-ignored", "clean": "echo 'No build artifacts to clean'", "dev": "echo 'Nothing to build'", "lint": "eslint *.mjs --no-warn-ignored", "format": "prettier --write . --ignore-path ../../../.prettierignore"}, "files": ["index.mjs", "react-internal.mjs", "next.mjs", "README.md", "scripts/"], "dependencies": {"@eslint/js": "^9.28.0", "@next/eslint-plugin-next": "^15.3.3", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.0.0"}, "devDependencies": {"eslint": "catalog:eslint", "typescript": "catalog:typescript-5"}, "peerDependencies": {"eslint": "^9.0.0", "typescript": ">=4.0.0"}}