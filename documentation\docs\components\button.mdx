---
sidebar_position: 2
---

# Button

The Button component is used for user actions and interactions throughout the application. It supports multiple variants, sizes, and states.

import { Button } from '@telesoft/ui';
import { ComponentShowcase } from '../../src/components/ComponentShowcase';
import { UIThemeProvider } from '../../src/components/UIThemeProvider';

<UIThemeProvider>

## Variants

The Button component supports several visual variants to match different use cases and contexts.

<ComponentShowcase
  title="Primary Variant"
  description="The default variant for primary actions and call-to-action buttons."
  component={<Button variant="primary">Primary Button</Button>}
  code={`<Button variant="primary">Primary Button</Button>`}
/>

<ComponentShowcase
  title="Secondary Variant"
  description="Used for secondary actions that are less prominent than primary actions."
  component={<Button variant="secondary">Secondary Button</Button>}
  code={`<Button variant="secondary">Secondary Button</Button>`}
/>

<ComponentShowcase
  title="Danger Variant"
  description="For destructive actions like delete, remove, or cancel operations."
  component={<Button variant="danger">Delete Item</Button>}
  code={`<Button variant="danger">Delete Item</Button>`}
/>

<ComponentShowcase
  title="Success Variant"
  description="For positive actions like save, confirm, or success states."
  component={<Button variant="success">Save Changes</Button>}
  code={`<Button variant="success">Save Changes</Button>`}
/>

<ComponentShowcase
  title="Warning Variant"
  description="For cautionary actions that require user attention."
  component={<Button variant="warning">Warning Action</Button>}
  code={`<Button variant="warning">Warning Action</Button>`}
/>

<ComponentShowcase
  title="Ghost Variant"
  description="Minimal styling for subtle actions that shouldn't dominate the interface."
  component={<Button variant="ghost">Ghost Button</Button>}
  code={`<Button variant="ghost">Ghost Button</Button>`}
/>

<ComponentShowcase
  title="Outline Variant"
  description="Outlined buttons for secondary actions with more visual weight than ghost."
  component={<Button variant="outline">Outline Button</Button>}
  code={`<Button variant="outline">Outline Button</Button>`}
/>

## Sizes

Buttons come in three sizes to fit different contexts and layouts.

<ComponentShowcase
  title="Button Sizes"
  description="Small, medium (default), and large button sizes."
  component={
    <>
      <Button size="sm" variant="primary">
        Small
      </Button>
      <Button size="md" variant="primary">
        Medium
      </Button>
      <Button size="lg" variant="primary">
        Large
      </Button>
    </>
  }
  code={`<Button size="sm" variant="primary">Small</Button>
<Button size="md" variant="primary">Medium</Button>
<Button size="lg" variant="primary">Large</Button>`}
/>

## States

Buttons support different interaction states for better user feedback.

<ComponentShowcase
  title="Loading State"
  description="Shows a loading indicator when the button action is in progress."
  component={
    <Button variant="primary" loading>
      Loading...
    </Button>
  }
  code={`<Button variant="primary" loading>Loading...</Button>`}
/>

<ComponentShowcase
  title="Disabled State"
  description="Prevents interaction and shows the button as inactive."
  component={
    <Button variant="primary" disabled>
      Disabled Button
    </Button>
  }
  code={`<Button variant="primary" disabled>Disabled Button</Button>`}
/>

## Complex Examples

<ComponentShowcase
  title="Form Actions"
  description="Common button combinations used in forms and dialogs."
  component={
    <>
      <Button variant="outline">Cancel</Button>
      <Button variant="primary">Save Changes</Button>
    </>
  }
  code={`<Button variant="outline">Cancel</Button>
<Button variant="primary">Save Changes</Button>`}
/>

<ComponentShowcase
  title="Destructive Action with Confirmation"
  description="Typical pattern for dangerous operations."
  component={
    <>
      <Button variant="ghost">Cancel</Button>
      <Button variant="danger">Delete Forever</Button>
    </>
  }
  code={`<Button variant="ghost">Cancel</Button>
<Button variant="danger">Delete Forever</Button>`}
/>

## Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'variant',
      type: "'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'ghost' | 'outline'",
      default: "'primary'",
      description: 'Visual style variant of the button',
    },
    {
      name: 'size',
      type: "'sm' | 'md' | 'lg'",
      default: "'md'",
      description: 'Size of the button',
    },
    {
      name: 'loading',
      type: 'boolean',
      default: 'false',
      description: 'Shows loading state with spinner',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Disables the button and prevents interaction',
    },
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description: 'Button content (text, icons, etc.)',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
    {
      name: 'onClick',
      type: '(event: MouseEvent) => void',
      description: 'Click event handler',
    },
  ]}
/>

## Accessibility

The Button component includes built-in accessibility features:

- **Keyboard navigation**: Fully accessible via Tab and Enter/Space keys
- **Screen reader support**: Proper semantic HTML button element
- **Focus management**: Clear focus indicators with custom focus ring
- **Loading state**: Appropriate ARIA attributes when loading
- **Disabled state**: Proper disabled attribute and visual feedback

## Best Practices

### Do ✅

- Use primary buttons for the main action on a page
- Use secondary buttons for less important actions
- Use danger buttons only for destructive actions
- Keep button text concise and action-oriented
- Use loading states for async operations

### Don't ❌

- Don't use multiple primary buttons in the same context
- Don't use danger buttons for non-destructive actions
- Don't make button text too long or verbose
- Don't forget to handle loading and error states
- Don't stack too many buttons in a single row

</UIThemeProvider>
