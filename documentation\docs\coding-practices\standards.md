# Coding Standards

This document outlines the specific coding standards and conventions used throughout the Telesoft UI project. These standards ensure consistency, readability, and maintainability across all packages.

## 📝 General Principles

### 1. Code Clarity Over Cleverness

- Write code that is easy to read and understand
- Use descriptive variable and function names
- Prefer explicit over implicit behavior
- Comment complex logic and business rules

### 2. Consistency

- Follow established patterns within the codebase
- Use shared configuration packages (`@telesoft/eslint-config`, etc.)
- Maintain consistent file structure across packages

### 3. Type Safety

- Use TypeScript throughout the stack
- Avoid `any` types whenever possible
- Define clear interfaces for all public APIs
- Leverage TypeScript's strict mode features

## 🎯 TypeScript Standards

### Interface Definitions

```typescript
// ✅ Good: Clear, documented interface
interface UserProfile {
  /** Unique identifier for the user */
  id: string;

  /** User's full name */
  name: string;

  /** User's email address */
  email: string;

  /** User's role in the system */
  role: 'admin' | 'user' | 'viewer';

  /** Optional profile image URL */
  avatarUrl?: string;

  /** Account creation timestamp */
  createdAt: Date;
}

// ❌ Bad: Unclear, undocumented
interface User {
  id: any;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  created: string;
}
```

### Function Signatures

```typescript
// ✅ Good: Clear parameters and return type
async function fetchUserProfile(
  userId: string,
  options?: {
    includeAvatar?: boolean;
    timeout?: number;
  },
): Promise<UserProfile | null> {
  // Implementation
}

// ❌ Bad: Unclear parameters
async function getUser(id: any, opts?: any): Promise<any> {
  // Implementation
}
```

### Generic Types

```typescript
// ✅ Good: Bounded generics with clear constraints
interface ApiResponse<T = unknown> {
  data: T;
  status: 'success' | 'error';
  message?: string;
}

interface Repository<T extends { id: string }> {
  findById(id: string): Promise<T | null>;
  save(entity: T): Promise<T>;
  delete(id: string): Promise<void>;
}

// ❌ Bad: Unbounded generics
interface Response<T> {
  data: T;
  status: string;
}
```

## ⚛️ React Component Standards

### Component Structure

```typescript
// ✅ Good: Well-structured component
import React, { forwardRef, useCallback } from 'react';
import { cn } from '../utils/classNames';

export interface AlertProps {
  /** The content to display in the alert */
  children: React.ReactNode;

  /** Visual variant of the alert */
  variant?: 'info' | 'success' | 'warning' | 'error';

  /** Whether the alert can be dismissed */
  dismissible?: boolean;

  /** Callback fired when alert is dismissed */
  onDismiss?: () => void;

  /** Additional CSS classes */
  className?: string;
}

export const Alert = forwardRef<HTMLDivElement, AlertProps>(
  ({
    children,
    variant = 'info',
    dismissible = false,
    onDismiss,
    className,
    ...props
  }, ref) => {
    const handleDismiss = useCallback(() => {
      onDismiss?.();
    }, [onDismiss]);

    return (
      <div
        ref={ref}
        role="alert"
        className={cn(
          'rounded-lg border p-4',
          {
            'bg-blue-50 border-blue-200 text-blue-800': variant === 'info',
            'bg-green-50 border-green-200 text-green-800': variant === 'success',
            'bg-yellow-50 border-yellow-200 text-yellow-800': variant === 'warning',
            'bg-red-50 border-red-200 text-red-800': variant === 'error',
          },
          className
        )}
        {...props}
      >
        <div className="flex justify-between items-start">
          <div className="flex-1">{children}</div>
          {dismissible && (
            <button
              type="button"
              onClick={handleDismiss}
              className="ml-2 text-current hover:opacity-70"
              aria-label="Dismiss alert"
            >
              ✕
            </button>
          )}
        </div>
      </div>
    );
  }
);

Alert.displayName = 'Alert';
```

### Hooks Standards

```typescript
// ✅ Good: Custom hook with clear return type
import { useState, useCallback, useEffect } from 'react';

interface UseApiOptions {
  enabled?: boolean;
  refetchInterval?: number;
}

interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => void;
}

export function useApi<T>(
  url: string,
  options: UseApiOptions = {},
): UseApiReturn<T> {
  const { enabled = true, refetchInterval } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [url, enabled]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (!refetchInterval) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval]);

  return { data, loading, error, refetch: fetchData };
}
```

## 🎨 Styling Standards

### Tailwind CSS Usage

```typescript
// ✅ Good: Organized class names with conditional logic
const buttonClasses = cn(
  // Base styles
  'inline-flex items-center justify-center rounded-md font-medium transition-colors',
  'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
  'disabled:pointer-events-none disabled:opacity-50',

  // Size variants
  {
    'h-8 px-3 text-sm': size === 'sm',
    'h-10 px-4 text-base': size === 'md',
    'h-12 px-6 text-lg': size === 'lg',
  },

  // Color variants
  {
    'bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500':
      variant === 'primary',
    'bg-secondary-600 text-white hover:bg-secondary-700 focus-visible:ring-secondary-500':
      variant === 'secondary',
    'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus-visible:ring-gray-500':
      variant === 'outline',
  },

  className,
);

// ❌ Bad: Unorganized, hard to read
const classes = cn(
  'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  size === 'sm'
    ? 'h-8 px-3 text-sm'
    : size === 'md'
      ? 'h-10 px-4 text-base'
      : 'h-12 px-6 text-lg',
  variant === 'primary'
    ? 'bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500'
    : 'bg-secondary-600 text-white hover:bg-secondary-700 focus-visible:ring-secondary-500',
  className,
);
```

## 📁 File Organization

### Package Structure

```
src/
├── components/
│   ├── Button/
│   │   ├── Button.tsx        # Main component
│   │   ├── Button.test.tsx   # Tests
│   │   ├── Button.stories.tsx # Storybook stories (if applicable)
│   │   └── index.ts          # Re-exports
│   └── index.ts              # Component exports
├── hooks/
│   ├── useApi.ts
│   ├── useApi.test.ts
│   └── index.ts
├── types/
│   ├── api.ts
│   ├── components.ts
│   └── index.ts
├── utils/
│   ├── classNames.ts
│   ├── classNames.test.ts
│   ├── dateFormatters.ts
│   └── index.ts
└── index.ts                  # Main package export
```

### Naming Conventions

```typescript
// ✅ Good naming conventions

// Components: PascalCase
export const UserProfile = () => {};
export const DataTable = () => {};

// Hooks: camelCase starting with 'use'
export const useLocalStorage = () => {};
export const useApiData = () => {};

// Utilities: camelCase
export const formatCurrency = () => {};
export const validateEmail = () => {};

// Constants: SCREAMING_SNAKE_CASE
export const API_BASE_URL = 'https://api.example.com';
export const DEFAULT_TIMEOUT = 5000;

// Types/Interfaces: PascalCase
export interface ApiResponse<T> {}
export type UserRole = 'admin' | 'user';

// Enums: PascalCase
export enum OrderStatus {
  Pending = 'pending',
  Shipped = 'shipped',
  Delivered = 'delivered',
}
```

## 🔄 Import/Export Standards

### Import Organization

```typescript
// ✅ Good: Organized imports
// 1. Node modules
import React, { useState, useEffect, useCallback } from 'react';
import { clsx } from 'clsx';

// 2. Internal packages (workspace)
import { Button } from '@telesoft/ui';
import { formatDate } from '@telesoft/utils';

// 3. Relative imports (closest to farthest)
import { UserCard } from './UserCard';
import { useUserData } from '../hooks/useUserData';
import { ApiResponse } from '../../types/api';

// ❌ Bad: Unorganized imports
import { UserCard } from './UserCard';
import React, { useState } from 'react';
import { ApiResponse } from '../../types/api';
import { Button } from '@telesoft/ui';
import { useUserData } from '../hooks/useUserData';
```

### Export Patterns

```typescript
// ✅ Good: Named exports with clear re-exports
// components/Button/Button.tsx
export interface ButtonProps {
  // ...
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>();
// Implementation

// components/Button/index.ts
export { Button, type ButtonProps } from './Button';

// components/index.ts
export { Button, type ButtonProps } from './Button';
export { Input, type InputProps } from './Input';

// ❌ Bad: Default exports without clear names
export default forwardRef<HTMLButtonElement, Props>();
// Implementation
```

## 🛡️ Error Handling

### Function Error Handling

```typescript
// ✅ Good: Explicit error handling
async function fetchUserData(userId: string): Promise<UserProfile | null> {
  try {
    const response = await fetch(`/api/users/${userId}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch user: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching user data:', error);
    return null;
  }
}

// ❌ Bad: Unhandled errors
async function getUser(id: string) {
  const response = await fetch(`/api/users/${id}`);
  return response.json();
}
```

### React Error Boundaries

```typescript
// ✅ Good: Proper error boundary
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-200 rounded-lg bg-red-50">
          <h2 className="text-lg font-semibold text-red-800">Something went wrong</h2>
          <p className="text-red-600">
            {this.state.error?.message || 'An unexpected error occurred'}
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## 📊 Performance Guidelines

### Memoization

```typescript
// ✅ Good: Strategic memoization
const ExpensiveComponent = memo<Props>(({ data, onSelect }) => {
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      calculated: item.value * 1.2
    }));
  }, [data]);

  const handleSelect = useCallback((id: string) => {
    onSelect(id);
  }, [onSelect]);

  return (
    <div>
      {processedData.map(item => (
        <Item key={item.id} data={item} onSelect={handleSelect} />
      ))}
    </div>
  );
});

// ❌ Bad: Over-memoization
const SimpleComponent = memo(() => {
  const value = useMemo(() => 'static value', []);
  const handler = useCallback(() => {}, []);

  return <div>{value}</div>;
});
```

## 🔍 Code Review Checklist

Before submitting code for review, ensure:

### ✅ Type Safety

- [ ] All functions have proper TypeScript types
- [ ] No `any` types without justification
- [ ] Interfaces are properly documented
- [ ] Generic types are properly constrained

### ✅ React Standards

- [ ] Components use `forwardRef` when appropriate
- [ ] Props are properly typed with interfaces
- [ ] Event handlers are properly typed
- [ ] Components have `displayName` set

### ✅ Performance

- [ ] No unnecessary re-renders
- [ ] Expensive calculations are memoized
- [ ] Event handlers are stable with `useCallback`
- [ ] Lists use proper `key` props

### ✅ Accessibility

- [ ] Proper ARIA attributes
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility
- [ ] Color contrast compliance

### ✅ Testing

- [ ] Unit tests cover main functionality
- [ ] Error cases are tested
- [ ] Accessibility is tested
- [ ] Integration tests for complex interactions

---

Continue to our [Unit Testing Guide](./testing.md) to learn about testing standards and best practices.
