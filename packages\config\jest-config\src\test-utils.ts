import { render } from '@testing-library/react';
import type { RenderOptions } from '@testing-library/react';
import React, { type ReactElement } from 'react';
import '@testing-library/jest-dom';

// Custom render function that can be extended with providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
): ReturnType<typeof render> => render(ui, { ...options });

// Helper function to test if an element has specific CSS classes
export const expectToHaveClasses = (
  element: HTMLElement,
  classes: string[],
) => {
  classes.forEach((className) => {
    expect(element).toHaveClass(className);
  });
};

// Helper function to test if an element doesn't have specific CSS classes
export const expectNotToHaveClasses = (
  element: HTMLElement,
  classes: string[],
) => {
  classes.forEach((className) => {
    expect(element).not.toHaveClass(className);
  });
};

// Helper to create mock event handlers
export const createMockHandlers = () => ({
  onClick: jest.fn(),
  onFocus: jest.fn(),
  onBlur: jest.fn(),
  onChange: jest.fn(),
  onSubmit: jest.fn(),
  onMouseEnter: jest.fn(),
  onMouseLeave: jest.fn(),
});

// Helper to test component refs
export const testComponentRef = <T extends HTMLElement>(
  component: ReactElement,
  expectedInstanceType: new (...args: any[]) => T,
  getElementFromScreen: () => T,
) => {
  const ref = React.createRef<T>();
  render(React.cloneElement(component, { ref }));

  expect(ref.current).toBeInstanceOf(expectedInstanceType);
  expect(ref.current).toBe(getElementFromScreen());
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';

// Use custom render as default
export { customRender as render };
