import React from 'react';
import { formatTimestamp } from '@telesoft/utils';

export interface ConnectionStatusProps {
  isConnected: boolean;
  lastUpdate?: string | null;
  label?: string;
  showLastUpdate?: boolean;
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  lastUpdate,
  label = 'Connection Status',
  showLastUpdate = true,
  className = '',
}) => {
  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <div
        className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}
      ></div>
      <div>
        <div className="text-lg font-bold text-text-primary">
          {isConnected ? 'Live' : 'Disconnected'}
        </div>
        <div className="text-sm text-text-secondary">{label}</div>
      </div>
      {showLastUpdate && lastUpdate && (
        <div className="mt-2 pt-2 border-t border-border-primary/20">
          <span className="text-xs text-text-subtle">
            Last update:{' '}
            {formatTimestamp(new Date(lastUpdate).getTime(), {
              format: 'datetime',
              includeSeconds: true,
              use24Hour: true,
            })}
          </span>
        </div>
      )}
    </div>
  );
};
