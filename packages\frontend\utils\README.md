# @telesoft/utils

Common utility functions for date formatting, chart data transformation, and styling.

## Features

- **Date/Time Utilities**: Consistent timestamp formatting across the application
- **Chart Utilities**: Data transformation and color management for D3 charts
- **Style Utilities**: Common CSS class combinations and styling helpers

## Installation

```bash
pnpm add @telesoft/utils
```

## Usage

### Date Utilities

```typescript
import { formatTimestamp, formatRelativeTime } from '@telesoft/utils';

// Format timestamp with options
const formatted = formatTimestamp(Date.now(), {
  format: 'datetime',
  includeSeconds: true,
  use24Hour: true,
});

// Relative time formatting
const relative = formatRelativeTime(new Date());
// "2 minutes ago"
```

### Chart Utilities

```typescript
import {
  getChartColors,
  transformForChart,
  transformDeploymentData,
} from '@telesoft/utils';

// Get color palette for charts
const colors = getChartColors(5, 'cybersecurity');

// Transform data for charts
const chartData = transformForChart(rawData, {
  maxDataPoints: 50,
  sortByTime: true,
  groupBy: 'namespace',
});

// Transform deployment-specific data
const deploymentCharts = transformDeploymentData(deployments);
```

### Style Utilities

```typescript
import { STYLE_PRESETS, classNames, getToggleClasses } from '@telesoft/utils';

// Use predefined style combinations
<Card className={STYLE_PRESETS.cardGlass}>

// Combine classes conditionally
const buttonClass = classNames(
  'btn',
  isActive && 'btn-active',
  disabled && 'btn-disabled'
);

// Get toggle switch classes
const { container, thumb } = getToggleClasses(isActive);
```

## API Reference

### Date Utilities

- `formatTimestamp(timestamp, options)` - Format timestamp to human-readable string
- `formatRelativeTime(date, baseDate)` - Format as relative time ("2 hours ago")
- `formatTimestampLegacy(timestamp)` - Legacy format for compatibility
- `formatTimestampTime(timestamp)` - Time-only format (HH:MM:SS)

### Chart Utilities

- `getChartColors(count, palette)` - Get color array for charts
- `getStatusColor(status)` - Get color for status/severity levels
- `transformForChart(data, options)` - Transform data for chart consumption
- `transformDeploymentData(deployments)` - Transform deployment data specifically

### Style Utilities

- `STYLE_PRESETS` - Predefined CSS class combinations
- `classNames(...classes)` - Combine CSS classes conditionally
- `getToggleClasses(isActive)` - Get toggle switch classes
- `getStatusBadgeVariant(status)` - Get badge variant for status
- `getResponsiveGrid(cols)` - Get responsive grid classes
