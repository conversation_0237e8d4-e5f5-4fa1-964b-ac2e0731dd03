'use client';

import { <PERSON>, CardHeader, CardContent } from '@telesoft/ui';
import { ThreatFlowDiagram } from '@telesoft/d3';
import { useState, useMemo, useEffect } from 'react';
import { classNames, STYLE_PRESETS } from '@telesoft/utils';

export default function ThreatHunting() {
  const [sourceAActive, setSourceAActive] = useState(false);
  const [sourceBActive, setSourceBActive] = useState(false);
  const [processedActive, setProcessedActive] = useState(false);

  // Dynamic metrics state
  const [securityEvents, setSecurityEvents] = useState(1247);
  const [threatDetections, setThreatDetections] = useState(23);
  const [networkAlerts, setNetworkAlerts] = useState(89);
  const [processedThreats, setProcessedThreats] = useState(156);
  const [_falsePositives, setFalsePositives] = useState(12);

  // Additional dynamic state for status cards
  const [activeHunts, setActiveHunts] = useState({
    running: 7,
    queued: 3,
    completed: 142,
  });
  const [detectionRate, setDetectionRate] = useState(94.7);
  const [systemHealth, setSystemHealth] = useState({
    processingLoad: 67,
    memoryUsage: 42,
  });

  // Continuous animation cycle - keep everything active with minor metric updates
  useEffect(() => {
    // Set everything to active immediately for constant "analyzing" state
    setSourceAActive(true);
    setSourceBActive(true);
    setProcessedActive(true);

    // Continuous minor metric updates every 2-4 seconds
    const updateMetrics = () => {
      // Small incremental changes to simulate ongoing activity
      setSecurityEvents((prev) => prev + Math.floor(Math.random() * 3) + 1);
      setThreatDetections((prev) => prev + Math.floor(Math.random() * 2));
      setNetworkAlerts((prev) => prev + Math.floor(Math.random() * 2) + 1);
      setProcessedThreats((prev) => prev + Math.floor(Math.random() * 3) + 1);
      setFalsePositives((prev) => prev + Math.floor(Math.random() * 1));

      // Update status card metrics with minor variations
      setActiveHunts((prev) => ({
        running: Math.max(
          5,
          Math.min(
            10,
            prev.running +
              (Math.random() > 0.7 ? (Math.random() > 0.5 ? 1 : -1) : 0),
          ),
        ),
        queued: Math.max(
          1,
          Math.min(
            8,
            prev.queued +
              (Math.random() > 0.8 ? (Math.random() > 0.5 ? 1 : -1) : 0),
          ),
        ),
        completed: prev.completed + (Math.random() > 0.9 ? 1 : 0),
      }));

      setDetectionRate((prev) =>
        Math.max(92, Math.min(98, prev + (Math.random() - 0.5) * 0.2)),
      );

      setSystemHealth((prev) => ({
        processingLoad: Math.max(
          45,
          Math.min(85, prev.processingLoad + (Math.random() - 0.5) * 3),
        ),
        memoryUsage: Math.max(
          35,
          Math.min(70, prev.memoryUsage + (Math.random() - 0.5) * 2),
        ),
      }));
    };

    // Initial update
    updateMetrics();

    // Set up continuous metric updates
    const metricsInterval = setInterval(
      () => {
        updateMetrics();
      },
      2000 + Math.random() * 2000,
    ); // Update every 2-4 seconds

    return () => {
      clearInterval(metricsInterval);
    };
  }, []);

  const ThreatSource = ({
    title,
    description,
    metrics,
    active,
    position = 'left',
  }: {
    title: string;
    description: string;
    metrics: {
      label: string;
      value: string | number;
      status?: 'good' | 'warning' | 'danger';
    }[];
    active: boolean;
    position?: 'left' | 'right';
  }) => (
    <div className={`relative ${position === 'left' ? 'mr-8' : 'ml-8'}`}>
      <Card
        className={`w-80 transition-all duration-500 ${
          active ? 'cyber-glow ring-2 ring-primary-400' : 'hover:elevation-high'
        }`}
      >
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div
              className={`w-4 h-4 rounded-full transition-all duration-300 ${
                active ? 'bg-primary-400 animate-pulse' : 'bg-border-secondary'
              }`}
            />
            <h3 className="text-lg font-semibold text-text-primary">{title}</h3>
          </div>
          <p className="text-sm text-text-secondary mt-2">{description}</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {metrics.map((metric, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-sm text-text-muted">{metric.label}</span>
                <span
                  className={`text-sm font-mono ${
                    metric.status === 'danger'
                      ? 'text-cyber-danger-400'
                      : metric.status === 'warning'
                        ? 'text-cyber-warning-400'
                        : metric.status === 'good'
                          ? 'text-cyber-matrix-400'
                          : 'text-text-primary'
                  }`}
                >
                  {metric.value}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Data flow pipe */}
      <div
        className={`absolute top-1/2 ${position === 'left' ? '-right-8' : '-left-8'} 
        w-8 h-2 bg-border-secondary transition-all duration-500 ${
          active ? 'bg-primary-400 shadow-cyber' : ''
        }`}
      >
        {active && (
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-primary-600 to-primary-400 animate-pulse" />
        )}
      </div>
    </div>
  );

  const ProcessingCenter = ({ active }: { active: boolean }) => (
    <div className="relative mx-8">
      <Card
        className={`w-96 transition-all duration-500 ${
          active
            ? 'cyber-glow-lg ring-2 ring-cyber-matrix-400 scale-105'
            : 'hover:elevation-high'
        }`}
      >
        <CardHeader>
          <div className="flex items-center justify-center space-x-3">
            <div
              className={`w-6 h-6 rounded-full transition-all duration-300 ${
                active
                  ? 'bg-cyber-matrix-400 animate-pulse'
                  : 'bg-border-secondary'
              }`}
            />
            <h3 className="text-xl font-bold text-text-primary">
              Threat Correlation Engine
            </h3>
          </div>
          <p className="text-sm text-text-secondary text-center mt-2">
            Advanced analytics combining multiple intelligence sources
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Processing indicators */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div
                  className={`text-2xl font-mono mb-1 transition-colors duration-300 ${
                    active ? 'text-primary-400' : 'text-text-secondary'
                  }`}
                >
                  {active ? `${(97.5 + Math.random() * 2).toFixed(1)}%` : '0%'}
                </div>
                <div className="text-xs text-text-muted">Correlation Rate</div>
              </div>
              <div className="text-center">
                <div
                  className={`text-2xl font-mono mb-1 transition-colors duration-300 ${
                    active ? 'text-cyber-matrix-400' : 'text-text-secondary'
                  }`}
                >
                  {active ? Math.floor(150 + Math.random() * 15) : '0'}
                </div>
                <div className="text-xs text-text-muted">Active Rules</div>
              </div>
            </div>

            {/* Processing visualization */}
            <div className="relative">
              <div className="flex justify-center items-center h-20">
                <div
                  className={`w-16 h-16 rounded-full border-4 transition-all duration-500 ${
                    active
                      ? 'border-primary-400 border-t-cyber-matrix-400 animate-spin'
                      : 'border-border-secondary'
                  }`}
                />
              </div>
              {active && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-xs text-primary-400 font-mono animate-pulse">
                    PROCESSING
                  </div>
                </div>
              )}
            </div>

            {/* Output indicators */}
            <div className="pt-4 border-t border-border-primary">
              <div className="grid grid-cols-3 gap-2 text-center">
                <div>
                  <div
                    className={`text-lg font-mono mb-1 transition-colors duration-300 ${
                      active ? 'text-cyber-danger-400' : 'text-text-secondary'
                    }`}
                  >
                    {active ? Math.floor(processedThreats * 0.08) : '0'}
                  </div>
                  <div className="text-xs text-text-muted">High Risk</div>
                </div>
                <div>
                  <div
                    className={`text-lg font-mono mb-1 transition-colors duration-300 ${
                      active ? 'text-cyber-warning-400' : 'text-text-secondary'
                    }`}
                  >
                    {active ? Math.floor(processedThreats * 0.18) : '0'}
                  </div>
                  <div className="text-xs text-text-muted">Medium Risk</div>
                </div>
                <div>
                  <div
                    className={`text-lg font-mono mb-1 transition-colors duration-300 ${
                      active ? 'text-cyber-matrix-400' : 'text-text-secondary'
                    }`}
                  >
                    {active ? Math.floor(processedThreats * 0.54) : '0'}
                  </div>
                  <div className="text-xs text-text-muted">Low Risk</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const sourceAMetrics = [
    {
      label: 'Events/sec',
      value: securityEvents.toLocaleString(),
      status: 'good' as const,
    },
    { label: 'Data Quality', value: '99.2%', status: 'good' as const },
    {
      label: 'Latency',
      value: `${Math.floor(Math.random() * 10) + 20}ms`,
      status: 'good' as const,
    },
    {
      label: 'Alerts',
      value: threatDetections,
      status: threatDetections > 25 ? ('warning' as const) : ('good' as const),
    },
  ];

  const sourceBMetrics = [
    {
      label: 'IOCs Processed',
      value: networkAlerts.toLocaleString(),
      status: 'good' as const,
    },
    { label: 'Feed Health', value: '97.8%', status: 'good' as const },
    { label: 'Update Rate', value: '15min', status: 'good' as const },
    {
      label: 'Critical IOCs',
      value: Math.floor(networkAlerts / 12),
      status: 'danger' as const,
    },
  ];

  // D3 Flow Diagram Data - simplified to 3 source boxes
  const flowNodes = useMemo(
    () => [
      // Simplified Source Groups
      {
        id: 'threat-intelligence',
        label: 'Threat Intelligence',
        description: 'CTX, MISP & IOC Feeds',
        type: 'source' as const,
        active: sourceAActive,
      },
      {
        id: 'news',
        label: 'News',
        description: 'Threat Summaries & Intelligence',
        type: 'source' as const,
        active: sourceBActive,
      },
      {
        id: 'playbook',
        label: 'Playbook',
        description: 'Security Policies & Rules',
        type: 'source' as const,
        active: sourceBActive,
      },
      // Central Processor
      {
        id: 'processor',
        label: 'Correlation Engine',
        description: 'Advanced Analytics',
        type: 'processor' as const,
        metrics: [
          {
            label: 'Correlation Rate',
            value: processedActive
              ? `${(97.5 + Math.random() * 2).toFixed(1)}%`
              : '0%',
            status: 'good' as const,
          },
          {
            label: 'Active Rules',
            value: processedActive
              ? Math.floor(150 + Math.random() * 15).toString()
              : '0',
            status: 'good' as const,
          },
        ],
        active: processedActive,
      },
      // Outputs
      {
        id: 'output-conclusive',
        label: 'Conclusive',
        description: processedActive
          ? Math.floor(8 + Math.random() * 8).toString()
          : '0',
        type: 'output' as const,
        metrics: [],
        active: processedActive,
      },
      {
        id: 'output-inconclusive',
        label: 'Inconclusive',
        description: processedActive
          ? Math.floor(520 + Math.random() * 80).toString()
          : '0',
        type: 'output' as const,
        metrics: [],
        active: processedActive,
      },
    ],
    [sourceAActive, sourceBActive, processedActive],
  );

  const flowLinks = useMemo(
    () => [
      // Simplified links from 3 sources to processor
      {
        source: 'threat-intelligence',
        target: 'processor',
        active: sourceAActive,
      },
      { source: 'news', target: 'processor', active: sourceBActive },
      { source: 'playbook', target: 'processor', active: sourceBActive },
      // Processor to outputs
      {
        source: 'processor',
        target: 'output-conclusive',
        active: processedActive,
      },
      {
        source: 'processor',
        target: 'output-inconclusive',
        active: processedActive,
      },
    ],
    [sourceAActive, sourceBActive, processedActive],
  );

  return (
    <div className={classNames(STYLE_PRESETS.pageContainer)}>
      <div className={classNames(STYLE_PRESETS.contentContainer)}>
        {/* Main Dashboard Layout */}
        <div className="relative">
          {/* Background grid pattern */}
          <div className="absolute inset-0 cyber-grid-bg opacity-30 pointer-events-none" />

          <div className="relative z-10 flex items-center justify-center py-12">
            {/* Left Source - Security Events */}
            <ThreatSource
              title="Security Events"
              description="Real-time SIEM events and network telemetry"
              metrics={sourceAMetrics}
              active={sourceAActive}
              position="left"
            />

            {/* Processing Center */}
            <ProcessingCenter active={processedActive} />

            {/* Right Source - Threat Intelligence */}
            <ThreatSource
              title="Threat Intelligence"
              description="External IOCs and threat actor information"
              metrics={sourceBMetrics}
              active={sourceBActive}
              position="right"
            />
          </div>

          {/* Merger visualization arrows */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
            {/* Left arrow */}
            <div
              className={`absolute -left-48 top-0 w-16 h-0.5 transition-all duration-500 ${
                sourceAActive
                  ? 'bg-primary-400 shadow-cyber'
                  : 'bg-border-secondary'
              }`}
            >
              <div
                className={`absolute -right-2 -top-1 w-4 h-4 border-r-2 border-t-2 transform rotate-45 transition-all duration-500 ${
                  sourceAActive
                    ? 'border-primary-400'
                    : 'border-border-secondary'
                }`}
              />
            </div>

            {/* Right arrow */}
            <div
              className={`absolute -right-48 top-0 w-16 h-0.5 transition-all duration-500 ${
                sourceBActive
                  ? 'bg-primary-400 shadow-cyber'
                  : 'bg-border-secondary'
              }`}
            >
              <div
                className={`absolute -left-2 -top-1 w-4 h-4 border-l-2 border-t-2 transform -rotate-45 transition-all duration-500 ${
                  sourceBActive
                    ? 'border-primary-400'
                    : 'border-border-secondary'
                }`}
              />
            </div>
          </div>
        </div>

        {/* D3 Interactive Flow Diagram */}
        <div className="mt-0">
          <CardContent>
            <div className="bg-background-primary rounded-lg p-6 border border-border-primary flex items-center justify-center min-h-[450px]">
              <ThreatFlowDiagram
                nodes={flowNodes}
                links={flowLinks}
                width={800}
                height={400}
                onNodeClick={(node) => {
                  console.log('Node clicked:', node);
                  // Handle node interactions
                }}
                className="mx-auto"
              />
            </div>
          </CardContent>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Active Hunts
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Running</span>
                  <span className="text-sm font-mono text-cyber-matrix-400">
                    {activeHunts.running}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Queued</span>
                  <span className="text-sm font-mono text-cyber-warning-400">
                    {activeHunts.queued}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-text-muted">Completed</span>
                  <span className="text-sm font-mono text-text-secondary">
                    {activeHunts.completed}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                Detection Rate
              </h3>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-mono text-primary-400 mb-2">
                  {detectionRate.toFixed(1)}%
                </div>
                <div className="text-sm text-text-muted">Last 24 hours</div>
                <div className="mt-4 h-2 bg-background-tertiary rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-primary-500 to-cyber-matrix-500 rounded-full transition-all duration-1000"
                    style={{ width: `${detectionRate}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-text-primary">
                System Health
              </h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Sources Online
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-cyber-matrix-400 rounded-full animate-pulse" />
                    <span className="text-sm font-mono text-cyber-matrix-400">
                      2/2
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">
                    Processing Load
                  </span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.processingLoad)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-text-muted">Memory Usage</span>
                  <span className="text-sm font-mono text-text-primary">
                    {Math.round(systemHealth.memoryUsage)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
