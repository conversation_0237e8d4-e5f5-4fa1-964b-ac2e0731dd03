import type { Config } from 'jest';

/**
 * Base Jest configuration for React components with TypeScript
 */
export const baseJestConfig: Config = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  // setupFilesAfterEnv should be specified by each package
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: {
          jsx: 'react-jsx',
        },
      },
    ],
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.(ts|tsx|js)',
    '<rootDir>/src/**/*.(test|spec).(ts|tsx|js)',
  ],
  collectCoverageFrom: [
    'src/**/*.(ts|tsx)',
    '!src/**/*.d.ts',
    '!src/test-setup.ts',
    '!src/**/__tests__/**',
    '!src/**/*.stories.*',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  verbose: true,
  // Ensure proper module resolution for jest-dom and other ESM packages
  transformIgnorePatterns: [
    'node_modules/(?!(@testing-library/jest-dom|@jest/transform|@jest/environment)/)',
  ],
};

/**
 * Base Jest configuration for Node.js packages with TypeScript
 */
export const nodeJestConfig: Config = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: [
    '**/__tests__/**/*.+(ts|tsx|js)',
    '**/*.(test|spec).+(ts|tsx|js)',
  ],
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.test.json',
      },
    ],
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/__tests__/**',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: [],
  testTimeout: 10000,
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
};

/**
 * Create a Jest configuration with a custom display name
 */
export const createJestConfig = (
  displayName: string,
  overrides: Partial<Config> = {},
): Config => ({
  ...baseJestConfig,
  displayName,
  ...overrides,
});

/**
 * Create a Node.js Jest configuration with a custom display name
 */
export const createNodeJestConfig = (
  displayName: string,
  overrides: Partial<Config> = {},
): Config => ({
  ...nodeJestConfig,
  displayName,
  ...overrides,
});

// Re-export test utilities
export * from './test-utils';
