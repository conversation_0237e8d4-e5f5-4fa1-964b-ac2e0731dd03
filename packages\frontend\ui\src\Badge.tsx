import * as React from 'react';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?:
    | 'default'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'high'
    | 'danger'
    | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

export const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  (
    { className = '', variant = 'default', size = 'md', children, ...props },
    ref,
  ) => {
    const baseClasses =
      'inline-flex items-center rounded-full font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2';

    const variantClasses = {
      default: 'bg-primary-500 text-white hover:bg-primary-600',
      secondary:
        'bg-background-tertiary border border-border-primary text-text-secondary hover:bg-background-hover',
      success: 'bg-cyber-matrix-500 text-white hover:bg-cyber-matrix-600',
      warning: 'bg-cyber-warning-500 text-white hover:bg-cyber-warning-600',
      high: 'bg-cyber-amber-500 text-white hover:bg-cyber-amber-600',
      danger: 'bg-cyber-danger-500 text-white hover:bg-cyber-danger-600',
      outline:
        'border border-border-primary text-text-primary hover:bg-background-hover',
    };

    const sizeClasses = {
      sm: 'px-3 py-0.5 text-xs',
      md: 'px-2.5 py-0.5 text-sm',
      lg: 'px-3 py-1 text-base',
    };

    const classes =
      `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`.trim();

    return (
      <div className={classes} ref={ref} {...props}>
        {children}
      </div>
    );
  },
);

Badge.displayName = 'Badge';
