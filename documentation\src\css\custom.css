/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Component Showcase Styles */
.component-showcase {
  margin: 2rem 0;
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 8px;
  overflow: hidden;
}

.showcase-header {
  padding: 1rem 1.5rem;
  background: var(--ifm-background-color);
  border-bottom: 1px solid var(--ifm-color-emphasis-200);
}

.showcase-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}

.showcase-header p {
  margin: 0;
  color: var(--ifm-color-content-secondary);
}

.showcase-preview {
  background: var(--ifm-background-surface-color);
}

.preview-container {
  padding: 2rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  min-height: 120px;
}

.preview-controls {
  padding: 1rem 1.5rem;
  background: var(--ifm-color-emphasis-100);
  border-top: 1px solid var(--ifm-color-emphasis-200);
}

.toggle-code-btn {
  background: var(--ifm-color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.toggle-code-btn:hover {
  background: var(--ifm-color-primary-dark);
}

.showcase-code {
  border-top: 1px solid var(--ifm-color-emphasis-200);
}

.showcase-props {
  padding: 1.5rem;
  background: var(--ifm-background-color);
  border-top: 1px solid var(--ifm-color-emphasis-200);
}

.showcase-props h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.showcase-props table {
  width: 100%;
  border-collapse: collapse;
}

.showcase-props th,
.showcase-props td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--ifm-color-emphasis-200);
  vertical-align: top;
}

.showcase-props th {
  background: var(--ifm-color-emphasis-100);
  font-weight: 600;
}

.showcase-props code {
  background: var(--ifm-code-background);
  color: var(--ifm-code-color);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.85em;
}
