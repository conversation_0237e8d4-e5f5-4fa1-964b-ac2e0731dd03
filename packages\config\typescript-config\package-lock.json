{"name": "@telesoft/typescript-config", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@telesoft/typescript-config", "version": "0.0.0", "dependencies": {"typescript": "^5"}, "devDependencies": {"@tsconfig/next": "^2.0.3", "@tsconfig/node20": "^20.1.5", "@tsconfig/recommended": "^1.0.8", "@tsconfig/strictest": "^2.0.5", "@tsconfig/vite-react": "^6.3.5"}}, "../../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript": {"version": "5.8.3", "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "node_modules/@tsconfig/next": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/@tsconfig/next/-/next-2.0.3.tgz", "integrity": "sha512-b4aKvmdWnv9aUy+NStUFlefirk3SItW20OJtbi5/ue9oMRaQtUYbN5zsr7OmV3rlx6Gp6h7A82gPoCXhrNGVPQ==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node20": {"version": "20.1.5", "resolved": "https://registry.npmjs.org/@tsconfig/node20/-/node20-20.1.5.tgz", "integrity": "sha512-Vm8e3WxDTqMGPU4GATF9keQAIy1Drd7bPwlgzKJnZtoOsTm1tduUTbDjg0W5qERvGuxPI2h9RbMufH0YdfBylA==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/recommended": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/@tsconfig/recommended/-/recommended-1.0.8.tgz", "integrity": "sha512-TotjFaaXveVUdsrXCdalyF6E5RyG6+7hHHQVZonQtdlk1rJZ1myDIvPUUKPhoYv+JAzThb2lQJh9+9ZfF46hsA==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/strictest": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@tsconfig/strictest/-/strictest-2.0.5.tgz", "integrity": "sha512-ec4tjL2Rr0pkZ5hww65c+EEPYwxOi4Ryv+0MtjeaSQRJyq322Q27eOQiFbuNgw2hpL4hB1/W/HBGk3VKS43osg==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/vite-react": {"version": "6.3.5", "resolved": "https://registry.npmjs.org/@tsconfig/vite-react/-/vite-react-6.3.5.tgz", "integrity": "sha512-1JDbtNCgexy5HuQdBkFHv38u5j79XWI1KWs6Jxwnm9i2rD1IPWyzlPqh4vgsAiRu1Zy1d26wxxSJYVqGaDJ+PQ==", "dev": true, "license": "MIT"}, "node_modules/typescript": {"resolved": "../../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "link": true}}}