'use client';

import React from 'react';
import {
  useDeploymentsWebSocket,
  useDeploymentFilters,
  useDeploymentStats,
  useDeploymentTimeAnalysis,
} from '../lib/hooks/useDeployments';
import { Deployment } from '@telesoft/types';

interface DeploymentCardProps {
  deployment: Deployment;
}

function DeploymentCard({ deployment }: DeploymentCardProps) {
  const dataPoints = Object.keys(deployment.data).length;
  const latestTimestamp = Math.max(
    ...Object.keys(deployment.data).map((ts) => parseInt(ts)),
  );
  const latestValue = deployment.data[latestTimestamp.toString()];
  const latestTime = new Date(latestTimestamp).toLocaleString();

  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-semibold text-lg text-gray-900">
          {deployment.name}
        </h3>
        <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
          {deployment.namespace}
        </span>
      </div>
      <div className="space-y-2 text-sm text-gray-600">
        <div className="flex justify-between">
          <span>Data Points:</span>
          <span className="font-medium">{dataPoints}</span>
        </div>
        <div className="flex justify-between">
          <span>Latest Value:</span>
          <span className="font-medium">
            {latestValue?.toLocaleString() || 'N/A'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>Last Update:</span>
          <span className="font-medium text-xs">{latestTime}</span>
        </div>
      </div>
    </div>
  );
}

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  className?: string;
}

function StatsCard({ title, value, subtitle, className = '' }: StatsCardProps) {
  return (
    <div className={`bg-white p-4 rounded-lg shadow-sm border ${className}`}>
      <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">
        {title}
      </h3>
      <div className="mt-2">
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        {subtitle && (
          <div className="text-sm text-gray-600 mt-1">{subtitle}</div>
        )}
      </div>
    </div>
  );
}

export function DeploymentsDashboard() {
  const {
    deployments,
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    lastUpdate,
    refetch,
  } = useDeploymentsWebSocket({
    autoConnect: true,
    fallbackToRest: true,
  });

  const { filteredDeployments, filters, setFilters, resetFilters } =
    useDeploymentFilters(deployments);

  const stats = useDeploymentStats(deployments);
  const timeAnalysis = useDeploymentTimeAnalysis(deployments);

  const connectionStatus = isConnecting
    ? 'Connecting...'
    : isConnected
      ? 'Connected (Live)'
      : 'Disconnected';

  const connectionStatusColor = isConnecting
    ? 'text-yellow-600'
    : isConnected
      ? 'text-green-600'
      : 'text-red-600';

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              ML Deployments Dashboard
            </h1>
            <p className="text-gray-600 mt-1">
              Monitor machine learning deployment metrics in real-time
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-sm">
              <span className="text-gray-500">Status: </span>
              <span className={`font-medium ${connectionStatusColor}`}>
                {connectionStatus}
              </span>
            </div>
            <div className="flex gap-2">
              {!isConnected && (
                <button
                  onClick={connect}
                  disabled={isConnecting}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  Connect
                </button>
              )}
              {isConnected && (
                <button
                  onClick={disconnect}
                  className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Disconnect
                </button>
              )}
              <button
                onClick={refetch}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Refresh
              </button>
            </div>
          </div>
        </div>
        {lastUpdate && (
          <p className="text-xs text-gray-500 mt-2">
            Last updated: {new Date(lastUpdate).toLocaleString()}
          </p>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </div>
        </div>
      )}

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatsCard
          title="Total Deployments"
          value={stats.total}
          subtitle={`${stats.namespacesCount} namespaces`}
        />
        <StatsCard
          title="Total Data Points"
          value={stats.totalDataPoints.toLocaleString()}
          subtitle="Across all deployments"
        />
        <StatsCard
          title="Overall Average"
          value={stats.overallStats.avg.toFixed(2)}
          subtitle={`Range: ${stats.overallStats.min} - ${stats.overallStats.max}`}
        />
        <StatsCard
          title="Time Range"
          value={
            timeAnalysis.globalTimeRange
              ? `${(timeAnalysis.globalTimeRange.duration / (1000 * 60 * 60)).toFixed(1)}h`
              : 'N/A'
          }
          subtitle={
            timeAnalysis.globalTimeRange
              ? `${timeAnalysis.allTimestamps.length} unique timestamps`
              : 'No data'
          }
        />
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
        <h3 className="text-lg font-semibold mb-3">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Namespace
            </label>
            <select
              value={filters.namespace || ''}
              onChange={(e) =>
                setFilters({ namespace: e.target.value || undefined })
              }
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="">All namespaces</option>
              {stats.namespaces.map((namespace) => (
                <option key={namespace} value={namespace}>
                  {namespace} ({stats.namespaceDistribution[namespace]})
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Deployment Name
            </label>
            <select
              value={filters.name || ''}
              onChange={(e) =>
                setFilters({ name: e.target.value || undefined })
              }
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="">All deployments</option>
              {stats.deploymentNames.map((name) => (
                <option key={name} value={name}>
                  {name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Pattern
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={filters.namePattern || ''}
                onChange={(e) =>
                  setFilters({ namePattern: e.target.value || undefined })
                }
                placeholder="Search deployment names..."
                className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
              <button
                onClick={resetFilters}
                className="px-3 py-2 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
        <div className="mt-3 text-sm text-gray-600">
          Showing {filteredDeployments.length} of {deployments.length}{' '}
          deployments
        </div>
      </div>

      {/* Deployments Grid */}
      {filteredDeployments.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredDeployments.map((deployment) => (
            <DeploymentCard
              key={`${deployment.name}-${deployment.namespace}`}
              deployment={deployment}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">
            {deployments.length === 0
              ? 'No deployments found'
              : 'No deployments match the current filters'}
          </div>
          {deployments.length === 0 && !isConnecting && (
            <button
              onClick={refetch}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Try Again
            </button>
          )}
        </div>
      )}
    </div>
  );
}
