@import 'tailwindcss';

/* Import the dark theme as default */
@import '@telesoft/color-palette/tailwind-v4.css';

/* Import the light theme CSS */
@import '@telesoft/color-palette/tailwind-v4-light.css';

/* Application-specific styles can be added here */

/* Prevent layout shift when scrollbar appears/disappears */
/* Force vertical scrollbar to always be present */
html {
  overflow-y: scroll !important;
  overflow-x: hidden;
  min-height: 100vh;
}

body {
  overflow-x: hidden;
  min-height: 100vh;
}

/* Ensure the main container doesn't cause layout shifts */
#__next {
  min-height: 100vh;
}

/* Cross-browser scrollbar width consistency */
::-webkit-scrollbar {
  width: 17px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
