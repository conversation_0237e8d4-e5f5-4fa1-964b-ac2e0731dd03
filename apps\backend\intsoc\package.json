{"name": "@telesoft/intsoc-backend", "version": "0.1.0", "description": "Backend API for intsoc application", "main": "dist/bin/http.bundled.js", "scripts": {"dev": "tsx watch src/bin/http.ts", "watch": "tsx watch src/bin/http.ts", "build": "pnpm run build:tsc && pnpm run build:bundle", "build:tsc": "tsc", "build:bundle": "esbuild dist/bin/http.js --bundle --platform=node --target=node24 --outfile=dist/bin/http.bundled.js --external:systeminformation --external:ws --external:compression --external:helmet --external:express --external:axios --external:dotenv --external:node-fetch --external:ioredis --format=cjs", "build:package": "pnpm build && pnpm run package", "start": "node dist/bin/http.bundled.js", "lint": "eslint .", "format": "prettier --write . --ignore-path ../../../.prettierignore", "type-check": "tsc --noEmit", "package": "./scripts/create-tarball.sh"}, "dependencies": {"@telesoft-ui/redis": "workspace:*", "@types/node-fetch": "^2.6.12", "@types/ws": "^8.18.1", "axios": "^1.9.0", "compression": "^1.8.0", "dotenv": "^16.3.1", "express": "^5.1.0", "helmet": "^8.1.0", "node-fetch": "^3.3.2", "systeminformation": "^5.27.1", "ws": "^8.18.2"}, "devDependencies": {"@eslint/js": "catalog:eslint", "@types/compression": "^1.8.0", "@types/express": "^5.0.2", "@types/node": "catalog:node-24", "@typescript-eslint/eslint-plugin": "catalog:eslint", "@typescript-eslint/parser": "catalog:eslint", "esbuild": "^0.24.2", "eslint": "catalog:eslint", "tsx": "^4.6.0", "typescript": "catalog:typescript-5"}}