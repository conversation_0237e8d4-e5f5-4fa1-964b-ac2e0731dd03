# Unit Testing Guide

This guide covers testing standards and best practices for the Telesoft UI project. We use Jest and React Testing Library to ensure our components and utilities work correctly and remain maintainable.

## 🧪 Testing Philosophy

### 1. Test Behavior, Not Implementation

- Focus on what the component does, not how it does it
- Test user interactions and expected outcomes
- Avoid testing internal state or implementation details

### 2. Write Tests That Give Confidence

- Test the critical paths and edge cases
- Ensure tests fail when they should
- Write tests that are easy to understand and maintain

### 3. Keep Tests Simple and Focused

- One test should verify one behavior
- Use descriptive test names
- Keep setup and assertions minimal

## 🛠 Testing Stack

### Core Testing Tools

```mermaid
graph LR
    subgraph "Testing Framework"
        JEST[Jest 29.x]
        RTL[React Testing Library]
        JestDOM[@testing-library/jest-dom]
        UserEvent[@testing-library/user-event]
    end

    subgraph "Configuration"
        JestConfig[@telesoft/jest-config]
        TestUtils[Test Utilities]
        Setup[test-setup.ts]
    end

    JEST --> RTL
    RTL --> JestDOM
    JestDOM --> UserEvent

    JestConfig --> TestUtils
    TestUtils --> Setup
```

### Shared Configuration

Our monorepo uses `@telesoft/jest-config` for consistent testing setup:

```typescript
// jest.config.cjs
import { createJestConfig } from '@telesoft/jest-config';

export default createJestConfig('@telesoft/ui');
```

## 📝 Writing Component Tests

### Basic Component Test Structure

```typescript
// Button.test.tsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from './Button';

describe('Button', () => {
  // Test basic rendering
  it('renders with children', () => {
    render(<Button>Click me</Button>);

    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  // Test props
  it('applies variant classes correctly', () => {
    render(<Button variant="secondary">Secondary</Button>);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-gray-600');
  });

  // Test interactions
  it('calls onClick when clicked', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();

    render(<Button onClick={handleClick}>Click me</Button>);

    await user.click(screen.getByRole('button'));

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  // Test disabled state
  it('does not call onClick when disabled', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();

    render(
      <Button disabled onClick={handleClick}>
        Disabled
      </Button>
    );

    await user.click(screen.getByRole('button'));

    expect(handleClick).not.toHaveBeenCalled();
  });

  // Test accessibility
  it('has proper accessibility attributes', () => {
    render(<Button disabled>Disabled Button</Button>);

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('type', 'button');
  });
});
```

### Testing Form Components

```typescript
// Input.test.tsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Input } from './Input';

describe('Input', () => {
  it('updates value when user types', async () => {
    const user = userEvent.setup();
    const handleChange = jest.fn();

    render(
      <Input
        label="Username"
        value=""
        onChange={handleChange}
        data-testid="username-input"
      />
    );

    const input = screen.getByLabelText('Username');

    await user.type(input, 'john.doe');

    expect(handleChange).toHaveBeenCalledWith('john.doe');
  });

  it('shows error message when invalid', () => {
    render(
      <Input
        label="Email"
        value="invalid-email"
        error="Please enter a valid email"
      />
    );

    expect(screen.getByText('Please enter a valid email')).toBeInTheDocument();
    expect(screen.getByLabelText('Email')).toHaveAttribute('aria-invalid', 'true');
  });

  it('associates label with input correctly', () => {
    render(<Input label="Password" />);

    const input = screen.getByLabelText('Password');
    expect(input).toHaveAttribute('type', 'text');
  });
});
```

### Testing Complex Components

```typescript
// DataTable.test.tsx
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DataTable } from './DataTable';

const mockData = [
  { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'admin' },
  { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'user' },
];

const mockColumns = [
  { key: 'name', header: 'Name', sortable: true },
  { key: 'email', header: 'Email' },
  { key: 'role', header: 'Role' },
];

describe('DataTable', () => {
  it('renders table with data', () => {
    render(<DataTable data={mockData} columns={mockColumns} />);

    // Check headers
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Role')).toBeInTheDocument();

    // Check data rows
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('sorts data when sortable column header is clicked', async () => {
    const user = userEvent.setup();

    render(<DataTable data={mockData} columns={mockColumns} />);

    // Click the sortable Name header
    await user.click(screen.getByText('Name'));

    // Check that data is sorted (Jane should come before John)
    const rows = screen.getAllByRole('row');
    expect(within(rows[1]).getByText('Jane Smith')).toBeInTheDocument();
    expect(within(rows[2]).getByText('John Doe')).toBeInTheDocument();
  });

  it('calls onRowClick when row is clicked', async () => {
    const user = userEvent.setup();
    const handleRowClick = jest.fn();

    render(
      <DataTable
        data={mockData}
        columns={mockColumns}
        onRowClick={handleRowClick}
      />
    );

    // Click on first data row (skip header row)
    const dataRows = screen.getAllByRole('row').slice(1);
    await user.click(dataRows[0]);

    expect(handleRowClick).toHaveBeenCalledWith(mockData[0]);
  });

  it('shows empty state when no data', () => {
    render(<DataTable data={[]} columns={mockColumns} />);

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });
});
```

## 🎣 Testing Custom Hooks

### Simple Hook Testing

```typescript
// useCounter.test.ts
import { renderHook, act } from '@testing-library/react';
import { useCounter } from './useCounter';

describe('useCounter', () => {
  it('initializes with default value', () => {
    const { result } = renderHook(() => useCounter());

    expect(result.current.count).toBe(0);
  });

  it('initializes with custom value', () => {
    const { result } = renderHook(() => useCounter(10));

    expect(result.current.count).toBe(10);
  });

  it('increments count', () => {
    const { result } = renderHook(() => useCounter());

    act(() => {
      result.current.increment();
    });

    expect(result.current.count).toBe(1);
  });

  it('decrements count', () => {
    const { result } = renderHook(() => useCounter(5));

    act(() => {
      result.current.decrement();
    });

    expect(result.current.count).toBe(4);
  });

  it('resets count', () => {
    const { result } = renderHook(() => useCounter(10));

    act(() => {
      result.current.increment();
      result.current.increment();
    });

    expect(result.current.count).toBe(12);

    act(() => {
      result.current.reset();
    });

    expect(result.current.count).toBe(10);
  });
});
```

### Testing Hooks with Dependencies

```typescript
// useApi.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { useApi } from './useApi';

// Mock fetch
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('useApi', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  it('fetches data successfully', async () => {
    const mockData = { id: 1, name: 'Test User' };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockData,
    } as Response);

    const { result } = renderHook(() => useApi<typeof mockData>('/api/user/1'));

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(null);

    // Wait for data to load
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData);
    expect(result.current.error).toBe(null);
    expect(mockFetch).toHaveBeenCalledWith('/api/user/1');
  });

  it('handles fetch errors', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useApi('/api/user/1'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toEqual(new Error('Network error'));
  });

  it('does not fetch when disabled', () => {
    renderHook(() => useApi('/api/user/1', { enabled: false }));

    expect(mockFetch).not.toHaveBeenCalled();
  });
});
```

## 🔧 Testing Utilities

### Custom Test Utilities

```typescript
// test-utils.tsx
import React from 'react';
import { render as rtlRender, RenderOptions } from '@testing-library/react';
import { ThemeProvider } from '../context/ThemeContext';

// Custom render function with providers
function render(ui: React.ReactElement, options?: RenderOptions) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <ThemeProvider>
        {children}
      </ThemeProvider>
    );
  }

  return rtlRender(ui, { wrapper: Wrapper, ...options });
}

// Custom matchers
export function expectToHaveClasses(element: HTMLElement, classes: string[]) {
  classes.forEach(className => {
    expect(element).toHaveClass(className);
  });
}

export function expectNotToHaveClasses(element: HTMLElement, classes: string[]) {
  classes.forEach(className => {
    expect(element).not.toHaveClass(className);
  });
}

// Mock handlers
export function createMockHandlers() {
  return {
    onClick: jest.fn(),
    onChange: jest.fn(),
    onSubmit: jest.fn(),
    onFocus: jest.fn(),
    onBlur: jest.fn(),
  };
}

// Re-export everything
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
export { render };
```

### Testing with Custom Utilities

```typescript
// Component.test.tsx
import { render, screen, expectToHaveClasses, createMockHandlers } from '../test-utils';
import { Button } from './Button';

describe('Button with custom utilities', () => {
  it('applies correct classes', () => {
    render(<Button variant="primary" size="lg">Large Primary</Button>);

    const button = screen.getByRole('button');
    expectToHaveClasses(button, [
      'bg-blue-600',
      'text-white',
      'px-6',
      'py-3'
    ]);
  });

  it('handles multiple events', async () => {
    const user = userEvent.setup();
    const handlers = createMockHandlers();

    render(
      <Button
        onClick={handlers.onClick}
        onFocus={handlers.onFocus}
        onBlur={handlers.onBlur}
      >
        Interactive Button
      </Button>
    );

    const button = screen.getByRole('button');

    await user.click(button);
    await user.tab(); // Focus
    await user.tab(); // Blur (focus next element)

    expect(handlers.onClick).toHaveBeenCalledTimes(1);
    expect(handlers.onFocus).toHaveBeenCalledTimes(1);
    expect(handlers.onBlur).toHaveBeenCalledTimes(1);
  });
});
```

## 🧩 Integration Testing

### Testing Component Interactions

```typescript
// UserForm.test.tsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UserForm } from './UserForm';

describe('UserForm Integration', () => {
  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    const handleSubmit = jest.fn();

    render(<UserForm onSubmit={handleSubmit} />);

    // Fill out form
    await user.type(screen.getByLabelText('Name'), 'John Doe');
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.selectOptions(screen.getByLabelText('Role'), 'admin');

    // Submit form
    await user.click(screen.getByRole('button', { name: 'Save User' }));

    expect(handleSubmit).toHaveBeenCalledWith({
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'admin',
    });
  });

  it('shows validation errors for invalid data', async () => {
    const user = userEvent.setup();

    render(<UserForm onSubmit={jest.fn()} />);

    // Try to submit without filling required fields
    await user.click(screen.getByRole('button', { name: 'Save User' }));

    expect(screen.getByText('Name is required')).toBeInTheDocument();
    expect(screen.getByText('Email is required')).toBeInTheDocument();
  });

  it('disables submit button while loading', async () => {
    const user = userEvent.setup();
    const handleSubmit = jest.fn().mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    );

    render(<UserForm onSubmit={handleSubmit} />);

    // Fill form and submit
    await user.type(screen.getByLabelText('Name'), 'John Doe');
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: 'Save User' }));

    // Button should be disabled and show loading state
    const submitButton = screen.getByRole('button', { name: 'Saving...' });
    expect(submitButton).toBeDisabled();
  });
});
```

## 🎯 Test Coverage Guidelines

### Coverage Targets

- **Statements**: 80% minimum, 90% target
- **Branches**: 75% minimum, 85% target
- **Functions**: 85% minimum, 95% target
- **Lines**: 80% minimum, 90% target

### Running Coverage Reports

```bash
# Generate coverage report
pnpm test:coverage

# View HTML coverage report
open coverage/lcov-report/index.html
```

### Coverage Configuration

```javascript
// jest.config.cjs
export default {
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.tsx',
    '!src/test-setup.ts',
    '!src/**/__tests__/**',
  ],
  coverageThreshold: {
    global: {
      statements: 80,
      branches: 75,
      functions: 85,
      lines: 80,
    },
  },
};
```

## 🚀 Best Practices

### 1. Test Organization

```typescript
describe('ComponentName', () => {
  describe('rendering', () => {
    it('renders with default props', () => {});
    it('renders with custom props', () => {});
  });

  describe('interactions', () => {
    it('handles click events', () => {});
    it('handles keyboard events', () => {});
  });

  describe('edge cases', () => {
    it('handles empty data', () => {});
    it('handles error states', () => {});
  });

  describe('accessibility', () => {
    it('has proper ARIA attributes', () => {});
    it('supports keyboard navigation', () => {});
  });
});
```

### 2. Async Testing

```typescript
// ✅ Good: Proper async testing
it('loads data on mount', async () => {
  render(<DataComponent />);

  // Wait for loading to complete
  await waitFor(() => {
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
  });

  expect(screen.getByText('Data loaded')).toBeInTheDocument();
});

// ❌ Bad: Not waiting for async operations
it('loads data on mount', () => {
  render(<DataComponent />);
  expect(screen.getByText('Data loaded')).toBeInTheDocument(); // May fail
});
```

### 3. User-Centric Queries

```typescript
// ✅ Good: Query by role and accessible name
const button = screen.getByRole('button', { name: 'Save changes' });
const input = screen.getByLabelText('Email address');
const heading = screen.getByRole('heading', { name: 'User Profile' });

// ❌ Bad: Query by implementation details
const button = screen.getByClassName('btn-primary');
const input = screen.getByTestId('email-input');
const heading = screen.getByTagName('h1');
```

### 4. Effective Mocking

```typescript
// ✅ Good: Mock external dependencies
jest.mock('../api/userService', () => ({
  fetchUser: jest.fn(),
  updateUser: jest.fn(),
}));

// ✅ Good: Mock only what you need
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

// ❌ Bad: Over-mocking
jest.mock('react', () => ({
  useState: jest.fn(),
  useEffect: jest.fn(),
  // ... mocking too much
}));
```

## 📋 Testing Checklist

Before merging, ensure your tests:

### ✅ Coverage & Quality

- [ ] Tests cover main functionality
- [ ] Edge cases are tested
- [ ] Error scenarios are covered
- [ ] Coverage meets threshold requirements

### ✅ User-Focused

- [ ] Tests use semantic queries (byRole, byLabelText)
- [ ] User interactions are tested realistically
- [ ] Accessibility features are verified

### ✅ Maintainable

- [ ] Tests are easy to read and understand
- [ ] Test names describe behavior clearly
- [ ] Setup is minimal and focused
- [ ] No unnecessary complexity

### ✅ Reliable

- [ ] Tests don't depend on timing
- [ ] Async operations are properly awaited
- [ ] Tests are deterministic
- [ ] No flaky test behavior

---

Continue to our [VSCode Setup Guide](./vscode-setup.md) to configure your development environment for maximum productivity.
