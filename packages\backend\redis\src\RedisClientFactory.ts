import { RedisClient } from './RedisClient';
import { RedisConfig, RedisConnectionOptions } from './types';

export class RedisClientFactory {
  private static instances: Map<string, RedisClient> = new Map();

  /**
   * Create a new Redis client instance
   */
  static create(
    config: RedisConfig,
    options: RedisConnectionOptions = {},
    instanceName: string = 'default',
  ): RedisClient {
    const client = new RedisClient(config, options);
    this.instances.set(instanceName, client);
    return client;
  }

  /**
   * Get an existing Redis client instance by name
   */
  static getInstance(
    instanceName: string = 'default',
  ): RedisClient | undefined {
    return this.instances.get(instanceName);
  }

  /**
   * Remove and disconnect a Redis client instance
   */
  static async removeInstance(instanceName: string = 'default'): Promise<void> {
    const client = this.instances.get(instanceName);
    if (client) {
      await client.disconnect();
      this.instances.delete(instanceName);
    }
  }

  /**
   * Disconnect all client instances
   */
  static async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.instances.values()).map(
      (client) => client.disconnect(),
    );
    await Promise.all(disconnectPromises);
    this.instances.clear();
  }

  /**
   * Get all instance names
   */
  static getInstanceNames(): string[] {
    return Array.from(this.instances.keys());
  }
}
