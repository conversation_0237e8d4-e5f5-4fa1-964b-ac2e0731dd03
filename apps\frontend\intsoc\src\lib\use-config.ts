import { useState, useEffect } from 'react';

export interface ClientConfig {
  api: {
    baseUrl: string;
    wsUrl: string;
    timeout: number;
    useProxy: boolean;
    enableCache: boolean;
    cacheTimeout: number;
  };
  debug: {
    apiDebug: boolean;
  };
  app: {
    name: string;
  };
}

interface UseConfigResult {
  config: ClientConfig | null;
  loading: boolean;
  error: string | null;
}

// Default configuration used during SSR and as fallback
const DEFAULT_CONFIG: ClientConfig = {
  api: {
    baseUrl: 'http://localhost:3000', // Default to frontend server
    wsUrl: 'ws://localhost:4001/ws', // Fixed: include /ws path in fallback
    timeout: 10000,
    useProxy: true, // Default to proxy mode
    enableCache: false,
    cacheTimeout: 60000,
  },
  debug: {
    apiDebug: false,
  },
  app: {
    name: 'IntSOC',
  },
};

let cachedConfig: ClientConfig | null = null;
let configPromise: Promise<ClientConfig> | null = null;
let isInitialized = false;

/**
 * Fetch configuration from the server
 */
export async function fetchConfig(): Promise<ClientConfig> {
  if (cachedConfig && isInitialized) {
    return cachedConfig;
  }

  if (configPromise) {
    return configPromise;
  }

  configPromise = (async () => {
    try {
      const response = await fetch('/api/config', {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch config: ${response.status}`);
      }

      const config = await response.json();
      cachedConfig = config;
      isInitialized = true;
      console.log('[Config] Successfully fetched runtime configuration:', {
        baseUrl: config.api.baseUrl,
        useProxy: config.api.useProxy,
      });
      return config;
    } catch (error) {
      console.warn(
        'Failed to fetch runtime configuration, using defaults:',
        error,
      );
      cachedConfig = DEFAULT_CONFIG;
      isInitialized = true;
      return DEFAULT_CONFIG;
    } finally {
      configPromise = null;
    }
  })();

  return configPromise;
}

/**
 * Hook to get runtime configuration
 * This replaces the build-time environment variables with runtime configuration
 */
export function useConfig(): UseConfigResult {
  const [config, setConfig] = useState<ClientConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchConfig()
      .then((fetchedConfig) => {
        setConfig(fetchedConfig);
        setError(null);
      })
      .catch((err) => {
        setError(err.message);
        setConfig(DEFAULT_CONFIG);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return { config, loading, error };
}

/**
 * Get configuration synchronously (for use in API clients)
 * Returns cached config if available, otherwise triggers fetch and returns default
 */
export function getConfig(): ClientConfig {
  // If we don't have cached config and we're in the browser, trigger fetch
  if (!isInitialized && typeof window !== 'undefined') {
    console.warn(
      '[Config] Configuration not initialized yet! This may cause incorrect API URLs. Triggering fetch...',
    );
    // Trigger fetch but don't wait for it
    fetchConfig().catch(console.warn);
    return DEFAULT_CONFIG;
  } else if (cachedConfig && isInitialized) {
    console.log('[Config] Returning cached config:', {
      baseUrl: cachedConfig.api.baseUrl,
      useProxy: cachedConfig.api.useProxy,
    });
    return cachedConfig;
  } else {
    console.log('[Config] Server-side execution, returning defaults');
    return DEFAULT_CONFIG;
  }
}

/**
 * Get configuration with async support
 * This ensures we have the latest config from the server
 */
export async function getConfigAsync(): Promise<ClientConfig> {
  return await fetchConfig();
}

/**
 * Initialize configuration (call this during app startup)
 */
export async function initializeConfig(): Promise<ClientConfig> {
  return await fetchConfig();
}

/**
 * Legacy compatibility function for existing code
 * @deprecated Use useConfig hook instead
 */
export function getApiConfig() {
  const config = getConfig();
  const result = {
    baseUrl: config.api.baseUrl,
    wsUrl: config.api.wsUrl,
    timeout: config.api.timeout,
    useProxy: config.api.useProxy,
    enableCache: config.api.enableCache,
    cacheTimeout: config.api.cacheTimeout,
  };
  console.log('[Config] getApiConfig() returning:', {
    baseUrl: result.baseUrl,
    useProxy: result.useProxy,
  });
  return result;
}

/**
 * Legacy compatibility function for existing code
 * @deprecated Configuration is now fetched at runtime
 */
export function validateEnvironment(): void {
  // This function is now a no-op since validation happens server-side
  console.log('Environment validation moved to server-side configuration API');
}

/**
 * Validate client-side configuration
 */
export function validateApiConfig(config: ClientConfig['api']): string[] {
  const errors: string[] = [];

  if (!config.baseUrl) {
    errors.push('API base URL is not configured');
  }

  if (!config.wsUrl) {
    errors.push('WebSocket URL is not configured');
  }

  if (config.timeout < 1000) {
    errors.push('API timeout should be at least 1000ms');
  }

  if (config.cacheTimeout < 0) {
    errors.push('Cache timeout cannot be negative');
  }

  return errors;
}
