/**
 * Server-side API proxy service
 * Handles communication between Next.js API routes and backend server
 *
 * Note: This only handles HTTP requests. WebSocket connections bypass Next.js
 * and connect directly from the browser to the backend WebSocket server.
 */

import { getServerConfig, validateServerEnvironment } from './server-config';

// Validate server environment on initialization
validateServerEnvironment();

export class BackendApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public originalError?: any,
  ) {
    super(message);
    this.name = 'BackendApiError';
  }
}

export interface ProxyOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  skipRetry?: boolean;
}

export class BackendApiProxy {
  private config = getServerConfig();
  private activeRequests = new Map<string, Promise<Response>>();

  /**
   * Forward request to backend server with retry logic
   */
  async forwardRequest(
    endpoint: string,
    options: ProxyOptions = {},
  ): Promise<Response> {
    const url = `${this.config.backendUrl}${endpoint}`;
    const requestKey = `${options.method || 'GET'}:${endpoint}`;

    // Check for duplicate GET requests
    if (options.method === 'GET' || !options.method) {
      const activeRequest = this.activeRequests.get(requestKey);
      if (activeRequest) {
        console.log(`[API Proxy] Reusing active request for ${requestKey}`);
        return activeRequest.then((res) => res.clone());
      }
    }

    const requestOptions: RequestInit = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-Proxy/1.0',
        'X-Forwarded-For': 'proxy',
        ...options.headers,
      },
    };

    // Add body for non-GET requests
    if (options.body && options.method !== 'GET') {
      requestOptions.body =
        typeof options.body === 'string'
          ? options.body
          : JSON.stringify(options.body);
    }

    const executeRequest = async (retryCount = 0): Promise<Response> => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(
          () => controller.abort(),
          this.config.timeout,
        );

        console.log(
          `[API Proxy] ${options.method || 'GET'} ${url}${retryCount > 0 ? ` (retry ${retryCount})` : ''}`,
        );

        const response = await fetch(url, {
          ...requestOptions,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Don't retry on client errors
        if (!response.ok && response.status >= 400 && response.status < 500) {
          return response;
        }

        // Retry on server errors
        if (
          !response.ok &&
          !options.skipRetry &&
          retryCount < this.config.maxRetries
        ) {
          const delay = this.config.retryDelay * Math.pow(2, retryCount);
          console.log(
            `[API Proxy] Backend returned ${response.status}, retrying in ${delay}ms...`,
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
          return executeRequest(retryCount + 1);
        }

        return response;
      } catch (error) {
        // Remove from active requests on error
        this.activeRequests.delete(requestKey);

        console.error(`[API Proxy] Request failed for ${requestKey}:`, error);

        if (error instanceof DOMException && error.name === 'AbortError') {
          if (!options.skipRetry && retryCount < this.config.maxRetries) {
            const delay = this.config.retryDelay * Math.pow(2, retryCount);
            console.log(
              `[API Proxy] Request timeout, retrying in ${delay}ms...`,
            );
            await new Promise((resolve) => setTimeout(resolve, delay));
            return executeRequest(retryCount + 1);
          }
          throw new BackendApiError('Backend request timeout', 408);
        }

        // Handle network/connection errors
        if (
          error instanceof TypeError &&
          error.message.includes('fetch failed')
        ) {
          // Check if it's a connection refused error
          const isConnectionRefused =
            error.cause && (error.cause as any).code === 'ECONNREFUSED';

          if (isConnectionRefused) {
            throw new BackendApiError(
              `Failed to connect to backend: Connection refused (${url})`,
              503,
              error,
            );
          }

          throw new BackendApiError(
            `Failed to connect to backend: ${error.message}`,
            503,
            error,
          );
        }

        // Handle other errors
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';

        throw new BackendApiError(
          `Failed to connect to backend: ${errorMessage}`,
          500,
          error,
        );
      }
    };

    // Store promise for deduplication
    const requestPromise = executeRequest();
    if (options.method === 'GET' || !options.method) {
      this.activeRequests.set(requestKey, requestPromise);
      requestPromise.finally(() => this.activeRequests.delete(requestKey));
    }

    return requestPromise;
  }

  /**
   * Get JSON response from backend
   */
  async getJson<T>(endpoint: string): Promise<T> {
    const response = await this.forwardRequest(endpoint, { method: 'GET' });

    if (!response.ok) {
      throw new BackendApiError(
        `Backend error: ${response.status} ${response.statusText}`,
        response.status,
      );
    }

    return response.json();
  }

  /**
   * Post JSON data to backend
   */
  async postJson<T>(endpoint: string, data: any): Promise<T> {
    const response = await this.forwardRequest(endpoint, {
      method: 'POST',
      body: data,
    });

    if (!response.ok) {
      throw new BackendApiError(
        `Backend error: ${response.status} ${response.statusText}`,
        response.status,
      );
    }

    return response.json();
  }

  /**
   * Batch multiple GET requests
   */
  async batchGet<T>(endpoints: string[]): Promise<T[]> {
    const promises = endpoints.map((endpoint) =>
      this.getJson<T>(endpoint).catch((error) => {
        console.error(
          `[API Proxy] Batch request failed for ${endpoint}:`,
          error,
        );
        return null;
      }),
    );

    const results = await Promise.allSettled(promises);
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        if (result.value === null) {
          throw new BackendApiError(
            `Batch request failed for ${endpoints[index]}: Request failed and returned null`,
          );
        }
        return result.value;
      } else {
        throw new BackendApiError(
          `Batch request failed for ${endpoints[index]}: ${result.reason}`,
        );
      }
    });
  }
}

// Singleton instance for server-side use
export const backendApiProxy = new BackendApiProxy();
