import { useState, useEffect, useCallback } from 'react';

export interface ConnectionStatusResult {
  isOnline: boolean;
  isConnected: boolean;
  lastCheck: string | null;
  checkConnection: () => Promise<boolean>;
}

export interface ConnectionStatusConfig {
  checkInterval?: number;
  endpoint?: string;
  timeout?: number;
}

export function useConnectionStatus(
  config: ConnectionStatusConfig = {},
): ConnectionStatusResult {
  const {
    checkInterval = 30000, // 30 seconds
    endpoint = '/api/health',
    timeout = 5000,
  } = config;

  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isConnected, setIsConnected] = useState(false);
  const [lastCheck, setLastCheck] = useState<string | null>(null);

  const checkConnection = useCallback(async (): Promise<boolean> => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(endpoint, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);
      const connected = response.ok;

      setIsConnected(connected);
      setLastCheck(new Date().toISOString());

      return connected;
    } catch (error) {
      setIsConnected(false);
      setLastCheck(new Date().toISOString());
      return false;
    }
  }, [endpoint, timeout]);

  // Listen to online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      checkConnection();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setIsConnected(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [checkConnection]);

  // Periodic connection checks
  useEffect(() => {
    if (!isOnline) return;

    // Initial check
    checkConnection();

    const interval = setInterval(checkConnection, checkInterval);

    return () => clearInterval(interval);
  }, [checkConnection, checkInterval, isOnline]);

  return {
    isOnline,
    isConnected,
    lastCheck,
    checkConnection,
  };
}
