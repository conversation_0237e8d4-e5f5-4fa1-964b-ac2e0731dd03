import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface HistogramData {
  value: number;
}

export interface HistogramProps {
  data: HistogramData[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  color?: string;
  bins?: number;
  className?: string;
  xLabel?: string;
  yLabel?: string;
}

export const Histogram: React.FC<HistogramProps> = ({
  data,
  width = 400,
  height = 300,
  margin = { top: 20, right: 20, bottom: 40, left: 40 },
  color = '#3b82f6',
  bins = 20,
  className = '',
  xLabel,
  yLabel,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const values = data.map((d: HistogramData) => d.value);
    const extent = d3.extent(values) as [number, number];

    const xScale = d3.scaleLinear().domain(extent).range([0, innerWidth]);

    const histogram = d3
      .histogram()
      .value((d: number) => d)
      .domain(xScale.domain() as [number, number])
      .thresholds(bins);

    const binData = histogram(values);

    const yScale = d3
      .scaleLinear()
      .domain([
        0,
        d3.max(binData, (d: d3.Bin<number, number>) => d.length) || 0,
      ])
      .range([innerHeight, 0]);

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add bars
    g.selectAll('.bar')
      .data(binData)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', (d: d3.Bin<number, number>) => xScale(d.x0!))
      .attr('width', (d: d3.Bin<number, number>) =>
        Math.max(0, xScale(d.x1!) - xScale(d.x0!) - 1),
      )
      .attr('y', (d: d3.Bin<number, number>) => yScale(d.length))
      .attr(
        'height',
        (d: d3.Bin<number, number>) => innerHeight - yScale(d.length),
      )
      .attr('fill', color)
      .style('cursor', 'pointer')
      .on('mouseover', function (event, d: d3.Bin<number, number>) {
        d3.select(this).attr('opacity', 0.8);

        // Add tooltip
        const tooltip = d3
          .select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip
          .html(
            `Range: ${d.x0?.toFixed(2)} - ${d.x1?.toFixed(2)}<br/>Count: ${d.length}`,
          )
          .style('left', event.pageX + 10 + 'px')
          .style('top', event.pageY - 10 + 'px');
      })
      .on('mouseout', function () {
        d3.select(this).attr('opacity', 1);
        d3.selectAll('.tooltip').remove();
      });

    // Add x-axis
    const xAxis = g
      .append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    xAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    xAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add y-axis
    const yAxis = g.append('g').call(d3.axisLeft(yScale));

    yAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    yAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add x-axis label
    if (xLabel) {
      g.append('text')
        .attr(
          'transform',
          `translate(${innerWidth / 2}, ${innerHeight + margin.bottom - 5})`,
        )
        .style('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text(xLabel);
    }

    // Add y-axis label
    if (yLabel) {
      g.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('y', 0 - margin.left)
        .attr('x', 0 - innerHeight / 2)
        .attr('dy', '1em')
        .style('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text(yLabel);
    }
  }, [data, width, height, margin, color, bins, xLabel, yLabel]);

  return (
    <div className={className}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ display: 'block' }}
      />
    </div>
  );
};
