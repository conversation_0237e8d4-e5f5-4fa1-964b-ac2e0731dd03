/**
 * Server-side configuration for API proxy
 * This configuration is only available on the server-side
 */

export interface ServerConfig {
  backendUrl: string;
  backendWsUrl: string;
  timeout: number;
  maxRetries: number;
  retryDelay: number;
}

/**
 * Get server-side configuration for backend communication
 * Uses regular environment variables (not NEXT_PUBLIC_)
 */
export function getServerConfig(): ServerConfig {
  const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:4001';
  const backendWsUrl = process.env.WS_URL || 'ws://localhost:4001/ws'; // Fixed: include /ws path
  const timeout = parseInt(process.env.BACKEND_API_TIMEOUT || '10000', 10);
  const maxRetries = parseInt(process.env.BACKEND_API_MAX_RETRIES || '3', 10);
  const retryDelay = parseInt(
    process.env.BACKEND_API_RETRY_DELAY || '1000',
    10,
  );

  return {
    backendUrl,
    backendWsUrl,
    timeout,
    maxRetries,
    retryDelay,
  };
}

/**
 * Validate server-side environment variables
 */
export function validateServerEnvironment(): void {
  const required = ['BACKEND_API_URL'];
  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    console.warn(
      'Missing server environment variables (using defaults):',
      missing,
    );
  }
}
