# System Overview

The Telesoft UI system is a modern, scalable monorepo containing both frontend and backend components designed for intelligence and analytics applications.

## System Architecture

```mermaid
graph TB
    subgraph "Frontend Applications"
        FE1[IntsOc Frontend]
        FE2[Other Frontend Apps]
    end

    subgraph "Backend Services"
        BE1[IntsOc Backend]
        BE2[Other Backend Services]
    end

    subgraph "Shared Packages"
        UI[UI Components]
        TYPES[Common Types]
        CONFIG[Config Packages]
        BRAND[Branding]
        D3[D3 Components]
    end

    subgraph "External Services"
        DB[(Database)]
        API[External APIs]
        AUTH[Authentication]
    end

    FE1 --> UI
    FE1 --> TYPES
    FE1 --> BRAND
    FE1 --> D3
    FE2 --> UI
    FE2 --> TYPES

    FE1 --> BE1
    FE2 --> BE2

    BE1 --> DB
    BE1 --> API
    BE1 --> AUTH
    BE2 --> DB

    UI --> CONFIG
    TYPES --> CONFIG
```

## Component Architecture

```mermaid
graph LR
    subgraph "Design System"
        DS[Design Tokens]
        CP[Color Palette]
        BR[Branding]
    end

    subgraph "UI Components"
        BC[Base Components]
        CC[Composite Components]
        DC[Data Components]
    end

    subgraph "Data Visualization"
        D3C[D3 Charts]
        VIZ[Visualization Utils]
    end

    DS --> BC
    CP --> BC
    BR --> BC

    BC --> CC
    BC --> DC

    D3C --> VIZ
    DC --> D3C
```

## Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Database
    participant External API

    User->>Frontend: User Interaction
    Frontend->>Backend: API Request
    Backend->>Database: Query Data
    Database-->>Backend: Return Data
    Backend->>External API: Fetch Additional Data
    External API-->>Backend: Return Data
    Backend-->>Frontend: Aggregated Response
    Frontend-->>User: Updated UI
```

## Key Design Principles

1. **Modularity**: Each package has a single responsibility and clear interfaces
2. **Reusability**: Components and utilities are designed for reuse across applications
3. **Type Safety**: TypeScript throughout the stack ensures robust development
4. **Performance**: Optimized bundle sizes and efficient data handling
5. **Scalability**: Architecture supports adding new applications and services

## Technology Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, TypeScript
- **Build System**: Turbo (monorepo), tsup (bundling)
- **Package Management**: pnpm
- **Documentation**: Docusaurus with Mermaid diagrams
