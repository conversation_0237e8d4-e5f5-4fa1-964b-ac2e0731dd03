@theme {
  /* Primary brand colors - Modern blue with enhanced vibrancy */
  --color-primary-50: #ebf8ff;
  --color-primary-100: #d1f0ff;
  --color-primary-200: #a3e0ff;
  --color-primary-300: #70ccff;
  --color-primary-400: #3db5ff;
  --color-primary-500: #1aa1dc;
  --color-primary-600: #0d8bc9;
  --color-primary-700: #0a6fa3;
  --color-primary-800: #08547d;
  --color-primary-900: #053a57;
  --color-primary-950: #02202f;

  /* Cyber matrix colors - Modern emerald with enhanced depth */
  --color-cyber-matrix-50: #f0fdf5;
  --color-cyber-matrix-100: #dcfce8;
  --color-cyber-matrix-200: #bbf7d1;
  --color-cyber-matrix-300: #86efad;
  --color-cyber-matrix-400: #4ade80;
  --color-cyber-matrix-500: #10b981;
  --color-cyber-matrix-600: #059669;
  --color-cyber-matrix-700: #047857;
  --color-cyber-matrix-800: #065f46;
  --color-cyber-matrix-900: #064e3b;

  /* Cyber warning colors - Modern amber with enhanced warmth */
  --color-cyber-warning-50: #fffbeb;
  --color-cyber-warning-100: #fef3c7;
  --color-cyber-warning-200: #fde68a;
  --color-cyber-warning-300: #fcd34d;
  --color-cyber-warning-400: #fbbf24;
  --color-cyber-warning-500: #f59e0b;
  --color-cyber-warning-600: #d97706;
  --color-cyber-warning-700: #b45309;
  --color-cyber-warning-800: #92400e;
  --color-cyber-warning-900: #78350f;

  /* Cyber amber colors - Modern orange-amber with enhanced intensity */
  --color-cyber-amber-50: #fff7ed;
  --color-cyber-amber-100: #ffedd5;
  --color-cyber-amber-200: #fed7aa;
  --color-cyber-amber-300: #fdba74;
  --color-cyber-amber-400: #fb923c;
  --color-cyber-amber-500: #f97316;
  --color-cyber-amber-600: #ea580c;
  --color-cyber-amber-700: #c2410c;
  --color-cyber-amber-800: #9a3412;
  --color-cyber-amber-900: #7c2d12;

  /* Cyber danger colors - Modern red with enhanced intensity */
  --color-cyber-danger-50: #fef2f2;
  --color-cyber-danger-100: #fee2e2;
  --color-cyber-danger-200: #fecaca;
  --color-cyber-danger-300: #fca5a5;
  --color-cyber-danger-400: #f87171;
  --color-cyber-danger-500: #ef4444;
  --color-cyber-danger-600: #dc2626;
  --color-cyber-danger-700: #b91c1c;
  --color-cyber-danger-800: #991b1b;
  --color-cyber-danger-900: #7f1d1d;

  /* Background colors - Modern dark theme with enhanced depth */
  --color-background-primary: #0a0d14;
  --color-background-secondary: #0f1419;
  --color-background-tertiary: #161b22;
  --color-background-hover: #1c212b;
  --color-background-active: #21262d;
  --color-background-elevated: #262c36;

  /* Text colors - Modern hierarchy with enhanced readability */
  --color-text-primary: #f6f8fa;
  --color-text-secondary: #d1d9e0;
  --color-text-muted: #8b949e;
  --color-text-subtle: #6e7681;
  --color-text-accent: #1aa1dc;
  --color-text-success: #10b981;
  --color-text-warning: #f59e0b;
  --color-text-amber: #f97316;
  --color-text-danger: #ef4444;

  /* Border colors - Modern borders with enhanced definition */
  --color-border-primary: #30363d;
  --color-border-secondary: #3d444d;
  --color-border-muted: #21262d;
  --color-border-subtle: #161b22;
  --color-border-accent: #1aa1dc;
  --color-border-success: #10b981;
  --color-border-warning: #f59e0b;
  --color-border-amber: #f97316;
  --color-border-danger: #ef4444;

  /* Slate colors - Modern neutrals with enhanced sophistication */
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-200: #e2e8f0;
  --color-slate-300: #cbd5e1;
  --color-slate-400: #94a3b8;
  --color-slate-500: #64748b;
  --color-slate-600: #475569;
  --color-slate-700: #334155;
  --color-slate-800: #1e293b;
  --color-slate-850: #161b22;
  --color-slate-900: #0f1419;
  --color-slate-950: #0a0d14;

  /* Modern accent colors for enhanced visual appeal */
  --color-accent-purple: #8b5cf6;
  --color-accent-purple-light: #a78bfa;
  --color-accent-purple-dark: #7c3aed;
  --color-accent-teal: #14b8a6;
  --color-accent-teal-light: #2dd4bf;
  --color-accent-teal-dark: #0f766e;
  --color-accent-indigo: #6366f1;
  --color-accent-indigo-light: #818cf8;
  --color-accent-indigo-dark: #4f46e5;

  /* Modern surface colors for cards and components */
  --color-surface-primary: #161b22;
  --color-surface-secondary: #21262d;
  --color-surface-tertiary: #30363d;
  --color-surface-hover: #3d444d;
  --color-surface-pressed: #484f58;

  /* Custom box shadows - Enhanced for modern depth */
  --shadow-cyber: 0 0 32px rgb(26 161 220 / 0.25);
  --shadow-cyber-lg: 0 0 48px rgb(26 161 220 / 0.35);
  --shadow-cyber-xl: 0 0 64px rgb(26 161 220 / 0.45);
  --shadow-glow: 0 0 24px rgb(16 185 129 / 0.4);
  --shadow-glow-red: 0 0 24px rgb(239 68 68 / 0.4);
  --shadow-glow-amber: 0 0 24px rgb(249 115 22 / 0.4);
  --shadow-glow-purple: 0 0 24px rgb(139 92 246 / 0.4);
  --shadow-elevation-low:
    0 1px 3px rgb(0 0 0 / 0.12), 0 1px 2px rgb(0 0 0 / 0.24);
  --shadow-elevation-medium:
    0 4px 6px rgb(0 0 0 / 0.07), 0 2px 4px rgb(0 0 0 / 0.06);
  --shadow-elevation-high:
    0 10px 15px rgb(0 0 0 / 0.1), 0 4px 6px rgb(0 0 0 / 0.05);

  /* Animation values - Enhanced for modern interactions */
  --animate-pulse-cyber: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-glow: glow 3s ease-in-out infinite alternate;
  --animate-float: float 6s ease-in-out infinite;
  --animate-slide-up: slide-up 0.3s cubic-bezier(0.16, 1, 0.3, 1);

  /* Modern gradients */
  --gradient-cyber: linear-gradient(135deg, #1aa1dc 0%, #10b981 100%);
  --gradient-cyber-subtle: linear-gradient(
    135deg,
    rgb(26 161 220 / 0.1) 0%,
    rgb(16 185 129 / 0.1) 100%
  );
  --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-amber: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  --gradient-background: linear-gradient(135deg, #0a0d14 0%, #161b22 100%);
}

/* Custom utility classes for Tailwind v4 - Enhanced modern styles - Dark theme */
.dark .cyber-grid-bg,
.cyber-grid-bg {
  background-image:
    linear-gradient(rgba(26, 161, 220, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(26, 161, 220, 0.08) 1px, transparent 1px);
  background-size: 24px 24px;
}

.dark .cyber-grid-bg-dense,
.cyber-grid-bg-dense {
  background-image:
    linear-gradient(rgba(26, 161, 220, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(26, 161, 220, 0.15) 1px, transparent 1px);
  background-size: 16px 16px;
}

.dark .cyber-glow,
.cyber-glow {
  box-shadow: var(--shadow-cyber);
}

.dark .cyber-glow-lg,
.cyber-glow-lg {
  box-shadow: var(--shadow-cyber-lg);
}

.dark .cyber-glow-xl,
.cyber-glow-xl {
  box-shadow: var(--shadow-cyber-xl);
}

.dark .glow-green,
.glow-green {
  box-shadow: var(--shadow-glow);
}

.dark .glow-red,
.glow-red {
  box-shadow: var(--shadow-glow-red);
}

.dark .glow-amber,
.glow-amber {
  box-shadow: var(--shadow-glow-amber);
}

.dark .glow-purple,
.glow-purple {
  box-shadow: var(--shadow-glow-purple);
}

.dark .elevation-low,
.elevation-low {
  box-shadow: var(--shadow-elevation-low);
}

.dark .elevation-medium,
.elevation-medium {
  box-shadow: var(--shadow-elevation-medium);
}

.dark .elevation-high,
.elevation-high {
  box-shadow: var(--shadow-elevation-high);
}

.dark .gradient-cyber,
.gradient-cyber {
  background: var(--gradient-cyber);
}

.dark .gradient-cyber-subtle,
.gradient-cyber-subtle {
  background: var(--gradient-cyber-subtle);
}

.dark .gradient-danger,
.gradient-danger {
  background: var(--gradient-danger);
}

.dark .gradient-warning,
.gradient-warning {
  background: var(--gradient-warning);
}

.dark .gradient-amber,
.gradient-amber {
  background: var(--gradient-amber);
}

.dark .gradient-background,
.gradient-background {
  background: var(--gradient-background);
}

.dark .glass-effect,
.glass-effect {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(22, 27, 34, 0.85);
  border: 1px solid rgba(48, 54, 61, 0.5);
}

.dark .glass-effect-strong,
.glass-effect-strong {
  backdrop-filter: blur(24px) saturate(200%);
  background-color: rgba(22, 27, 34, 0.9);
  border: 1px solid rgba(48, 54, 61, 0.8);
}

@keyframes glow {
  0% {
    box-shadow: 0 0 8px rgb(26 161 220 / 0.4);
  }
  100% {
    box-shadow: 0 0 32px rgb(26 161 220 / 0.8);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom Scrollbar Styling - Cybersecurity Theme */
/* Default/Dark theme scrollbars - Webkit Scrollbars (Chrome, Safari, Edge) */
html:not(.light) ::-webkit-scrollbar,
html.dark ::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

html:not(.light) ::-webkit-scrollbar-track,
html.dark ::-webkit-scrollbar-track,
::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: 4px;
}

html:not(.light) ::-webkit-scrollbar-thumb,
html.dark ::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb {
  background: var(--color-border-primary);
  border-radius: 4px;
  border: 1px solid var(--color-background-secondary);
  transition: all 0.2s ease;
}

html:not(.light) ::-webkit-scrollbar-thumb:hover,
html.dark ::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-accent);
  box-shadow: 0 0 8px rgba(26, 161, 220, 0.3);
}

html:not(.light) ::-webkit-scrollbar-thumb:active,
html.dark ::-webkit-scrollbar-thumb:active,
::-webkit-scrollbar-thumb:active {
  background: var(--color-primary-400);
  box-shadow: 0 0 12px rgba(26, 161, 220, 0.5);
}

html:not(.light) ::-webkit-scrollbar-corner,
html.dark ::-webkit-scrollbar-corner,
::-webkit-scrollbar-corner {
  background: var(--color-background-secondary);
}

/* Firefox Scrollbars - Default/Dark theme */
html:not(.light) *,
html.dark *,
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border-primary) var(--color-background-secondary);
}

/* Light theme scrollbar overrides */
html.light ::-webkit-scrollbar-track {
  background: var(--color-slate-100) !important;
}

html.light ::-webkit-scrollbar-thumb {
  background: var(--color-slate-400) !important;
  border: 1px solid var(--color-slate-100);
}

html.light ::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-accent) !important;
  box-shadow: 0 0 8px rgba(26, 161, 220, 0.3);
}

html.light ::-webkit-scrollbar-thumb:active {
  background: var(--color-primary-400) !important;
  box-shadow: 0 0 12px rgba(26, 161, 220, 0.5);
}

html.light ::-webkit-scrollbar-corner {
  background: var(--color-slate-100) !important;
}

/* Light theme Firefox scrollbars - more specific to avoid modal conflicts */
html.light ::-webkit-scrollbar-track {
  background: var(--color-slate-100) !important;
}

html.light ::-webkit-scrollbar-thumb {
  background: var(--color-slate-400) !important;
  border: 1px solid var(--color-slate-100);
}

html.light ::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-accent) !important;
  box-shadow: 0 0 8px rgba(26, 161, 220, 0.3);
}

html.light ::-webkit-scrollbar-thumb:active {
  background: var(--color-primary-400) !important;
  box-shadow: 0 0 12px rgba(26, 161, 220, 0.5);
}

html.light ::-webkit-scrollbar-corner {
  background: var(--color-slate-100) !important;
}

/* Smooth scrolling for the entire app */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar utilities for specific components */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Enhanced scrollbar for modal content - inherits main page theme colors but thinner */
.modal-scroll::-webkit-scrollbar {
  width: 6px !important;
}

.modal-scroll::-webkit-scrollbar-track {
  background: var(--color-background-secondary) !important;
  border-radius: 3px !important;
}

.modal-scroll::-webkit-scrollbar-thumb {
  background: var(--color-border-primary) !important;
  border-radius: 3px !important;
  border: 1px solid var(--color-background-secondary) !important;
  transition: all 0.2s ease !important;
}

.modal-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-accent) !important;
  box-shadow: 0 0 6px rgba(26, 161, 220, 0.3) !important;
}

.modal-scroll::-webkit-scrollbar-thumb:active {
  background: var(--color-primary-400) !important;
  box-shadow: 0 0 8px rgba(26, 161, 220, 0.4) !important;
}

.modal-scroll::-webkit-scrollbar-corner {
  background: var(--color-background-secondary) !important;
}

/* Table scrollbar styling for horizontal scrolling - Dark theme */
html:not(.light) .overflow-x-auto::-webkit-scrollbar,
html.dark .overflow-x-auto::-webkit-scrollbar,
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

html:not(.light) .overflow-x-auto::-webkit-scrollbar-track,
html.dark .overflow-x-auto::-webkit-scrollbar-track,
.overflow-x-auto::-webkit-scrollbar-track {
  background: var(--color-background-tertiary);
  border-radius: 3px;
}

html:not(.light) .overflow-x-auto::-webkit-scrollbar-thumb,
html.dark .overflow-x-auto::-webkit-scrollbar-thumb,
.overflow-x-auto::-webkit-scrollbar-thumb {
  background: var(--color-border-secondary);
  border-radius: 3px;
  border: 1px solid var(--color-background-tertiary);
}

html:not(.light) .overflow-x-auto::-webkit-scrollbar-thumb:hover,
html.dark .overflow-x-auto::-webkit-scrollbar-thumb:hover,
.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-accent);
  box-shadow: 0 0 6px rgba(26, 161, 220, 0.3);
}

/* Light theme table scrollbars */
html.light .overflow-x-auto::-webkit-scrollbar-track {
  background: var(--color-slate-200) !important;
}

html.light .overflow-x-auto::-webkit-scrollbar-thumb {
  background: var(--color-slate-400) !important;
  border: 1px solid var(--color-slate-200);
}

html.light .overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-accent) !important;
  box-shadow: 0 0 6px rgba(26, 161, 220, 0.3);
}
