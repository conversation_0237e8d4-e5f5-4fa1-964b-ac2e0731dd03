{"name": "@telesoft/jest-config", "version": "0.0.0", "private": true, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./test-utils": {"types": "./dist/test-utils.d.ts", "import": "./dist/test-utils.js", "require": "./dist/test-utils.cjs"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "format": "prettier --write . --ignore-path ../../../.prettierignore"}, "files": ["dist", "README.md"], "peerDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.0.0", "jest": "^29.0.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@telesoft/typescript-config": "workspace:*", "@testing-library/jest-dom": "catalog:testing", "@types/jest": "catalog:testing", "@types/react": "catalog:typescript-5", "tsup": "catalog:build", "typescript": "catalog:typescript-5"}, "description": "Shared Jest configuration and testing utilities for the Telesoft UI monorepo.", "dependencies": {"jest": "catalog:testing", "react": "catalog:react-18", "react-dom": "catalog:react-18"}, "keywords": [], "author": "", "license": "ISC"}