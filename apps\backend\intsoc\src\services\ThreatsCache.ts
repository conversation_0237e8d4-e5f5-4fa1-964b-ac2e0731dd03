import { CacheService } from '@telesoft-ui/redis';
import type { RedisConfig, RedisConnectionOptions } from '@telesoft-ui/redis';
import config from '../config';

interface ThreatIncident {
  uid: string;
  incident_type: string;
  risk_severity: string;
  investigation_status: string;
  timestamp: number;
}

interface ThreatsData {
  incidents: ThreatIncident[];
  [key: string]: unknown;
}

/**
 * Threats-specific cache wrapper that manages its own CacheService instance
 */
export class ThreatsCache {
  private cache: CacheService;
  private readonly THREATS_KEY = 'threats:latest';

  constructor(connectionOptions?: RedisConnectionOptions) {
    const redisConfig: RedisConfig = {
      host: config.redis.host,
      port: config.redis.port,
      db: config.redis.db,
      password: config.redis.password,
      connectTimeout: config.redis.connectTimeout,
      commandTimeout: config.redis.commandTimeout,
    };
    this.cache = new CacheService(redisConfig, connectionOptions);
  }

  /**
   * Initialize the cache service
   */
  async initialize(): Promise<void> {
    await this.cache.initialize();
  }

  async getThreatsData(): Promise<unknown | null> {
    return this.cache.get(this.THREATS_KEY);
  }

  async storeThreatsData(
    data: unknown,
    ttlSeconds: number = 3600,
  ): Promise<boolean> {
    return this.cache.set(this.THREATS_KEY, data, ttlSeconds);
  } /**
   * Append new threats data to existing cache, merging incidents and avoiding duplicates.
   * This method is designed for incremental updates (e.g., from WebSocket streams)
   * where new data should be merged with existing data rather than replacing it.
   * Deduplication is performed based on the incident's uid field.
   */
  async appendThreatsData(
    newData: unknown,
    ttlSeconds: number = 3600,
  ): Promise<boolean> {
    try {
      // Get existing data
      const existingData = await this.cache.get(this.THREATS_KEY);

      // Validate new data structure
      if (!newData || typeof newData !== 'object') {
        console.warn('ThreatsCache: Invalid new data format for append');
        return false;
      }

      const newDataObj = newData as ThreatsData;
      if (!newDataObj.incidents || !Array.isArray(newDataObj.incidents)) {
        console.warn('ThreatsCache: New data missing incidents array');
        return false;
      }

      let mergedData: ThreatsData;

      if (!existingData) {
        // No existing data, just store the new data
        mergedData = newDataObj;
      } else {
        // Merge with existing data
        const existingDataObj = existingData as ThreatsData;

        if (
          !existingDataObj.incidents ||
          !Array.isArray(existingDataObj.incidents)
        ) {
          // Existing data is malformed, replace with new data
          mergedData = newDataObj;
        } else {
          // Create a map for deduplication based on uid
          const incidentsMap = new Map<string, ThreatIncident>();

          // Add existing incidents to map
          existingDataObj.incidents.forEach((incident: ThreatIncident) => {
            if (incident.uid) {
              incidentsMap.set(incident.uid, incident);
            }
          });

          // Add new incidents to map (will overwrite duplicates)
          newDataObj.incidents.forEach((incident: ThreatIncident) => {
            if (incident.uid) {
              incidentsMap.set(incident.uid, incident);
            }
          });

          // Create merged data with deduplicated incidents
          mergedData = {
            ...existingDataObj,
            ...newDataObj,
            incidents: Array.from(incidentsMap.values()),
          };
        }
      }

      // Store the merged data
      return await this.cache.set(this.THREATS_KEY, mergedData, ttlSeconds);
    } catch (error) {
      console.error('ThreatsCache: Error appending threats data:', error);
      return false;
    }
  }

  async hasThreatsData(): Promise<boolean> {
    return this.cache.exists(this.THREATS_KEY);
  }

  /**
   * Check if threats data exists and is not expired
   * Returns true only if data exists and has not expired
   * Returns false if cache is disconnected, key doesn't exist, or key is expired
   */
  async hasValidThreatsData(): Promise<boolean> {
    try {
      // First check if cache is connected
      const connectionStatus = this.cache.getStatus();
      if (connectionStatus !== 'connected') {
        console.log(
          'ThreatsCache: Cache not connected, cannot check for valid data',
        );
        return false;
      }

      const redisClient = this.cache.getClient();
      const ttl = await redisClient.ttl(this.THREATS_KEY);
      // TTL > 0 means key exists and has time left
      // TTL = -1 means key exists but has no expiration (shouldn't happen with our setup)
      // TTL = -2 means key doesn't exist
      const hasValidData = ttl > 0 || ttl === -1;

      if (!hasValidData) {
        console.log('ThreatsCache: No valid cached data (TTL:', ttl, ')');
      }

      return hasValidData;
    } catch (error) {
      console.error('ThreatsCache: Error checking TTL:', error);
      return false;
    }
  }

  async clearThreatsData(): Promise<boolean> {
    return this.cache.delete(this.THREATS_KEY);
  }

  getConnectionStatus(): string {
    return this.cache.getStatus();
  }

  async healthCheck(): Promise<boolean> {
    return this.cache.healthCheck();
  }

  async getStats() {
    return this.cache.getStats();
  }

  cleanup(): void {
    this.cache.cleanup();
  }
}
