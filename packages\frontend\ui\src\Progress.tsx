import * as React from 'react';

export interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number;
  max?: number;
  variant?: 'default' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  label?: string;
}

export const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  (
    {
      className = '',
      value = 0,
      max = 100,
      variant = 'default',
      size = 'md',
      showValue = false,
      label,
      ...props
    },
    ref,
  ) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

    const baseClasses = 'relative w-full overflow-hidden rounded-full';
    const backgroundClasses = 'bg-slate-800';

    const sizeClasses = {
      sm: 'h-2',
      md: 'h-3',
      lg: 'h-4',
    };

    const variantClasses = {
      default: 'bg-primary-500',
      success: 'bg-cyber-matrix-500',
      warning: 'bg-cyber-warning-500',
      danger: 'bg-cyber-danger-500',
    };

    const containerClasses =
      `${baseClasses} ${backgroundClasses} ${sizeClasses[size]} ${className}`.trim();
    const progressClasses = `h-full transition-all duration-500 ease-out ${variantClasses[variant]}`;

    return (
      <div className="w-full space-y-2">
        {(label || showValue) && (
          <div className="flex justify-between text-sm text-text-secondary">
            {label && <span>{label}</span>}
            {showValue && <span>{Math.round(percentage)}%</span>}
          </div>
        )}
        <div ref={ref} className={containerClasses} {...props}>
          <div
            className={progressClasses}
            style={{ width: `${percentage}%` }}
            role="progressbar"
            aria-valuenow={value}
            aria-valuemin={0}
            aria-valuemax={max}
          />
        </div>
      </div>
    );
  },
);

Progress.displayName = 'Progress';
