/* Light Theme Color Palette for Tailwind v4 */

/* Light theme variables - scoped to .light class */
.light {
  color-scheme: light;
  /* Primary brand colors - Modern blue with enhanced vibrancy (same as dark) */
  --color-primary-50: #ebf8ff;
  --color-primary-100: #d1f0ff;
  --color-primary-200: #a3e0ff;
  --color-primary-300: #70ccff;
  --color-primary-400: #3db5ff;
  --color-primary-500: #1aa1dc;
  --color-primary-600: #0d8bc9;
  --color-primary-700: #0a6fa3;
  --color-primary-800: #08547d;
  --color-primary-900: #053a57;
  --color-primary-950: #02202f;

  /* Cyber matrix colors - Modern emerald with enhanced depth (same as dark) */
  --color-cyber-matrix-50: #f0fdf5;
  --color-cyber-matrix-100: #dcfce8;
  --color-cyber-matrix-200: #bbf7d1;
  --color-cyber-matrix-300: #86efad;
  --color-cyber-matrix-400: #4ade80;
  --color-cyber-matrix-500: #10b981;
  --color-cyber-matrix-600: #059669;
  --color-cyber-matrix-700: #047857;
  --color-cyber-matrix-800: #065f46;
  --color-cyber-matrix-900: #064e3b;

  /* Cyber warning colors - Modern amber with enhanced warmth (same as dark) */
  --color-cyber-warning-50: #fffbeb;
  --color-cyber-warning-100: #fef3c7;
  --color-cyber-warning-200: #fde68a;
  --color-cyber-warning-300: #fcd34d;
  --color-cyber-warning-400: #fbbf24;
  --color-cyber-warning-500: #f59e0b;
  --color-cyber-warning-600: #d97706;
  --color-cyber-warning-700: #b45309;
  --color-cyber-warning-800: #92400e;
  --color-cyber-warning-900: #78350f;

  /* Cyber amber colors - Modern orange-amber with enhanced intensity (same as dark) */
  --color-cyber-amber-50: #fff7ed;
  --color-cyber-amber-100: #ffedd5;
  --color-cyber-amber-200: #fed7aa;
  --color-cyber-amber-300: #fdba74;
  --color-cyber-amber-400: #fb923c;
  --color-cyber-amber-500: #f97316;
  --color-cyber-amber-600: #ea580c;
  --color-cyber-amber-700: #c2410c;
  --color-cyber-amber-800: #9a3412;
  --color-cyber-amber-900: #7c2d12;

  /* Cyber danger colors - Modern red with enhanced intensity (same as dark) */
  --color-cyber-danger-50: #fef2f2;
  --color-cyber-danger-100: #fee2e2;
  --color-cyber-danger-200: #fecaca;
  --color-cyber-danger-300: #fca5a5;
  --color-cyber-danger-400: #f87171;
  --color-cyber-danger-500: #ef4444;
  --color-cyber-danger-600: #dc2626;
  --color-cyber-danger-700: #b91c1c;
  --color-cyber-danger-800: #991b1b;
  --color-cyber-danger-900: #7f1d1d;

  /* Background colors - Modern light theme with enhanced clarity */
  --color-background-primary: #ffffff;
  --color-background-secondary: #f8fafc;
  --color-background-tertiary: #f1f5f9;
  --color-background-hover: #e2e8f0;
  --color-background-active: #cbd5e1;
  --color-background-elevated: #ffffff;

  /* Text colors - Modern hierarchy with enhanced readability for light theme */
  --color-text-primary: #0f172a;
  --color-text-secondary: #334155;
  --color-text-muted: #64748b;
  --color-text-subtle: #94a3b8;
  --color-text-accent: #0d8bc9;
  --color-text-success: #059669;
  --color-text-warning: #d97706;
  --color-text-amber: #ea580c;
  --color-text-danger: #dc2626;

  /* Border colors - Modern borders with enhanced definition for light theme */
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-muted: #f1f5f9;
  --color-border-subtle: #f8fafc;
  --color-border-accent: #0d8bc9;
  --color-border-success: #059669;
  --color-border-warning: #d97706;
  --color-border-amber: #ea580c;
  --color-border-danger: #dc2626;

  /* Slate colors - Modern neutrals with enhanced sophistication (inverted for light) */
  --color-slate-50: #0a0d14;
  --color-slate-100: #0f1419;
  --color-slate-200: #161b22;
  --color-slate-300: #21262d;
  --color-slate-400: #475569;
  --color-slate-500: #64748b;
  --color-slate-600: #94a3b8;
  --color-slate-700: #cbd5e1;
  --color-slate-800: #e2e8f0;
  --color-slate-850: #f1f5f9;
  --color-slate-900: #f8fafc;
  --color-slate-950: #ffffff;

  /* Modern accent colors for enhanced visual appeal (slightly adjusted for light theme) */
  --color-accent-purple: #7c3aed;
  --color-accent-purple-light: #8b5cf6;
  --color-accent-purple-dark: #6d28d9;
  --color-accent-teal: #0f766e;
  --color-accent-teal-light: #14b8a6;
  --color-accent-teal-dark: #0d9488;
  --color-accent-indigo: #4f46e5;
  --color-accent-indigo-light: #6366f1;
  --color-accent-indigo-dark: #4338ca;

  /* Modern surface colors for cards and components (light theme) */
  --color-surface-primary: #ffffff;
  --color-surface-secondary: #f8fafc;
  --color-surface-tertiary: #f1f5f9;
  --color-surface-hover: #e2e8f0;
  --color-surface-pressed: #cbd5e1;

  /* Custom box shadows - Enhanced for light theme depth */
  --shadow-cyber: 0 0 32px rgb(26 161 220 / 0.15);
  --shadow-cyber-lg: 0 0 48px rgb(26 161 220 / 0.2);
  --shadow-cyber-xl: 0 0 64px rgb(26 161 220 / 0.25);
  --shadow-glow: 0 0 24px rgb(16 185 129 / 0.25);
  --shadow-glow-red: 0 0 24px rgb(239 68 68 / 0.25);
  --shadow-glow-amber: 0 0 24px rgb(249 115 22 / 0.25);
  --shadow-glow-purple: 0 0 24px rgb(139 92 246 / 0.25);
  --shadow-elevation-low:
    0 1px 3px rgb(0 0 0 / 0.08), 0 1px 2px rgb(0 0 0 / 0.12);
  --shadow-elevation-medium:
    0 4px 6px rgb(0 0 0 / 0.05), 0 2px 4px rgb(0 0 0 / 0.04);
  --shadow-elevation-high:
    0 10px 15px rgb(0 0 0 / 0.08), 0 4px 6px rgb(0 0 0 / 0.04);

  /* Animation values - Enhanced for modern interactions (same as dark) */
  --animate-pulse-cyber: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-glow: glow 3s ease-in-out infinite alternate;
  --animate-float: float 6s ease-in-out infinite;
  --animate-slide-up: slide-up 0.3s cubic-bezier(0.16, 1, 0.3, 1);

  /* Modern gradients for light theme */
  --gradient-cyber: linear-gradient(135deg, #1aa1dc 0%, #10b981 100%);
  --gradient-cyber-subtle: linear-gradient(
    135deg,
    rgb(26 161 220 / 0.05) 0%,
    rgb(16 185 129 / 0.05) 100%
  );
  --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-amber: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  --gradient-background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* Custom utility classes for Tailwind v4 - Light theme styles */
.light .cyber-grid-bg {
  background-image:
    linear-gradient(rgba(26, 161, 220, 0.12) 1px, transparent 1px),
    linear-gradient(90deg, rgba(26, 161, 220, 0.12) 1px, transparent 1px);
  background-size: 24px 24px;
}

.light .cyber-grid-bg-dense {
  background-image:
    linear-gradient(rgba(26, 161, 220, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(26, 161, 220, 0.15) 1px, transparent 1px);
  background-size: 16px 16px;
}

.light .cyber-glow {
  box-shadow: var(--shadow-cyber);
}

.light .cyber-glow-lg {
  box-shadow: var(--shadow-cyber-lg);
}

.light .cyber-glow-xl {
  box-shadow: var(--shadow-cyber-xl);
}

.light .glow-green {
  box-shadow: var(--shadow-glow);
}

.light .glow-red {
  box-shadow: var(--shadow-glow-red);
}

.light .glow-amber {
  box-shadow: var(--shadow-glow-amber);
}

.light .glow-purple {
  box-shadow: var(--shadow-glow-purple);
}

.light .elevation-low {
  box-shadow: var(--shadow-elevation-low);
}

.light .elevation-medium {
  box-shadow: var(--shadow-elevation-medium);
}

.light .elevation-high {
  box-shadow: var(--shadow-elevation-high);
}

.light .gradient-cyber {
  background: var(--gradient-cyber);
}

.light .gradient-cyber-subtle {
  background: var(--gradient-cyber-subtle);
}

.light .gradient-danger {
  background: var(--gradient-danger);
}

.light .gradient-warning {
  background: var(--gradient-warning);
}

.light .gradient-amber {
  background: var(--gradient-amber);
}

.light .gradient-background {
  background: var(--gradient-background);
}

.light .glass-effect {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.light .glass-effect-strong {
  backdrop-filter: blur(24px) saturate(200%);
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

@keyframes glow {
  0% {
    box-shadow: 0 0 8px rgb(26 161 220 / 0.25);
  }
  100% {
    box-shadow: 0 0 32px rgb(26 161 220 / 0.45);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
