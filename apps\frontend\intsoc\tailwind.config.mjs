/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    '../../../packages/frontend/ui/**/*.{js,ts,jsx,tsx}',
    '../../../packages/frontend/ui/src/**/*.{js,ts,jsx,tsx}',
    '../../../packages/frontend/branding/**/*.{js,ts,jsx,tsx}',
    '../../../packages/frontend/branding/src/**/*.{js,ts,jsx,tsx}',
    '../../../packages/frontend/d3/**/*.{js,ts,jsx,tsx}',
    '../../../packages/frontend/d3/src/**/*.{js,ts,jsx,tsx}',
  ],
  safelist: [
    // Ensure cyber-amber colors are always included
    'bg-cyber-amber-50',
    'bg-cyber-amber-100',
    'bg-cyber-amber-200',
    'bg-cyber-amber-300',
    'bg-cyber-amber-400',
    'bg-cyber-amber-500',
    'bg-cyber-amber-600',
    'bg-cyber-amber-700',
    'bg-cyber-amber-800',
    'bg-cyber-amber-900',
    'hover:bg-cyber-amber-50',
    'hover:bg-cyber-amber-100',
    'hover:bg-cyber-amber-200',
    'hover:bg-cyber-amber-300',
    'hover:bg-cyber-amber-400',
    'hover:bg-cyber-amber-500',
    'hover:bg-cyber-amber-600',
    'hover:bg-cyber-amber-700',
    'hover:bg-cyber-amber-800',
    'hover:bg-cyber-amber-900',
    'text-text-amber',
    'border-border-amber',
    'glow-amber',
    'gradient-amber',
  ],
};
