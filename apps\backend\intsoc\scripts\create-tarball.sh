#!/bin/bash

# Create production tarball for Intsoc Backend
# Can be run from either the app directory or monorepo root

set -e

# Make this script executable if it isn't already
if [ ! -x "$0" ]; then
    chmod +x "$0"
fi

# Detect if we're running from monorepo root or app directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="$(dirname "$SCRIPT_DIR")"
MONOREPO_ROOT="$(cd "$APP_DIR/../../../" && pwd)"

# Determine working directory
if [[ "$PWD" == "$MONOREPO_ROOT" ]]; then
    echo "🏠 Running from monorepo root"
    WORKING_DIR="$APP_DIR"
    IS_MONOREPO_ROOT=true
else
    echo "📁 Running from app directory"
    WORKING_DIR="$PWD"
    IS_MONOREPO_ROOT=false
fi

APP_NAME="intsoc-backend"
VERSION=$(node -p "require('$WORKING_DIR/package.json').version")
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
TARBALL_NAME="${APP_NAME}-${VERSION}-${TIMESTAMP}.tar.gz"
DIST_DIR="$WORKING_DIR/dist"

echo "📦 Creating production tarball for ${APP_NAME} v${VERSION}..."

# Create dist directory
mkdir -p "$DIST_DIR"

# Create temporary directory for packaging
TEMP_DIR=$(mktemp -d)
PACKAGE_DIR="$TEMP_DIR/$APP_NAME"
mkdir -p "$PACKAGE_DIR"

echo "🏗️  Preparing package contents..."

# Change to app directory for consistent file operations
cd "$WORKING_DIR"

# Verify that the build exists
if [ ! -d "dist" ]; then
    echo "❌ Error: dist directory not found in $WORKING_DIR"
    if [ "$IS_MONOREPO_ROOT" = true ]; then
        echo "💡 Hint: Run 'pnpm run build:intsoc-backend' from monorepo root first"
    else
        echo "💡 Hint: Run 'pnpm build' first"
    fi
    exit 1
fi

# Verify essential build files exist
if [ ! -f "dist/bin/http.bundled.js" ]; then
    echo "❌ Error: Build appears incomplete. Main entry point dist/bin/http.bundled.js not found."
    echo "💡 Make sure to run the full build process that includes bundling."
    exit 1
fi

echo "✅ Build verification passed"

# Copy essential files for production deployment
cp -r dist "$PACKAGE_DIR/"
cp package.json "$PACKAGE_DIR/"
cp tsconfig.json "$PACKAGE_DIR/"

# Copy any environment files if they exist
if [ -f ".env.example" ]; then
    cp .env.example "$PACKAGE_DIR/"
fi

if [ -f ".env.production" ]; then
    cp .env.production "$PACKAGE_DIR/"
fi

# Create a simple README for the package
cat > "$PACKAGE_DIR/README.md" << EOF
# ${APP_NAME} Production Package

This package contains the built backend API ready for production deployment.

## Installation

1. Extract the tarball: \`tar -xzf ${TARBALL_NAME}\`
2. Move .env.example to .env and configure environment variables as needed
3. Install dependencies: \`pnpm install --prod\`
4. Start the application: \`pnpm start\`

## Alternative with npm

If pnpm is not available, you can use npm:
1. Extract the tarball: \`tar -xzf ${TARBALL_NAME}\`
2. Move .env.example to .env and configure environment variables as needed
3. Install dependencies: \`npm install\`
4. Start the application: \`npm start\`

## Dependencies

Redis is required for caching. Ensure Redis is running and accessible.

## Environment

Make sure to set the appropriate environment variables before starting the application, either using a .env file or directly in your environment.

### Required Environment Variables

\`\`\`bash
# Server Configuration
PORT=4001
NODE_ENV=production

# Redis Configuration
# REDIS_URL=redis://localhost:6379

\`\`\`

### Health Check

The application includes a health check endpoint:

\`\`\`bash
# Test if backend is running
curl -f http://localhost:4001/health || echo "Backend not available"
\`\`\`

## Production Process Management

For reliable production deployment, consider using a process manager:

### PM2 (Recommended)

PM2 provides advanced process management with automatic restarts, load balancing, and monitoring.

\`\`\`bash
# Install PM2 globally
npm install -g pm2

# Start the application
pm2 start dist/bin/http.js --name "intsoc-backend"

# Or create a PM2 ecosystem file (ecosystem.config.js):
module.exports = {
  apps: [{
    name: 'intsoc-backend',
    script: './dist/bin/http.js',
    env: {
      NODE_ENV: 'production',
      PORT: 4001
    },
    instances: 1,
    exec_mode: 'fork',
    max_memory_restart: '512M',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log'
  }]
};

# Then start with: pm2 start ecosystem.config.js
\`\`\`

### Forever

A simple process manager for continuous operation.

\`\`\`bash
# Install Forever globally
npm install -g forever

# Start the application
forever start --minUptime 1000 --spinSleepTime 1000 dist/bin/http.js

# List running processes
forever list

# Stop the application
forever stop dist/bin/http.js
\`\`\`

### systemd Service

For Linux systems, create a systemd service for automatic startup and management.

Create \`/etc/systemd/system/intsoc-backend.service\`:

\`\`\`ini
[Unit]
Description=Intsoc Backend API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/extracted/intsoc-backend
ExecStart=/usr/bin/node dist/bin/http.js
Restart=always
RestartSec=5
Environment=NODE_ENV=production
Environment=PORT=4001

[Install]
WantedBy=multi-user.target
\`\`\`

Then enable and start the service:
\`\`\`bash
sudo systemctl enable intsoc-backend
sudo systemctl start intsoc-backend
sudo systemctl status intsoc-backend
\`\`\`

## Notes

- This package contains a pre-built Node.js/Express application
- All workspace dependencies have been bundled into the build output
- Only runtime dependencies need to be installed
- For production environments, always use a process manager for reliability
- Consider setting up log rotation and monitoring for production deployments
- Ensure proper firewall configuration for the API port (default: 4001)

Built on: $(date)
Version: ${VERSION}
EOF

echo "📋 Creating production package.json..."

# Function to get actual version from pnpm list
get_dependency_version() {
    local dep_name="$1"
    local version
    
    # Try different approaches based on context
    if [ "$IS_MONOREPO_ROOT" = true ]; then
        # From monorepo root, use workspace-specific query
        version=$(cd "$MONOREPO_ROOT" && pnpm list "$dep_name" --filter @telesoft/intsoc-backend --depth=0 --json 2>/dev/null | node -e "
            try {
                const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                const version = data[0]?.dependencies?.['$dep_name']?.version;
                console.log(version || 'latest');
            } catch (e) {
                console.log('latest');
            }
        ")
    else
        # From app directory, use local query
        version=$(pnpm list "$dep_name" --depth=0 --json 2>/dev/null | node -e "
            try {
                const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                const version = data[0]?.dependencies?.['$dep_name']?.version;
                console.log(version || 'latest');
            } catch (e) {
                console.log('latest');
            }
        ")
    fi
    
    echo "$version"
}

# Get the actual resolved versions for runtime dependencies
EXPRESS_VERSION=$(get_dependency_version "express")
AXIOS_VERSION=$(get_dependency_version "axios")
COMPRESSION_VERSION=$(get_dependency_version "compression")
DOTENV_VERSION=$(get_dependency_version "dotenv")
HELMET_VERSION=$(get_dependency_version "helmet")
NODE_FETCH_VERSION=$(get_dependency_version "node-fetch")
SYSTEMINFORMATION_VERSION=$(get_dependency_version "systeminformation")
WS_VERSION=$(get_dependency_version "ws")
IOREDIS_VERSION=$(get_dependency_version "ioredis")

# Fallback: if versions are still "latest", try to get from node_modules
for dep in express axios compression dotenv helmet node-fetch systeminformation ws ioredis; do
    var_name="${dep^^}_VERSION"
    var_name="${var_name//-/_}"
    current_version=$(eval echo \$${var_name})
    
    if [ "$current_version" = "latest" ]; then
        if [ -f "node_modules/$dep/package.json" ]; then
            new_version=$(node -p "require('./node_modules/$dep/package.json').version")
            eval "${var_name}=${new_version}"
        elif [ -f "$MONOREPO_ROOT/node_modules/$dep/package.json" ]; then
            new_version=$(node -p "require('$MONOREPO_ROOT/node_modules/$dep/package.json').version")
            eval "${var_name}=${new_version}"
        fi
    fi
done

echo "📋 Resolved versions:"
echo "  - express: $EXPRESS_VERSION"
echo "  - axios: $AXIOS_VERSION"
echo "  - compression: $COMPRESSION_VERSION"
echo "  - dotenv: $DOTENV_VERSION"
echo "  - helmet: $HELMET_VERSION"
echo "  - node-fetch: $NODE_FETCH_VERSION"
echo "  - systeminformation: $SYSTEMINFORMATION_VERSION"
echo "  - ws: $WS_VERSION"
echo "  - ioredis: $IOREDIS_VERSION"

# Create package.json for production (only runtime dependencies needed)
cat > "$PACKAGE_DIR/package.json" << EOF
{
  "name": "@telesoft/intsoc-backend",
  "version": "${VERSION}",
  "description": "Backend API for intsoc application",
  "main": "dist/bin/http.bundled.js",
  "scripts": {
    "start": "node dist/bin/http.bundled.js",
    "postinstall": "echo 'Production package installed successfully'"
  },
  "dependencies": {
    "axios": "${AXIOS_VERSION}",
    "compression": "${COMPRESSION_VERSION}",
    "dotenv": "${DOTENV_VERSION}",
    "express": "${EXPRESS_VERSION}",
    "helmet": "${HELMET_VERSION}",
    "ioredis": "${IOREDIS_VERSION}",
    "node-fetch": "${NODE_FETCH_VERSION}",
    "systeminformation": "${SYSTEMINFORMATION_VERSION}",
    "ws": "${WS_VERSION}"
  },
  "packageManager": "pnpm@$(pnpm --version)",
  "engines": {
    "node": ">=18.0.0"
  }
}
EOF

# Note: TypeScript build process compiles all code into JavaScript
# Workspace dependencies are resolved during build, so we don't need them at runtime

echo "🗜️  Creating tarball..."

# Create the tarball
cd "$TEMP_DIR"
tar -czf "$TARBALL_NAME" "$APP_NAME"

# Move tarball to dist directory
mv "$TARBALL_NAME" "$DIST_DIR/"

# Cleanup
cd "$WORKING_DIR"
rm -rf "$TEMP_DIR"

echo "✅ Production tarball created: $DIST_DIR/$TARBALL_NAME"
echo "📊 Size: $(du -h "$DIST_DIR/$TARBALL_NAME" | cut -f1)"

# List contents of dist directory
echo "📁 Contents of dist directory:"
ls -la "$DIST_DIR/"
