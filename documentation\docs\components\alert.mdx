---
sidebar_position: 5
---

# Alert

The Alert component displays important information to users in a prominent way. It supports different variants for various types of notifications and can include titles and descriptions.

import { Alert, AlertTitle, AlertDescription, Button } from '@telesoft/ui';
import { ComponentShowcase } from '../../src/components/ComponentShowcase';
import { UIThemeProvider } from '../../src/components/UIThemeProvider';

<UIThemeProvider>

## Variants

Alert components come in different variants to convey different types of information and urgency levels.

<ComponentShowcase
  title="Default Alert"
  description="Standard alert for general information and notifications."
  component={
    <Alert variant="default">
      <AlertTitle>Heads up!</AlertTitle>
      <AlertDescription>
        This is a default alert. You can use it for general information.
      </AlertDescription>
    </Alert>
  }
  code={`<Alert variant="default">
  <AlertTitle>Heads up!</AlertTitle>
  <AlertDescription>
    This is a default alert. You can use it for general information.
  </AlertDescription>
</Alert>`}
/>

<ComponentShowcase
  title="Success Alert"
  description="Green alert for positive feedback, completed actions, or success messages."
  component={
    <Alert variant="success">
      <AlertTitle>Success!</AlertTitle>
      <AlertDescription>
        Your changes have been saved successfully.
      </AlertDescription>
    </Alert>
  }
  code={`<Alert variant="success">
  <AlertTitle>Success!</AlertTitle>
  <AlertDescription>
    Your changes have been saved successfully.
  </AlertDescription>
</Alert>`}
/>

<ComponentShowcase
  title="Warning Alert"
  description="Yellow alert for cautionary messages or important notices."
  component={
    <Alert variant="warning">
      <AlertTitle>Warning</AlertTitle>
      <AlertDescription>
        Please review your settings before proceeding.
      </AlertDescription>
    </Alert>
  }
  code={`<Alert variant="warning">
  <AlertTitle>Warning</AlertTitle>
  <AlertDescription>
    Please review your settings before proceeding.
  </AlertDescription>
</Alert>`}
/>

<ComponentShowcase
  title="Danger Alert"
  description="Red alert for errors, critical issues, or destructive actions."
  component={
    <Alert variant="danger">
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>
        Something went wrong. Please try again or contact support.
      </AlertDescription>
    </Alert>
  }
  code={`<Alert variant="danger">
  <AlertTitle>Error</AlertTitle>
  <AlertDescription>
    Something went wrong. Please try again or contact support.
  </AlertDescription>
</Alert>`}
/>

<ComponentShowcase
  title="Info Alert"
  description="Blue alert for informational messages and helpful tips."
  component={
    <Alert variant="info">
      <AlertTitle>Information</AlertTitle>
      <AlertDescription>
        This feature is currently in beta. Your feedback is welcome.
      </AlertDescription>
    </Alert>
  }
  code={`<Alert variant="info">
  <AlertTitle>Information</AlertTitle>
  <AlertDescription>
    This feature is currently in beta. Your feedback is welcome.
  </AlertDescription>
</Alert>`}
/>

## Simple Alerts

Alerts can be used without titles for simpler messages.

<ComponentShowcase
  title="Simple Alert Messages"
  description="Basic alerts with just descriptions for concise notifications."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Alert variant="success">
        <AlertDescription>File uploaded successfully!</AlertDescription>
      </Alert>
      <Alert variant="warning">
        <AlertDescription>Your session will expire in 5 minutes.</AlertDescription>
      </Alert>
      <Alert variant="danger">
        <AlertDescription>Invalid credentials. Please try again.</AlertDescription>
      </Alert>
    </div>
  }
  code={`<Alert variant="success">
  <AlertDescription>File uploaded successfully!</AlertDescription>
</Alert>

<Alert variant="warning">
  <AlertDescription>Your session will expire in 5 minutes.</AlertDescription>
</Alert>

<Alert variant="danger">
  <AlertDescription>Invalid credentials. Please try again.</AlertDescription>
</Alert>
`} />

## Alerts with Actions

Alerts can include interactive elements like buttons for user actions.

<ComponentShowcase
  title="Alert with Action Button"
  description="Alert containing actionable buttons for user interactions."
  component={
    <Alert variant="info">
      <AlertTitle>New Update Available</AlertTitle>
      <AlertDescription>
        Version 2.0 is now available with new features and improvements.
      </AlertDescription>
      <div style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem' }}>
        <Button variant="primary" size="sm">
          Update Now
        </Button>
        <Button variant="ghost" size="sm">
          Later
        </Button>
      </div>
    </Alert>
  }
  code={`<Alert variant="info">
  <AlertTitle>New Update Available</AlertTitle>
  <AlertDescription>
    Version 2.0 is now available with new features and improvements.
  </AlertDescription>
  <div className="mt-4 flex gap-2">
    <Button variant="primary" size="sm">Update Now</Button>
    <Button variant="ghost" size="sm">Later</Button>
  </div>
</Alert>`}
/>

<ComponentShowcase
  title="Dismissible Alert"
  description="Alert with a dismiss button for removable notifications."
  component={
    <Alert variant="warning">
      <AlertTitle>Storage Almost Full</AlertTitle>
      <AlertDescription>
        You're using 85% of your storage quota. Consider upgrading your plan.
      </AlertDescription>
      <div style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem' }}>
        <Button variant="outline" size="sm">
          Upgrade Plan
        </Button>
        <Button variant="ghost" size="sm">
          Dismiss
        </Button>
      </div>
    </Alert>
  }
  code={`<Alert variant="warning">
  <AlertTitle>Storage Almost Full</AlertTitle>
  <AlertDescription>
    You're using 85% of your storage quota. Consider upgrading your plan.
  </AlertDescription>
  <div className="mt-4 flex gap-2">
    <Button variant="outline" size="sm">Upgrade Plan</Button>
    <Button variant="ghost" size="sm">Dismiss</Button>
  </div>
</Alert>`}
/>

## Real-World Examples

<ComponentShowcase
  title="Form Validation Alert"
  description="Alert used for form validation errors."
  component={
    <Alert variant="danger">
      <AlertTitle>Validation Error</AlertTitle>
      <AlertDescription>
        Please correct the following errors:
        <ul style={{ marginTop: '0.5rem', marginLeft: '1rem' }}>
          <li>Email address is required</li>
          <li>Password must be at least 8 characters</li>
          <li>Please accept the terms and conditions</li>
        </ul>
      </AlertDescription>
    </Alert>
  }
  code={`<Alert variant="danger">
  <AlertTitle>Validation Error</AlertTitle>
  <AlertDescription>
    Please correct the following errors:
    <ul className="mt-2 ml-4">
      <li>Email address is required</li>
      <li>Password must be at least 8 characters</li>
      <li>Please accept the terms and conditions</li>
    </ul>
  </AlertDescription>
</Alert>`}
/>

<ComponentShowcase
  title="System Maintenance Alert"
  description="Alert for system maintenance notifications."
  component={
    <Alert variant="info">
      <AlertTitle>Scheduled Maintenance</AlertTitle>
      <AlertDescription>
        The system will be undergoing maintenance on Sunday, June 15th from 2:00
        AM to 4:00 AM EST. Some features may be temporarily unavailable during
        this time.
      </AlertDescription>
    </Alert>
  }
  code={`<Alert variant="info">
  <AlertTitle>Scheduled Maintenance</AlertTitle>
  <AlertDescription>
    The system will be undergoing maintenance on Sunday, June 15th from 2:00 AM to 4:00 AM EST. 
    Some features may be temporarily unavailable during this time.
  </AlertDescription>
</Alert>`}
/>

<ComponentShowcase
  title="Account Security Alert"
  description="Alert for security-related notifications."
  component={
    <Alert variant="warning">
      <AlertTitle>Security Notice</AlertTitle>
      <AlertDescription>
        We detected a login from an unrecognized device. If this wasn't you,
        please change your password immediately.
      </AlertDescription>
      <div style={{ marginTop: '1rem' }}>
        <Button variant="primary" size="sm">
          Change Password
        </Button>
      </div>
    </Alert>
  }
  code={`<Alert variant="warning">
  <AlertTitle>Security Notice</AlertTitle>
  <AlertDescription>
    We detected a login from an unrecognized device. If this wasn't you, 
    please change your password immediately.
  </AlertDescription>
  <div className="mt-4">
    <Button variant="primary" size="sm">Change Password</Button>
  </div>
</Alert>`}
/>

## Layout Examples

<ComponentShowcase
  title="Multiple Alerts"
  description="Multiple alerts displayed in sequence for different types of notifications."
  component={
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem',
        maxWidth: '500px',
      }}
    >
      <Alert variant="success">
        <AlertDescription>Profile updated successfully!</AlertDescription>
      </Alert>
      <Alert variant="info">
        <AlertTitle>Tips</AlertTitle>
        <AlertDescription>
          Add a profile picture to help others recognize you.
        </AlertDescription>
      </Alert>
      <Alert variant="warning">
        <AlertDescription>
          Remember to verify your email address to unlock all features.
        </AlertDescription>
      </Alert>
    </div>
  }
  code={`<div className="space-y-4">
  <Alert variant="success">
    <AlertDescription>Profile updated successfully!</AlertDescription>
  </Alert>
  
  <Alert variant="info">
    <AlertTitle>Tips</AlertTitle>
    <AlertDescription>
      Add a profile picture to help others recognize you.
    </AlertDescription>
  </Alert>
  
  <Alert variant="warning">
    <AlertDescription>
      Remember to verify your email address to unlock all features.
    </AlertDescription>
  </Alert>
</div>`}
/>

</UIThemeProvider>

## Props

### Alert Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'variant',
      type: "'default' | 'success' | 'warning' | 'danger' | 'info'",
      default: "'default'",
      description: 'Visual style variant of the alert',
    },
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description:
        'Alert content (typically AlertTitle and/or AlertDescription)',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

### AlertTitle Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description: 'Title content for the alert',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

### AlertDescription Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description: 'Description content for the alert',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

## Accessibility

The Alert component follows accessibility best practices:

- **ARIA role**: Uses `role="alert"` for screen reader announcements
- **Semantic structure**: Proper heading hierarchy with AlertTitle
- **Color independence**: Doesn't rely solely on color to convey meaning
- **Keyboard navigation**: Content within alerts is accessible via keyboard
- **Screen reader support**: Clear structure and meaningful content

## Best Practices

### Do ✅

- Use appropriate variants to match the message type
- Keep alert messages concise and actionable
- Include clear titles for complex alerts
- Provide action buttons when users can respond
- Use alerts sparingly to avoid alert fatigue
- Position alerts where users expect to see them

### Don't ❌

- Don't overuse alerts as they can become noise
- Don't use alerts for non-critical information
- Don't rely solely on color to convey meaning
- Don't make alerts too long or verbose
- Don't forget to provide a way to dismiss persistent alerts
- Don't use alerts for content that belongs in the main flow

## Common Patterns

### Dismissible Alerts

```tsx
const [isVisible, setIsVisible] = useState(true);

if (!isVisible) return null;

return (
  <Alert variant="info">
    <AlertTitle>Information</AlertTitle>
    <AlertDescription>This is a dismissible alert.</AlertDescription>
    <Button onClick={() => setIsVisible(false)} variant="ghost" size="sm">
      Dismiss
    </Button>
  </Alert>
);
```

### Form Validation Alerts

```tsx
{
  errors.length > 0 && (
    <Alert variant="danger">
      <AlertTitle>Please fix the following errors:</AlertTitle>
      <AlertDescription>
        <ul>
          {errors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
```

### Auto-dismissing Alerts

```tsx
useEffect(() => {
  if (showAlert) {
    const timer = setTimeout(() => {
      setShowAlert(false);
    }, 5000);
    return () => clearTimeout(timer);
  }
}, [showAlert]);
```
