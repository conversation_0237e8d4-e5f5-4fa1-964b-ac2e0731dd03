{"name": "@telesoft/ui", "version": "0.0.0", "private": true, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./styles.css": "./dist/index.css"}, "scripts": {"build": "pnpm run clean && tsup && tailwindcss -i ./src/styles.css -o ./dist/index.css", "dev": "tsup --watch --no-dts & tailwindcss -i ./src/styles.css -o ./dist/index.css --watch", "dev:fast": "tsup --watch --no-dts --no-sourcemap --format esm & tailwindcss -i ./src/styles.css -o ./dist/index.css --watch", "watch": "tsup --watch & tailwindcss -i ./src/styles.css -o ./dist/index.css --watch", "lint": "eslint .", "format": "prettier --write . --ignore-path ../../../.prettierignore", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "files": ["dist", "README.md"], "peerDependencies": {"react": "^18.3.0", "react-dom": "^18.3.0"}, "devDependencies": {"@tailwindcss/cli": "catalog:next-15", "@telesoft/color-palette": "workspace:*", "@telesoft/eslint-config": "workspace:*", "@telesoft/jest-config": "workspace:*", "@telesoft/types": "workspace:*", "@telesoft/typescript-config": "workspace:*", "@testing-library/jest-dom": "catalog:testing", "@testing-library/react": "catalog:testing", "@testing-library/user-event": "catalog:testing", "@types/jest": "catalog:testing", "@types/react": "catalog:typescript-5", "@types/react-dom": "catalog:typescript-5", "autoprefixer": "catalog:next-15", "eslint": "catalog:eslint", "identity-obj-proxy": "^3.0.0", "jest": "catalog:testing", "jest-environment-jsdom": "30.0.0-beta.3", "postcss": "catalog:next-15", "react": "catalog:react-18", "react-dom": "catalog:react-18", "tailwindcss": "catalog:next-15", "ts-jest": "^29.3.4", "tsup": "catalog:build", "typescript": "catalog:typescript-5"}}