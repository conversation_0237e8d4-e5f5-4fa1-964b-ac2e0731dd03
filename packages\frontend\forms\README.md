# @telesoft/forms

Reusable form field components built on top of `@telesoft/ui` and `@telesoft/utils`.

## Overview

This package provides higher-level form components that combine the base UI components with consistent styling, validation patterns, and user experience patterns. It sits between the low-level UI components and application code.

## Components

### NumberField

A number input field with built-in label, helper text, and validation.

```tsx
import { NumberField } from '@telesoft/forms';

<NumberField
  label="Port Number"
  value={port}
  onChange={setPort}
  min="1"
  max="65535"
  helperText="Valid port range: 1-65535"
  error={portError}
/>;
```

### SelectField

A select dropdown with consistent styling and helper text support.

```tsx
import { SelectField } from '@telesoft/forms';

<SelectField
  label="Environment"
  value={environment}
  onChange={setEnvironment}
  options={[
    { value: 'dev', label: 'Development' },
    { value: 'staging', label: 'Staging' },
    { value: 'prod', label: 'Production' },
  ]}
  helperText="Select deployment environment"
/>;
```

### TextField

A text input field with support for different input types.

```tsx
import { TextField } from '@telesoft/forms';

<TextField
  label="Email Address"
  type="email"
  value={email}
  onChange={setEmail}
  placeholder="<EMAIL>"
  helperText="We'll never share your email"
/>;
```

### FormSection

A container component for grouping related form fields with a title and optional description.

```tsx
import { FormSection } from '@telesoft/forms';

<FormSection
  title="Database Configuration"
  description="Configure your database connection settings"
  icon="🗄️"
>
  <TextField label="Host" value={host} onChange={setHost} />
  <NumberField label="Port" value={port} onChange={setPort} />
</FormSection>;
```

## Design Principles

1. **Consistent UX**: All form fields follow the same patterns for labels, helper text, and error states
2. **Accessibility**: Proper label associations and ARIA attributes
3. **Composability**: Components can be easily combined and customized
4. **Type Safety**: Full TypeScript support with proper prop types
5. **Style Integration**: Uses design tokens from `@telesoft/utils` for consistent theming

## Dependencies

- `@telesoft/ui`: Base UI components (Input, Button, etc.)
- `@telesoft/utils`: Style presets and utility functions
- `react`: Peer dependency

## Usage Pattern

The forms package is designed to be used in application code where you need form inputs. It reduces boilerplate and ensures consistency:

**Before (manual approach):**

```tsx
<div className="space-y-2">
  <label className="text-sm font-medium">Port Number</label>
  <input
    type="number"
    className="w-full px-3 py-2 border rounded..."
    value={port}
    onChange={(e) => setPort(parseInt(e.target.value))}
  />
  <p className="text-xs text-gray-500">Valid range: 1-65535</p>
</div>
```

**After (using forms package):**

```tsx
<NumberField
  label="Port Number"
  value={port}
  onChange={setPort}
  min="1"
  max="65535"
  helperText="Valid range: 1-65535"
/>
```
