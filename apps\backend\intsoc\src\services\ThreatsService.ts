import {
  WebSocketConnectionManager,
  ConnectionConfig,
} from './WebSocketConnectionManager';
import { MessageParser, ParsedMessage } from '../utils/MessageParser';
import { ThreatsCache } from './ThreatsCache';
import UnifiedWebSocketService from './UnifiedWebSocketService';
import config from '../config';

export interface ThreatsServiceConfig {
  threatsCache: ThreatsCache;
  cacheTimeout?: number;
  connectionConfig?: Partial<ConnectionConfig>;
}

export interface ThreatsData {
  incidents?: unknown[];
  [key: string]: unknown;
}

/**
 * Simplified threats service using the connection manager
 */
export class ThreatsService {
  private static readonly CONNECTION_ID = 'threats-websocket';
  private static instance: ThreatsService | null = null;

  private connectionManager: WebSocketConnectionManager;
  private threatsCache: ThreatsCache;
  private cacheTimeout: number;
  private isInitialized = false;

  constructor(serviceConfig: ThreatsServiceConfig) {
    // Warn if multiple instances are created
    if (ThreatsService.instance !== null) {
      console.warn(
        'ThreatsService: Multiple instances detected. Consider using singleton pattern.',
      );
    }

    this.connectionManager = WebSocketConnectionManager.getInstance();
    this.threatsCache = serviceConfig.threatsCache;
    this.cacheTimeout = serviceConfig.cacheTimeout || 3600;

    this.setupEventHandlers();
    ThreatsService.instance = this;
  }

  /**
   * Get the singleton instance or create a new one
   */
  public static getInstance(
    serviceConfig?: ThreatsServiceConfig,
  ): ThreatsService {
    if (!ThreatsService.instance && serviceConfig) {
      ThreatsService.instance = new ThreatsService(serviceConfig);
    } else if (!ThreatsService.instance) {
      throw new Error(
        'ThreatsService: Must provide serviceConfig for first initialization',
      );
    }
    return ThreatsService.instance;
  }

  /**
   * Initialize the threats WebSocket connection
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('ThreatsService: Already initialized');
      return;
    }

    // Validate that we have a WebSocket URL before attempting connection
    if (!config.externalApi.threatsWebSocketUrl) {
      const errorMsg = `ThreatsService: Cannot initialize - EXTERNAL_THREATS_WS environment variable is not set or empty`;
      console.error(errorMsg);
      throw new Error(errorMsg);
    }

    const connectionConfig: ConnectionConfig = {
      url: config.externalApi.threatsWebSocketUrl,
      reconnectDelayMs: 5000,
      maxReconnectAttempts: -1,
      connectionTimeoutMs: 10000,
      enableAutoReconnect: true,
      enableLogging: true,
    };

    console.log(
      `ThreatsService: Attempting to connect to ${config.externalApi.threatsWebSocketUrl}`,
    );

    try {
      await this.connectionManager.createConnection(
        ThreatsService.CONNECTION_ID,
        connectionConfig,
      );
      this.isInitialized = true;
      console.log(
        `ThreatsService: Successfully initialized and connected to ${config.externalApi.threatsWebSocketUrl}`,
      );
    } catch (error) {
      console.error(
        `ThreatsService: Failed to initialize connection to ${config.externalApi.threatsWebSocketUrl}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Stop the threats WebSocket connection
   */
  public disconnect(): void {
    if (!this.isInitialized) return;

    this.connectionManager.disconnect(ThreatsService.CONNECTION_ID);
    console.log('ThreatsService: Disconnected');
  }

  /**
   * Get the current WebSocket connection status
   */
  public getConnectionStatus(): string {
    if (!this.isInitialized) return 'not-initialized';
    return this.connectionManager.getConnectionStatus(
      ThreatsService.CONNECTION_ID,
    );
  }

  /**
   * Check if WebSocket is currently connected
   */
  public isConnected(): boolean {
    if (!this.isInitialized) return false;
    return this.connectionManager.isConnected(ThreatsService.CONNECTION_ID);
  }

  /**
   * Get connection statistics
   */
  public getStats() {
    if (!this.isInitialized) return null;
    return this.connectionManager.getStats(ThreatsService.CONNECTION_ID);
  }

  /**
   * Send data through the WebSocket connection
   */
  public send(data: string | Buffer): boolean {
    if (!this.isInitialized) {
      console.warn('ThreatsService: Cannot send data - not initialized');
      return false;
    }
    return this.connectionManager.send(ThreatsService.CONNECTION_ID, data);
  }

  /**
   * Set the cache timeout for threats data
   */
  public setCacheTimeout(timeoutSeconds: number): void {
    this.cacheTimeout = timeoutSeconds;
  }

  /**
   * Get the current cache timeout
   */
  public getCacheTimeout(): number {
    return this.cacheTimeout;
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    if (this.isInitialized) {
      this.connectionManager.removeConnection(ThreatsService.CONNECTION_ID);
      this.isInitialized = false;
    }
    // Reset singleton instance
    ThreatsService.instance = null;
    console.log('ThreatsService: Cleaned up');
  }

  /**
   * Set up event handlers for WebSocket events
   */
  private setupEventHandlers(): void {
    this.connectionManager.on('connected', (connectionId: string) => {
      if (connectionId === ThreatsService.CONNECTION_ID) {
        console.log('ThreatsService: Connected to threats WebSocket API');
      }
    });

    this.connectionManager.on(
      'disconnected',
      (connectionId: string, code: number, reason?: string) => {
        if (connectionId === ThreatsService.CONNECTION_ID) {
          console.log(
            `ThreatsService: Disconnected (code: ${code}, reason: ${reason || 'none'})`,
          );
        }
      },
    );

    this.connectionManager.on(
      'message',
      async (connectionId: string, data: Buffer | string) => {
        if (connectionId === ThreatsService.CONNECTION_ID) {
          await this.handleThreatsMessage(data);
        }
      },
    );

    this.connectionManager.on('error', (connectionId: string, error: Error) => {
      if (connectionId === ThreatsService.CONNECTION_ID) {
        console.error('ThreatsService: WebSocket error:', error.message);
      }
    });

    this.connectionManager.on(
      'reconnecting',
      (connectionId: string, attempt: number) => {
        if (connectionId === ThreatsService.CONNECTION_ID) {
          console.log(`ThreatsService: Reconnecting (attempt ${attempt})...`);
        }
      },
    );

    this.connectionManager.on('reconnectFailed', (connectionId: string) => {
      if (connectionId === ThreatsService.CONNECTION_ID) {
        console.error(
          'ThreatsService: Failed to reconnect after maximum attempts',
        );
      }
    });
  }

  /**
   * Handle incoming threats data from WebSocket
   */
  private async handleThreatsMessage(data: Buffer | string): Promise<void> {
    try {
      // Convert data to string and parse
      const messageString = MessageParser.dataToString(data);
      const parseResult: ParsedMessage = MessageParser.parseJson(messageString);

      if (!parseResult.success) {
        console.warn(
          'ThreatsService: Failed to parse message:',
          parseResult.error,
        );
        return;
      }

      // Validate that we have valid threats data
      if (!this.isValidThreatsData(parseResult.data)) {
        console.warn('ThreatsService: Received invalid threats data format');
        return;
      }

      const incidents = (parseResult.data as ThreatsData).incidents;
      const incidentsCount = Array.isArray(incidents) ? incidents.length : 0;

      console.log(
        `ThreatsService: Received valid threats data: ${incidentsCount} items`,
      );

      // Always append data to preserve existing incidents
      const stored = await this.threatsCache.appendThreatsData(
        parseResult.data,
        this.cacheTimeout,
      );

      if (stored) {
        console.log(
          'ThreatsService: Successfully appended threats data to cache',
        );

        // Only broadcast after successful cache write to ensure data consistency
        // This prevents race conditions where new clients might connect before cache is updated
        const unifiedWsService = UnifiedWebSocketService.getInstance();
        unifiedWsService.broadcastThreats(parseResult.data, 'websocket');
      } else {
        console.warn('ThreatsService: Failed to append threats data to cache');
        // Don't broadcast if cache write failed to maintain consistency
      }
    } catch (error) {
      console.error('ThreatsService: Error handling threats message:', error);
    }
  }

  /**
   * Validate threats data structure
   */
  private isValidThreatsData(data: unknown): data is ThreatsData {
    return data !== null && typeof data === 'object';
  }
}
