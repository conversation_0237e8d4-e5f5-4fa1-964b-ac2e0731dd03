#!/bin/bash

# Copy branding assets to public directory
BRANDING_ASSETS_PATH="../../../packages/frontend/branding/src/assets/logos"
PUBLIC_LOGOS_PATH="public/logos"

echo "📦 Copying branding assets..."

# Create public logos directory if it doesn't exist
mkdir -p "$PUBLIC_LOGOS_PATH"

# Copy the specific logos
if [ -f "$BRANDING_ASSETS_PATH/full-logo-white-blue.png" ]; then
    cp "$BRANDING_ASSETS_PATH/full-logo-white-blue.png" "$PUBLIC_LOGOS_PATH/"
    echo "✅ Copied full-logo-white-blue.png"
else
    echo "⚠️  full-logo-white-blue.png not found"
    exit 1
fi

if [ -f "$BRANDING_ASSETS_PATH/full-logo.png" ]; then
    cp "$BRANDING_ASSETS_PATH/full-logo.png" "$PUBLIC_LOGOS_PATH/"
    echo "✅ Copied full-logo.png"
else
    echo "⚠️  full-logo.png not found"
    exit 1
fi

if [ -f "$BRANDING_ASSETS_PATH/t-logo.png" ]; then
    cp "$BRANDING_ASSETS_PATH/t-logo.png" "$PUBLIC_LOGOS_PATH/"
    echo "✅ Copied t-logo.png"
else
    echo "⚠️  t-logo.png not found"
    exit 1
fi

echo "🎉 Branding assets copied successfully!"
