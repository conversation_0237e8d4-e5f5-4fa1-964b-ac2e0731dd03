import express, { Application } from 'express';
import compression from 'compression';
import { createHelmetMiddleware } from './middleware';
import { default as router } from './routes';
import config from './config';

const app: Application = express();

app.set('trust proxy', true); // Trust the first proxy (for reverse proxy setups)
app.set('x-powered-by', false); // Disable 'X-Powered-By' header for security
app.set('json spaces', 2); // Pretty-print JSON responses with 2 spaces
app.disable('etag'); // Disable ETag generation for performance and security

// Security middleware with comprehensive CSP and security headers
const isDevelopment = config.server.env === 'development';
app.use(createHelmetMiddleware(isDevelopment));
app.use(compression());
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: false })); // Parse URL-encoded bodies

app.use('/', router);

export default app;
