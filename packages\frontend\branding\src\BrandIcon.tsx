export interface BrandIconProps {
  size?: number;
  className?: string;
  color?: string;
}

/**
 * Minimal brand icon - just the TS letters
 */
export function BrandIcon({
  size = 24,
  className = '',
  color = 'currentColor',
}: BrandIconProps) {
  return (
    <div
      className={`inline-flex items-center justify-center font-bold ${className}`}
      style={{
        width: `${size}px`,
        height: `${size}px`,
        fontSize: `${size * 0.6}px`,
        color,
      }}
    >
      TS
    </div>
  );
}
