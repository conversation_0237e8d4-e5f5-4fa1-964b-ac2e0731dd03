import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { ThreatIncident } from '@telesoft/types';
import { threatsService, ThreatsWebSocketMessage } from '../services/threats';
import { ApiError, apiClient } from '../api-client';
import { useAppConfig } from '../contexts/config-provider';

export interface UseThreatsResult {
  threats: ThreatIncident[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Hook for managing threats data with loading and error states
 */
export function useThreats(): UseThreatsResult {
  const [threats, setThreats] = useState<ThreatIncident[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchThreats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await threatsService.getThreats();
      setThreats(data);
    } catch (err) {
      let errorMessage = 'Failed to load threats data';

      if (err instanceof ApiError) {
        errorMessage = `API Error: ${err.message}`;
      } else if (err instanceof Error) {
        // Handle specific backend unavailability messages
        if (
          err.message.includes('Backend Unavailable') ||
          err.message.includes('Service Unavailable') ||
          err.message.includes('503')
        ) {
          errorMessage =
            'Backend service is temporarily unavailable. Please try again later.';
        } else if (
          err.message.includes('Failed to connect') ||
          err.message.includes('Connection refused') ||
          err.message.includes('ECONNREFUSED')
        ) {
          errorMessage =
            'Unable to connect to the backend service. Please check your connection and try again.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      console.error('Error fetching threats:', err);

      // Don't clear existing threats data on error - keep showing cached data
      // setThreats([]); // Commented out to preserve existing data
    } finally {
      setLoading(false);
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchThreats();
  }, [fetchThreats]);

  useEffect(() => {
    fetchThreats();
  }, [fetchThreats]);

  return {
    threats,
    loading,
    error,
    refresh,
  };
}

export interface UseThreatFiltersResult {
  filteredThreats: ThreatIncident[];
  filters: ThreatFilters;
  setFilters: (filters: Partial<ThreatFilters>) => void;
  resetFilters: () => void;
}

export interface ThreatFilters {
  type?: ThreatIncident['incident_type'];
  level?: ThreatIncident['risk_severity'];
  progress?: ThreatIncident['investigation_status'];
}

/**
 * Hook for filtering threats with various criteria
 */
export function useThreatFilters(
  threats: ThreatIncident[],
): UseThreatFiltersResult {
  const [filters, setFiltersState] = useState<ThreatFilters>({});

  const filteredThreats = threats.filter((threat) => {
    if (filters.type && threat.incident_type !== filters.type) return false;
    if (filters.level && threat.risk_severity !== filters.level) return false;
    if (filters.progress && threat.investigation_status !== filters.progress)
      return false;
    return true;
  });

  const setFilters = useCallback((newFilters: Partial<ThreatFilters>) => {
    setFiltersState((prev) => ({ ...prev, ...newFilters }));
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState({});
  }, []);

  return {
    filteredThreats,
    filters,
    setFilters,
    resetFilters,
  };
}

/**
 * Hook for getting threat statistics and distributions
 */
export function useThreatStats(threats: ThreatIncident[]) {
  return useMemo(() => {
    return {
      total: threats.length,
      critical: threats.filter((t) => t.risk_severity === 'critical').length,
      high: threats.filter((t) => t.risk_severity === 'high').length,
      medium: threats.filter((t) => t.risk_severity === 'medium').length,
      low: threats.filter((t) => t.risk_severity === 'low').length,
      info: threats.filter((t) => t.risk_severity === 'info').length,
      unknown: threats.filter((t) => t.risk_severity === 'unknown').length,
      newIncidents: threats.filter((t) => t.investigation_status === 'created')
        .length,
      inProgress: threats.filter((t) => t.investigation_status === 'running')
        .length,
      completed: threats.filter((t) => t.investigation_status === 'complete')
        .length,
      failed: threats.filter((t) => t.investigation_status === 'failed').length,
      typeDistribution: threatsService.getTypeDistribution(threats),
      levelDistribution: threatsService.getLevelDistribution(threats),
      recentThreats: threatsService.getRecentThreats(threats, 5),
      criticalThreats: threatsService.getCriticalThreats(threats),
    };
  }, [threats]);
}

export interface UseThreatsWebSocketOptions {
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  fallbackToRest?: boolean;
}

export interface UseThreatsWebSocketResult {
  threats: ThreatIncident[];
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connect: () => void;
  disconnect: () => void;
  lastUpdate: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for real-time threats data using WebSocket with REST API fallback
 */
export function useThreatsWebSocket(
  options: UseThreatsWebSocketOptions = {},
): UseThreatsWebSocketResult {
  const { autoConnect = true, fallbackToRest = true } = options;

  // Get configuration state
  const { config, loading: configLoading } = useAppConfig();

  const [threats, setThreats] = useState<ThreatIncident[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isConnectingRef = useRef(false);
  const lastPongRef = useRef<number>(Date.now());
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Fallback REST API fetch
  const refetch = useCallback(async () => {
    if (!fallbackToRest) return;

    try {
      const data = await threatsService.getThreats();
      setThreats(data);
      setLastUpdate(new Date().toISOString());
      setError(null);
    } catch (err) {
      let errorMessage = 'Failed to fetch threats';

      if (err instanceof ApiError) {
        errorMessage = `API Error: ${err.message}`;
      } else if (err instanceof Error) {
        // Handle specific backend unavailability messages
        if (
          err.message.includes('Backend Unavailable') ||
          err.message.includes('Service Unavailable') ||
          err.message.includes('503')
        ) {
          errorMessage = 'Backend service is temporarily unavailable';
        } else if (
          err.message.includes('Failed to connect') ||
          err.message.includes('Connection refused') ||
          err.message.includes('ECONNREFUSED')
        ) {
          errorMessage = 'Unable to connect to backend service';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      console.error('Failed to fetch threats via REST API:', err);

      // Don't clear existing data on error
      // setThreats([]); // Commented out to preserve existing data
    }
  }, [fallbackToRest]);

  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }

    threatsService.disconnectWebSocket();
    wsRef.current = null;
    setIsConnected(false);
    setIsConnecting(false);
    isConnectingRef.current = false;
  }, []);

  // Update handleWebSocketMessage to track pongs
  const handleWebSocketMessage = useCallback(
    (message: ThreatsWebSocketMessage) => {
      if (message.type === 'pong') {
        lastPongRef.current = Date.now();
        return;
      }

      if (message.type === 'threats-initial' && message.data?.incidents) {
        // Initial full data load - replace everything
        console.log('WebSocket: Received threats-initial', {
          incidentsCount: message.data.incidents.length,
          timestamp: message.timestamp,
        });
        setThreats([...message.data.incidents]); // Create new array reference
        setLastUpdate(message.timestamp);
      } else if (message.type === 'threats-update' && message.data?.incidents) {
        // Append new incidents only
        const newIncidents = message.data.incidents;
        console.log('WebSocket: Received threats-update', {
          newIncidentsCount: newIncidents.length,
          timestamp: message.timestamp,
          action: message.data.action,
        });

        if (message.data.action === 'append' && newIncidents.length > 0) {
          setThreats((prev) => {
            // Simply append new incidents and sort by time
            const updated = [...prev, ...newIncidents];

            // Remove any duplicates based on uid
            const uniqueMap = new Map<string, ThreatIncident>();
            updated.forEach((incident) => {
              uniqueMap.set(incident.uid, incident);
            });

            const uniqueThreats = Array.from(uniqueMap.values());

            console.log('WebSocket: Appended new incidents', {
              previousCount: prev.length,
              newCount: uniqueThreats.length,
              addedUIDs: newIncidents.map((i) => i.uid),
            });

            // Sort by time (newest first) for consistent ordering
            return uniqueThreats.sort((a, b) => (b.time || 0) - (a.time || 0));
          });
        }

        setLastUpdate(message.timestamp);
      } else if (message.type === 'ping') {
        // Respond to ping with pong
        const pongMessage: ThreatsWebSocketMessage = {
          type: 'pong',
          timestamp: new Date().toISOString(),
        };
        threatsService.sendWebSocketMessage(pongMessage);
      }
    },
    [],
  );

  const connect = useCallback(async () => {
    if (threatsService.isWebSocketConnected()) {
      return; // Already connected
    }

    if (isConnectingRef.current) {
      return; // Connection in progress
    }

    try {
      setIsConnecting(true);
      isConnectingRef.current = true;
      setError(null);

      const { ws } = await threatsService.connectWebSocket(
        handleWebSocketMessage,
        (_error) => {
          setError('WebSocket connection error');
          setIsConnecting(false);
          isConnectingRef.current = false;
        },
        (event) => {
          setIsConnected(false);
          setIsConnecting(false);
          isConnectingRef.current = false;

          // Note: Reconnection is now handled automatically by threatsService
          // Just log the disconnect and optionally fallback to REST
          console.log(
            'Threats WebSocket disconnected, service will handle reconnection',
          );

          if (fallbackToRest && event.code !== 1000) {
            console.log('Falling back to REST API while reconnecting...');
            refetch();
          }
        },
        () => {
          setIsConnected(true);
          setIsConnecting(false);
          isConnectingRef.current = false;
          setError(null);
        },
      );

      wsRef.current = ws;
    } catch (err) {
      console.error('Error creating threats WebSocket:', err);
      setError('Failed to create WebSocket connection');
      setIsConnecting(false);
      isConnectingRef.current = false;

      // Fallback to REST API if WebSocket creation fails
      if (fallbackToRest) {
        refetch();
      }
    }
  }, [handleWebSocketMessage, fallbackToRest, refetch]);

  const disconnect = useCallback(() => {
    cleanup();
  }, [cleanup]);

  // Auto-connect on mount if enabled, but wait for configuration to be loaded
  useEffect(() => {
    let isMounted = true;

    // Only connect when configuration is loaded and autoConnect is enabled
    if (autoConnect && !configLoading && config && isMounted) {
      console.log('[useThreatsWebSocket] Configuration loaded, connecting...', {
        wsUrl: config.api.wsUrl,
        useProxy: config.api.useProxy,
      });
      connect();
    }

    return () => {
      isMounted = false;
      cleanup();
    };
  }, [autoConnect, configLoading, config, connect, cleanup]);

  // Improved ping/keepalive mechanism with timeout detection
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      if (threatsService.isWebSocketConnected()) {
        // Check if we haven't received a pong in too long
        const timeSinceLastPong = Date.now() - lastPongRef.current;
        if (timeSinceLastPong > 60000) {
          // 1 minute timeout
          console.warn(
            '[Threats WebSocket] No pong received for 60s, reconnecting...',
          );
          disconnect();
          connect();
          return;
        }

        const pingMessage: ThreatsWebSocketMessage = {
          type: 'ping',
          timestamp: new Date().toISOString(),
        };
        threatsService.sendWebSocketMessage(pingMessage);
      }
    }, 30000); // Ping every 30 seconds

    pingIntervalRef.current = pingInterval;

    return () => {
      if (pingIntervalRef.current) {
        clearInterval(pingIntervalRef.current);
        pingIntervalRef.current = null;
      }
    };
  }, [isConnected, connect, disconnect]);

  return {
    threats,
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    lastUpdate,
    refetch,
  };
}

/**
 * Hook for prefetching threats data
 */
export function usePrefetchThreats() {
  const prefetch = useCallback(async () => {
    try {
      await apiClient.prefetch('/api/v1/threats');
    } catch (error) {
      console.error('Failed to prefetch threats:', error);
    }
  }, []);

  return { prefetch };
}
