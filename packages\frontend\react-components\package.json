{"name": "@telesoft/react-components", "version": "1.0.0", "description": "Common React components for tabs, status indicators, stats cards, and filters", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "format": "prettier --write . --ignore-path ../../../.prettierignore"}, "dependencies": {"react": "^18.2.0", "@telesoft/ui": "workspace:*", "@telesoft/utils": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.0", "@telesoft/typescript-config": "workspace:*", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist"]}