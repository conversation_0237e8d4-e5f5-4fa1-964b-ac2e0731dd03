---
sidebar_position: 7
---

# Status

The Status component displays the current state or condition of a system, user, or process through a visual indicator. It's commonly used for showing online/offline status, system health, or operational states.

import { Status } from '@telesoft/ui';
import { ComponentShowcase } from '../../src/components/ComponentShowcase';
import { UIThemeProvider } from '../../src/components/UIThemeProvider';

<UIThemeProvider>

## Basic Usage

Status indicators provide quick visual feedback about state or condition.

<ComponentShowcase
  title="Basic Status Indicator"
  description="Simple status dot without additional text."
  component={<Status variant="online" />}
  code={`<Status variant="online" />`}
/>

## Variants

Status components come in different variants to represent various states and conditions.

<ComponentShowcase
  title="Online Status"
  description="Green indicator showing active or available state."
  component={<Status variant="online" />}
  code={`<Status variant="online" />`}
/>

<ComponentShowcase
  title="Offline Status"
  description="Gray indicator showing inactive or unavailable state."
  component={<Status variant="offline" />}
  code={`<Status variant="offline" />`}
/>

<ComponentShowcase
  title="Warning Status"
  description="Yellow indicator showing caution or attention needed."
  component={<Status variant="warning" />}
  code={`<Status variant="warning" />`}
/>

<ComponentShowcase
  title="Error Status"
  description="Red indicator showing error or critical condition."
  component={<Status variant="error" />}
  code={`<Status variant="error" />`}
/>

<ComponentShowcase
  title="Processing Status"
  description="Blue indicator showing active processing or loading state."
  component={<Status variant="processing" />}
  code={`<Status variant="processing" />`}
/>

## Sizes

Status indicators come in different sizes for various UI contexts.

<ComponentShowcase
  title="Status Sizes"
  description="Different sizes for various use cases and layouts."
  component={
    <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <Status variant="online" size="sm" />
        <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Small</span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <Status variant="online" size="md" />
        <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Medium</span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <Status variant="online" size="lg" />
        <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Large</span>
      </div>
    </div>
  }
  code={`<Status variant="online" size="sm" />
<Status variant="online" size="md" />
<Status variant="online" size="lg" />`}
/>

## With Labels

Status indicators can include descriptive labels for better clarity.

<ComponentShowcase
  title="Status with Label"
  description="Status indicator with descriptive text label."
  component={<Status variant="online" label="Online" />}
  code={`<Status variant="online" label="Online" />`}
/>

<ComponentShowcase
  title="Various Status Labels"
  description="Different status variants with appropriate labels."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Status variant="online" label="Online" />
      <Status variant="offline" label="Offline" />
      <Status variant="warning" label="Limited" />
      <Status variant="error" label="Error" />
      <Status variant="processing" label="Processing" />
    </div>
  }
  code={`<Status variant="online" label="Online" />
<Status variant="offline" label="Offline" />
<Status variant="warning" label="Limited" />
<Status variant="error" label="Error" />
<Status variant="processing" label="Processing" />`}
/>

## Animation Control

Status indicators can be animated or static based on the use case.

<ComponentShowcase
  title="Animated Status"
  description="Animated status indicators for dynamic states."
  component={
    <div style={{ display: 'flex', gap: '2rem' }}>
      <Status variant="processing" label="Processing" animated />
      <Status variant="online" label="Connected" animated />
    </div>
  }
  code={`<Status variant="processing" label="Processing" animated />
<Status variant="online" label="Connected" animated />`}
/>

<ComponentShowcase
  title="Static Status"
  description="Non-animated status indicators for stable states."
  component={
    <div style={{ display: 'flex', gap: '2rem' }}>
      <Status variant="offline" label="Disconnected" animated={false} />
      <Status variant="error" label="Failed" animated={false} />
    </div>
  }
  code={`<Status variant="offline" label="Disconnected" animated={false} />
<Status variant="error" label="Failed" animated={false} />`}
/>

## With Custom Content

Status indicators can include custom content alongside the status indicator.

<ComponentShowcase
  title="Status with Custom Content"
  description="Status indicator with additional custom content."
  component={
    <Status variant="online">
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <span style={{ fontWeight: 'medium' }}>System Status</span>
        <span style={{ fontSize: '0.875rem', color: '#64748b' }}>
          All systems operational
        </span>
      </div>
    </Status>
  }
  code={`<Status variant="online">
  <div className="flex flex-col">
    <span className="font-medium">System Status</span>
    <span className="text-sm text-gray-500">All systems operational</span>
  </div>
</Status>`}
/>

## Real-World Examples

<ComponentShowcase
  title="User Status List"
  description="Status indicators showing user availability in a list."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
        <Status variant="online" size="sm" />
        <span>John Doe - Available</span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
        <Status variant="warning" size="sm" />
        <span>Jane Smith - Away</span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
        <Status variant="offline" size="sm" />
        <span>Bob Johnson - Offline</span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
        <Status variant="processing" size="sm" />
        <span>Alice Brown - In Meeting</span>
      </div>
    </div>
  }
  code={`<div className="flex items-center gap-3">
  <Status variant="online" size="sm" />
  <span>John Doe - Available</span>
</div>
<div className="flex items-center gap-3">
  <Status variant="warning" size="sm" />
  <span>Jane Smith - Away</span>
</div>
<div className="flex items-center gap-3">
  <Status variant="offline" size="sm" />
  <span>Bob Johnson - Offline</span>
</div>
<div className="flex items-center gap-3">
  <Status variant="processing" size="sm" />
  <span>Alice Brown - In Meeting</span>
</div>`}
/>

<ComponentShowcase
  title="Service Health Dashboard"
  description="Status indicators for monitoring service health."
  component={
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: '1rem',
        maxWidth: '400px',
      }}
    >
      <Status variant="online" label="API Gateway" size="md" />
      <Status variant="online" label="Database" size="md" />
      <Status variant="warning" label="Cache Server" size="md" />
      <Status variant="error" label="Email Service" size="md" />
    </div>
  }
  code={`<div className="grid grid-cols-2 gap-4">
  <Status variant="online" label="API Gateway" size="md" />
  <Status variant="online" label="Database" size="md" />
  <Status variant="warning" label="Cache Server" size="md" />
  <Status variant="error" label="Email Service" size="md" />
</div>`}
/>

<ComponentShowcase
  title="Connection Status"
  description="Status showing connection states with details."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Status variant="online" size="lg">
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span style={{ fontWeight: 'medium', color: '#10b981' }}>Connected</span>
          <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Latency: 45ms</span>
        </div>
      </Status>
      <Status variant="processing" size="lg">
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span style={{ fontWeight: 'medium', color: '#3b82f6' }}>Connecting...</span>
          <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Attempting reconnection</span>
        </div>
      </Status>
      <Status variant="error" size="lg">
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span style={{ fontWeight: 'medium', color: '#ef4444' }}>Connection Failed</span>
          <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Check network settings</span>
        </div>
      </Status>
    </div>
  }
  code={`<Status variant="online" size="lg">
  <div className="flex flex-col">
    <span className="font-medium text-green-500">Connected</span>
    <span className="text-sm text-gray-500">Latency: 45ms</span>
  </div>
</Status>

<Status variant="processing" size="lg">
  <div className="flex flex-col">
    <span className="font-medium text-blue-500">Connecting...</span>
    <span className="text-sm text-gray-500">Attempting reconnection</span>
  </div>
</Status>

<Status variant="error" size="lg">
  <div className="flex flex-col">
    <span className="font-medium text-red-500">Connection Failed</span>
    <span className="text-sm text-gray-500">Check network settings</span>
  </div>
</Status>
`} />

## Layout Examples

<ComponentShowcase
  title="Status in Card Headers"
  description="Status indicators integrated into card components."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', maxWidth: '300px' }}>
      <div style={{ padding: '1rem', border: '1px solid #e2e8f0', borderRadius: '0.5rem' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
          <span style={{ fontWeight: 'medium' }}>Server 01</span>
          <Status variant="online" size="sm" />
        </div>
        <span style={{ fontSize: '0.875rem', color: '#64748b' }}>Running normally</span>
      </div>
      <div style={{ padding: '1rem', border: '1px solid #e2e8f0', borderRadius: '0.5rem' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
          <span style={{ fontWeight: 'medium' }}>Server 02</span>
          <Status variant="warning" size="sm" />
        </div>
        <span style={{ fontSize: '0.875rem', color: '#64748b' }}>High CPU usage</span>
      </div>
    </div>
  }
  code={`<div className="p-4 border border-gray-200 rounded-lg">
  <div className="flex items-center justify-between mb-2">
    <span className="font-medium">Server 01</span>
    <Status variant="online" size="sm" />
  </div>
  <span className="text-sm text-gray-500">Running normally</span>
</div>

<div className="p-4 border border-gray-200 rounded-lg">
  <div className="flex items-center justify-between mb-2">
    <span className="font-medium">Server 02</span>
    <Status variant="warning" size="sm" />
  </div>
  <span className="text-sm text-gray-500">High CPU usage</span>
</div>
`} />

</UIThemeProvider>

## Props

### Status Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'variant',
      type: "'online' | 'offline' | 'warning' | 'error' | 'processing'",
      default: "'online'",
      description: 'Visual style variant of the status indicator',
    },
    {
      name: 'size',
      type: "'sm' | 'md' | 'lg'",
      default: "'md'",
      description: 'Size of the status indicator',
    },
    {
      name: 'animated',
      type: 'boolean',
      default: 'true',
      description: 'Whether the status indicator should be animated',
    },
    {
      name: 'label',
      type: 'string',
      description: 'Optional label to display next to the status indicator',
    },
    {
      name: 'children',
      type: 'React.ReactNode',
      description: 'Custom content to display next to the status indicator',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

## Accessibility

The Status component follows accessibility best practices:

- **Semantic meaning**: Uses appropriate visual indicators for different states
- **Color independence**: Doesn't rely solely on color to convey status
- **Text alternatives**: Supports labels and custom content for screen readers
- **Consistent behavior**: Predictable animation and visual patterns
- **Focus management**: Proper focus handling when interactive

## Best Practices

### Do ✅

- Use consistent status variants across your application
- Provide clear labels when status meaning isn't obvious
- Use appropriate sizes for the context (small for lists, larger for emphasis)
- Consider animation for dynamic states like "processing"
- Group related status indicators logically
- Use status indicators to provide quick visual feedback

### Don't ❌

- Don't rely solely on color to convey status meaning
- Don't use too many different status variants in one interface
- Don't animate status indicators unnecessarily
- Don't make status indicators too small to be clearly visible
- Don't use status indicators for decorative purposes
- Don't forget to update status indicators when state changes

## Common Patterns

### Real-time Status Updates

```tsx
const [connectionStatus, setConnectionStatus] = useState('connecting');

useEffect(() => {
  const checkConnection = async () => {
    try {
      const response = await fetch('/api/health');
      setConnectionStatus(response.ok ? 'online' : 'error');
    } catch {
      setConnectionStatus('offline');
    }
  };

  const interval = setInterval(checkConnection, 5000);
  return () => clearInterval(interval);
}, []);

return <Status variant={connectionStatus} label="Server Status" />;
```

### User Presence Indicator

```tsx
const getUserStatus = (lastSeen: Date) => {
  const now = new Date();
  const diffMinutes = (now.getTime() - lastSeen.getTime()) / (1000 * 60);

  if (diffMinutes < 5) return 'online';
  if (diffMinutes < 30) return 'warning';
  return 'offline';
};

return (
  <Status variant={getUserStatus(user.lastSeen)} label={user.name} size="sm" />
);
```

### Service Health Monitor

```tsx
const serviceHealth = {
  api: 'online',
  database: 'online',
  cache: 'warning',
  email: 'error',
};

return (
  <div className="grid grid-cols-2 gap-4">
    {Object.entries(serviceHealth).map(([service, status]) => (
      <Status
        key={service}
        variant={status}
        label={service.charAt(0).toUpperCase() + service.slice(1)}
      />
    ))}
  </div>
);
```
