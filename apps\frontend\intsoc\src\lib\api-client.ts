/**
 * HTTP API Client for frontend-to-backend communication
 *
 * This handles HTTP requests only. Depending on configuration:
 * - If USE_API_PROXY=true: Browser → Next.js API routes → Backend
 * - If USE_API_PROXY=false: Browser → Backend directly
 *
 * Note: WebSocket connections always bypass this client and connect directly to backend
 *
 * Configuration is now fetched at runtime instead of using build-time environment variables.
 */

import { getGlobalConfig } from './contexts/config-provider';
import { initializeConfig } from './client-config';

export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: Response,
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export interface RequestOptions extends RequestInit {
  skipCache?: boolean;
  retry?: number;
  retryDelay?: number;
}

export class ApiClient {
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, Promise<any>>();
  private configInitialized = false;

  private async ensureConfigInitialized() {
    if (!this.configInitialized) {
      await initializeConfig();
      this.configInitialized = true;
    }
  }

  private get config() {
    return getGlobalConfig().api;
  }

  private getCacheKey(endpoint: string, options?: RequestInit): string {
    return `${options?.method || 'GET'}:${endpoint}`;
  }

  private async request<T>(
    endpoint: string,
    options: RequestOptions = {},
  ): Promise<T> {
    // Ensure configuration is loaded
    await this.ensureConfigInitialized();

    // Construct URL - baseUrl is either '' (proxy, endpoints have full path) or 'http://backend:4001' (direct)
    const url = `${this.config.baseUrl}${endpoint}`;

    const cacheKey = this.getCacheKey(endpoint, options);

    // Check cache for GET requests
    if (
      !options.skipCache &&
      options.method === 'GET' &&
      this.config.enableCache
    ) {
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
        if (getGlobalConfig().debug.apiDebug) {
          console.log(`[API Client] Cache hit for ${cacheKey}`);
        }
        return cached.data;
      }
    }

    // Check for pending requests (request deduplication)
    if (options.method === 'GET') {
      const pending = this.pendingRequests.get(cacheKey);
      if (pending) {
        if (getGlobalConfig().debug.apiDebug) {
          console.log(`[API Client] Deduplicating request for ${cacheKey}`);
        }
        return pending;
      }
    }

    if (getGlobalConfig().debug.apiDebug) {
      console.log(
        `[API Client] ${options.method || 'GET'} ${url} (proxy: ${this.config.useProxy})`,
      );
    }

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    const executeRequest = async (retryCount = 0): Promise<T> => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(
          () => controller.abort(),
          this.config.timeout,
        );

        const response = await fetch(url, {
          ...config,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new ApiError(
            `HTTP ${response.status}: ${response.statusText}`,
            response.status,
            response,
          );
        }

        const data = await response.json();

        // Cache successful GET requests
        if (
          !options.skipCache &&
          options.method === 'GET' &&
          this.config.enableCache
        ) {
          this.cache.set(cacheKey, { data, timestamp: Date.now() });
        }

        return data;
      } catch (error) {
        // Remove from pending requests on error
        this.pendingRequests.delete(cacheKey);

        if (error instanceof ApiError) {
          // Don't retry client errors (4xx)
          if (error.status && error.status >= 400 && error.status < 500) {
            throw error;
          }
        }

        // Retry logic
        const maxRetries = options.retry ?? 2;
        if (retryCount < maxRetries) {
          const delay =
            options.retryDelay ??
            Math.min(1000 * Math.pow(2, retryCount), 5000);
          if (getGlobalConfig().debug.apiDebug) {
            console.log(
              `[API Client] Retrying request in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`,
            );
          }
          await new Promise((resolve) => setTimeout(resolve, delay));
          return executeRequest(retryCount + 1);
        }

        if (error instanceof DOMException && error.name === 'AbortError') {
          throw new ApiError('Request timeout');
        }

        throw new ApiError(
          error instanceof Error ? error.message : 'Unknown error occurred',
        );
      }
    };

    // Store promise for deduplication
    const promise = executeRequest();
    if (options.method === 'GET') {
      this.pendingRequests.set(cacheKey, promise);
      promise.finally(() => this.pendingRequests.delete(cacheKey));
    }

    return promise;
  }

  async get<T>(
    endpoint: string,
    options?: Omit<RequestOptions, 'method' | 'body'>,
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T>(
    endpoint: string,
    data?: unknown,
    options?: Omit<RequestOptions, 'method' | 'body'>,
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // Clear cache method
  clearCache(endpoint?: string): void {
    if (endpoint) {
      const cacheKey = this.getCacheKey(endpoint, { method: 'GET' });
      this.cache.delete(cacheKey);
    } else {
      this.cache.clear();
    }
  }

  // Prefetch method for Next.js
  async prefetch(endpoint: string): Promise<void> {
    try {
      await this.get(endpoint, { skipCache: false });
    } catch (error) {
      console.warn(`Failed to prefetch ${endpoint}:`, error);
    }
  }
}

// Singleton instance
export const apiClient = new ApiClient();
