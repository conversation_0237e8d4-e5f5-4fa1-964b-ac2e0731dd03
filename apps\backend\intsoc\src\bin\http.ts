#!/usr/bin/env node

process.env.TZ = 'Europe/London';

import http from 'http';
import app from '../index';
import config from '../config';
import UnifiedWebSocketService from '../services/UnifiedWebSocketService';
import { WebSocketConnectionManager } from '../services/WebSocketConnectionManager';
import {
  threatsController,
  systemMetricsController,
  mlController,
} from '../routes';

const { port } = config.server;
app.set('port', port);

function onError(error: { syscall: string; code: string }) {
  if (error.syscall !== 'listen') {
    throw error;
  }

  const bind = typeof port === 'string' ? `Pipe ${port}` : `Port ${port}`;

  switch (error.code) {
    case 'EACCES':
      console.error(`${bind} requires elevated privileges`);
      process.exit(1);
      break;
    case 'EADDRINUSE':
      console.error(`${bind} is already in use`);
      process.exit(1);
      break;
    default:
      throw error;
  }
}

const server = http.createServer(app);

// Initialize unified WebSocket service
const wsService = UnifiedWebSocketService.getInstance();

wsService.initialize(server);

server.listen(port);
server.on('error', onError);
server.on('listening', () => {
  const addr = server.address();
  const bind = typeof addr === 'string' ? `pipe ${addr}` : `port ${addr?.port}`;
  console.log(`Listening on ${bind}`);
  console.log(
    `🚀 ${config.server.appName} backend server is running in ${config.server.env} mode`,
  );
  console.log(`📡 WebSocket server available at ws://${bind}/ws/metrics`);
  console.log(
    `🔥 Threats WebSocket server available at ws://${bind}/ws/threats`,
  );
  console.log(
    `🤖 Machine Learning WebSocket server available at ws://${bind}/ws/ml`,
  );
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');

  // Cleanup application controllers
  threatsController.cleanup();
  systemMetricsController.cleanup();
  mlController.cleanup();

  // Close server-side WebSocket connections
  wsService.close();

  // Clean up external WebSocket connections (like threats API connection)
  const connectionManager = WebSocketConnectionManager.getInstance();
  connectionManager.cleanup();

  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');

  // Cleanup application controllers
  threatsController.cleanup();
  systemMetricsController.cleanup();
  mlController.cleanup();

  // Close server-side WebSocket connections
  wsService.close();

  // Clean up external WebSocket connections (like threats API connection)
  const connectionManager = WebSocketConnectionManager.getInstance();
  connectionManager.cleanup();

  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});
