# @telesoft/jest-config

Shared Jest configuration and testing utilities for the Telesoft UI monorepo.

## Installation

This package is part of the Telesoft UI monorepo and should be installed as a workspace dependency:

```json
{
  "devDependencies": {
    "@telesoft/jest-config": "workspace:*"
  }
}
```

## Usage

### Jest Configuration

Use the base configuration in your `jest.config.js`:

```javascript
import { createJestConfig } from '@telesoft/jest-config';

export default createJestConfig('@your-package/name');
```

Or extend the base configuration:

```javascript
import { baseJestConfig } from '@telesoft/jest-config';

export default {
  ...baseJestConfig,
  displayName: '@your-package/name',
  // Add your custom overrides here
};
```

### Test Utilities

Import testing utilities from the package:

```typescript
import {
  render,
  screen,
  act,
  userEvent,
  expectToHaveClasses,
  expectNotToHaveClasses,
  createMockHandlers,
  testComponentRef
} from '@telesoft/jest-config/test-utils';

// Use in your tests
test('example', () => {
  const handlers = createMockHandlers();
  render(<MyComponent onClick={handlers.onClick} />);

  const element = screen.getByRole('button');
  expectToHaveClasses(element, ['btn', 'btn-primary']);
});
```

## Features

### Base Configuration

- TypeScript support with ts-jest
- ES Modules support
- jsdom test environment
- CSS module mocking
- Coverage reporting
- Path mapping support (`@/` -> `src/`)

### Test Utilities

- **Custom render function**: Extensible render function for React Testing Library
- **CSS class helpers**: `expectToHaveClasses()` and `expectNotToHaveClasses()`
- **Mock handlers**: Pre-configured Jest mock functions for common events
- **Ref testing**: Helper for testing component refs
- **Re-exports**: All React Testing Library utilities plus `act` from React

## API

### Configuration Functions

#### `baseJestConfig`

Base Jest configuration object that can be extended.

#### `createJestConfig(displayName, overrides?)`

Creates a Jest configuration with a display name and optional overrides.

### Test Utilities

#### `expectToHaveClasses(element, classes)`

Assert that an element has all specified CSS classes.

#### `expectNotToHaveClasses(element, classes)`

Assert that an element does not have any of the specified CSS classes.

#### `createMockHandlers()`

Returns an object with pre-configured Jest mock functions for common React events.

#### `testComponentRef(component, expectedInstanceType, getElementFromScreen)`

Helper function for testing component refs.
