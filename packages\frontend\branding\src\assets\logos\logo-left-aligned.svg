<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
	 viewBox="0 0 92.540123 21.207108" xml:space="preserve" y="0px" x="0px">
<style type="text/css">
@font-face {
    font-family: "montserrat-black";
    src: url("Montserrat-Black.ttf");
}
	.st0{fill:#FFFFFF;}
	.st1{font-family:'montserrat-black';}
	.st2{font-size:14px;}
	.st3{letter-spacing:10;}
	.st4{fill:none;stroke:#F15A24;stroke-width:0.5;stroke-miterlimit:10;}
	.st5{fill:#F15A24;}
	.st6{fill:none;stroke:#27AAE1;stroke-width:0.5;stroke-miterlimit:10;}
	.st7{fill:#00AEEF;}
</style>
<g transform="translate(-80,-47.996447)">
	<g>
		<text class="st0 st1 st2 st3" transform="translate(112,65.9579)"></text>
	</g>
	<g>
		<polygon points="84,43 84,75" class="st6 st7" />
		<polygon points="86.5,43 86.5,75" class="st6 st7" />

		<polygon points="89,43 89,50" class="st6 st7" />
		<polygon points="91.5,43 91.5,50" class="st6 st7" />
		<polygon points="89,55 89,75" class="st6 st7" />
		<polygon points="91.5,55 91.5,75" class="st6 st7" />

		<polygon points="94,68.5 94,75" class="st6 st7" />
		<polygon points="94,43 94,50" class="st6 st7" />
		<polygon points="96.5,68.5 96.5,75" class="st6 st7" />
		<polygon points="96.5,43 96.5,50" class="st6 st7" />

		<polygon points="99,43 99,50" class="st6 st7" />
		<polygon points="101.5,43 101.5,50" class="st6 st7" />

		<polygon points="99,55 99,75" class="st6 st7" />
		<polygon points="101.5,55 101.5,75" class="st6 st7" />

		<polygon points="104,43 104,75" class="st6 st7" />
		<polygon points="106.5,43 106.5,75" class="st6 st7" />

	</g>
</g>
</svg>
