/**
 * Global error handler for unhandled promise rejections and uncaught exceptions
 * This helps prevent crashes when backend services are unavailable
 */

export function setupGlobalErrorHandlers() {
  // Handle unhandled promise rejections
  if (typeof process !== 'undefined') {
    process.on('unhandledRejection', (reason, promise) => {
      console.error('🚨 Unhandled Promise Rejection at:', promise);
      console.error('📝 Reason:', reason);

      // Log structured error information
      if (reason instanceof Error) {
        console.error('📋 Error Details:', {
          name: reason.name,
          message: reason.message,
          stack: reason.stack,
          timestamp: new Date().toISOString(),
        });

        // Check if it's a backend connectivity issue
        if (
          reason.message.includes('ECONNREFUSED') ||
          reason.message.includes('Failed to connect to backend') ||
          reason.message.includes('fetch failed')
        ) {
          console.warn(
            '⚠️  Backend connectivity issue detected. Application will continue running with degraded functionality.',
          );
        }
      }

      // Don't exit the process - let the application continue running
      // In production, you might want to send this to an error monitoring service
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('🚨 Uncaught Exception:', error);

      // Log structured error information
      console.error('📋 Error Details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      // Check if it's a backend connectivity issue
      if (
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('Failed to connect to backend') ||
        error.message.includes('fetch failed')
      ) {
        console.warn(
          '⚠️  Backend connectivity issue detected. Attempting to continue...',
        );
        return; // Don't exit for backend connectivity issues
      }

      // For other critical errors, we might want to exit gracefully
      console.error('💥 Critical error occurred. Exiting gracefully...');
      process.exit(1);
    });

    // Handle process termination signals
    ['SIGTERM', 'SIGINT'].forEach((signal) => {
      process.on(signal as NodeJS.Signals, () => {
        console.log(`📡 Received ${signal}, shutting down gracefully...`);
        process.exit(0);
      });
    });
  }

  // Client-side error handling (if running in browser)
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 Unhandled Promise Rejection (Client):', event.reason);

      // Prevent the default browser behavior (which logs to console)
      event.preventDefault();

      // Log structured error information
      if (event.reason instanceof Error) {
        console.error('📋 Client Error Details:', {
          name: event.reason.name,
          message: event.reason.message,
          stack: event.reason.stack,
          timestamp: new Date().toISOString(),
        });
      }
    });

    window.addEventListener('error', (event) => {
      console.error('🚨 Uncaught Error (Client):', event.error);

      // Log structured error information
      console.error('📋 Client Error Details:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        timestamp: new Date().toISOString(),
      });
    });
  }
}

// Auto-setup when module is imported
setupGlobalErrorHandlers();
