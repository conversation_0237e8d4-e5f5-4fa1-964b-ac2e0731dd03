import { defineConfig } from 'tsup';

export default defineConfig((options) => ({
  entry: ['./index.tsx'],
  format: options.watch ? ['esm'] : ['cjs', 'esm'], // Only ESM in watch mode
  dts: !options.watch, // Skip type generation in watch mode
  splitting: false,
  sourcemap: options.watch ? false : true, // Skip sourcemaps in watch mode for speed
  clean: !options.watch, // Skip cleaning in watch mode
  outDir: 'dist',
  external: ['react', 'react-dom'],
  minify: !options.watch, // Skip minification in watch mode
  tsconfig: options.watch ? './tsconfig.dev.json' : './tsconfig.json', // Use dev config in watch mode
  esbuildOptions(esbuildOptions) {
    esbuildOptions.jsx = 'automatic';
    // Use faster target in dev
    if (options.watch) {
      esbuildOptions.target = 'esnext'; // Use latest target for faster compilation
    }
  },
}));
