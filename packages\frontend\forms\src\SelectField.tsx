import React from 'react';
import { STYLE_PRESETS, classNames } from '@telesoft/utils';

export interface SelectFieldProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  helperText?: string;
  error?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export const SelectField: React.FC<SelectFieldProps> = ({
  label,
  value,
  onChange,
  options,
  helperText,
  error,
  disabled,
  placeholder,
  className,
}) => (
  <div className="space-y-2">
    <label className={classNames(STYLE_PRESETS.formLabel)}>{label}</label>
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={classNames(
        STYLE_PRESETS.formSelect,
        error && 'border-cyber-danger-500 focus-visible:ring-cyber-danger-500',
        className,
      )}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
    {error && <p className="text-sm text-cyber-danger-500">{error}</p>}
    {helperText && !error && (
      <p className={classNames(STYLE_PRESETS.formHelpText)}>{helperText}</p>
    )}
  </div>
);
