import React from 'react';
import { render, screen } from '@testing-library/react';
import { Status } from '../Status';

describe('Status', () => {
  describe('Basic rendering', () => {
    it('renders with default props', () => {
      render(<Status data-testid="status" />);
      // Since it's just a dot without label, we need to find it by test id
      const status = screen.getByTestId('status');
      expect(status).toBeInTheDocument();
    });

    it('renders as a simple dot when no label or children', () => {
      render(<Status data-testid="status-dot" />);
      const status = screen.getByTestId('status-dot');
      expect(status).toHaveClass('rounded-full');
    });

    it('renders with label', () => {
      render(<Status label="Online" />);
      expect(screen.getByText('Online')).toBeInTheDocument();
    });

    it('renders with children', () => {
      render(<Status>Custom content</Status>);
      expect(screen.getByText('Custom content')).toBeInTheDocument();
    });
  });

  describe('Variants', () => {
    const variants = [
      'online',
      'offline',
      'warning',
      'error',
      'processing',
    ] as const;

    variants.forEach((variant) => {
      it(`renders ${variant} variant correctly`, () => {
        render(<Status variant={variant} label={variant} />);
        expect(screen.getByText(variant)).toBeInTheDocument();
      });
    });

    it('applies online variant classes', () => {
      render(<Status variant="online" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('bg-cyber-matrix-500');
    });

    it('applies offline variant classes', () => {
      render(<Status variant="offline" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('bg-slate-500');
    });

    it('applies warning variant classes', () => {
      render(<Status variant="warning" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('bg-cyber-warning-500');
    });

    it('applies error variant classes', () => {
      render(<Status variant="error" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('bg-cyber-danger-500');
    });

    it('applies processing variant classes', () => {
      render(<Status variant="processing" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('bg-primary-500');
    });

    it('defaults to online variant', () => {
      render(<Status data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('bg-cyber-matrix-500');
    });
  });

  describe('Sizes', () => {
    const sizes = ['sm', 'md', 'lg'] as const;

    sizes.forEach((size) => {
      it(`renders ${size} size correctly`, () => {
        render(<Status size={size} data-testid="status" />);
        const status = screen.getByTestId('status');
        expect(status).toBeInTheDocument();
      });
    });

    it('applies small size classes', () => {
      render(<Status size="sm" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('w-2', 'h-2');
    });

    it('applies medium size classes', () => {
      render(<Status size="md" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('w-3', 'h-3');
    });

    it('applies large size classes', () => {
      render(<Status size="lg" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('w-4', 'h-4');
    });

    it('defaults to medium size', () => {
      render(<Status data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('w-3', 'h-3');
    });
  });

  describe('Animation', () => {
    it('applies animation by default for online status', () => {
      render(<Status variant="online" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('animate-pulse');
    });

    it('applies animation by default for processing status', () => {
      render(<Status variant="processing" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('animate-pulse');
    });

    it('does not animate offline status by default', () => {
      render(<Status variant="offline" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).not.toHaveClass('animate-pulse');
    });

    it('does not animate warning status by default', () => {
      render(<Status variant="warning" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).not.toHaveClass('animate-pulse');
    });

    it('does not animate error status by default', () => {
      render(<Status variant="error" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).not.toHaveClass('animate-pulse');
    });

    it('can disable animation for online status', () => {
      render(<Status variant="online" animated={false} data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).not.toHaveClass('animate-pulse');
    });

    it('can disable animation for processing status', () => {
      render(
        <Status variant="processing" animated={false} data-testid="status" />,
      );
      const status = screen.getByTestId('status');
      expect(status).not.toHaveClass('animate-pulse');
    });

    it('animation prop does not affect non-animating variants', () => {
      render(<Status variant="offline" animated={true} data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).not.toHaveClass('animate-pulse');
    });
  });

  describe('Layout with label', () => {
    it('renders with flex layout when label is provided', () => {
      render(<Status label="System Status" data-testid="status-container" />);
      const container = screen.getByTestId('status-container');
      expect(container).toHaveClass('flex', 'items-center', 'gap-2');
    });

    it('renders label with correct styling', () => {
      render(<Status label="Connected" />);
      const label = screen.getByText('Connected');
      expect(label).toHaveClass('text-sm', 'text-text-secondary');
    });

    it('renders both dot and label', () => {
      render(
        <Status
          label="Active"
          variant="online"
          data-testid="status-container"
        />,
      );
      const container = screen.getByTestId('status-container');
      const dot = container.querySelector('.rounded-full');
      const label = screen.getByText('Active');

      expect(dot).toBeInTheDocument();
      expect(label).toBeInTheDocument();
      expect(dot).toHaveClass('bg-cyber-matrix-500');
    });
  });

  describe('Layout with children', () => {
    it('renders with flex layout when children are provided', () => {
      render(
        <Status data-testid="status-container">
          <span>Custom content</span>
        </Status>,
      );
      const container = screen.getByTestId('status-container');
      expect(container).toHaveClass('flex', 'items-center', 'gap-2');
    });

    it('renders both dot and children', () => {
      render(
        <Status variant="error" data-testid="status-container">
          <span>Error occurred</span>
        </Status>,
      );
      const container = screen.getByTestId('status-container');
      const dot = container.querySelector('.rounded-full');

      expect(dot).toBeInTheDocument();
      expect(screen.getByText('Error occurred')).toBeInTheDocument();
      expect(dot).toHaveClass('bg-cyber-danger-500');
    });

    it('can render both label and children', () => {
      render(
        <Status label="Status:" data-testid="status-container">
          <strong>Critical</strong>
        </Status>,
      );
      const container = screen.getByTestId('status-container');

      expect(screen.getByText('Status:')).toBeInTheDocument();
      expect(screen.getByText('Critical')).toBeInTheDocument();
      expect(container).toHaveClass('flex', 'items-center', 'gap-2');
    });
  });

  describe('Custom props', () => {
    it('applies custom className to simple dot', () => {
      render(<Status className="custom-status" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('custom-status');
    });

    it('applies custom className to container with label', () => {
      render(
        <Status
          label="Test"
          className="custom-container"
          data-testid="status"
        />,
      );
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('custom-container');
    });

    it('passes through additional props to simple dot', () => {
      render(<Status data-testid="status" title="Status indicator" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveAttribute('title', 'Status indicator');
    });

    it('passes through additional props to container', () => {
      render(
        <Status label="Test" data-testid="status" title="Status with label" />,
      );
      const status = screen.getByTestId('status');
      expect(status).toHaveAttribute('title', 'Status with label');
    });

    it('forwards ref correctly for simple dot', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<Status ref={ref} />);
      expect(ref.current).toBeInstanceOf(HTMLDivElement);
    });

    it('forwards ref correctly for container', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<Status ref={ref} label="Test" />);
      expect(ref.current).toBeInstanceOf(HTMLDivElement);
    });
  });

  describe('Base classes', () => {
    it('applies base classes to dot', () => {
      render(<Status data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('rounded-full');
    });

    it('maintains base classes with custom className', () => {
      render(<Status className="custom" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveClass('rounded-full', 'custom');
    });
  });

  describe('Accessibility', () => {
    it('can be given an accessible label', () => {
      render(<Status aria-label="System is online" data-testid="status" />);
      const status = screen.getByTestId('status');
      expect(status).toHaveAttribute('aria-label', 'System is online');
    });

    it('works with screen readers when using visible label', () => {
      render(<Status label="Connection Status: Online" />);
      expect(screen.getByText('Connection Status: Online')).toBeInTheDocument();
    });
  });
});
