import React from 'react';
import { render, screen } from '@testing-library/react';
import { Progress } from '../Progress';

describe('Progress', () => {
  describe('Basic rendering', () => {
    it('renders with default props', () => {
      render(<Progress />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toBeInTheDocument();
    });

    it('renders with value and max', () => {
      render(<Progress value={50} max={100} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveAttribute('aria-valuenow', '50');
      expect(progressbar).toHaveAttribute('aria-valuemax', '100');
    });

    it('has proper ARIA attributes', () => {
      render(<Progress value={25} max={100} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveAttribute('aria-valuenow', '25');
      expect(progressbar).toHaveAttribute('aria-valuemin', '0');
      expect(progressbar).toHaveAttribute('aria-valuemax', '100');
    });
  });

  describe('Value calculations', () => {
    it('calculates percentage correctly', () => {
      render(<Progress value={50} max={100} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveStyle({ width: '50%' });
    });

    it('handles zero value', () => {
      render(<Progress value={0} max={100} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveStyle({ width: '0%' });
    });

    it('handles max value', () => {
      render(<Progress value={100} max={100} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveStyle({ width: '100%' });
    });

    it('clamps values above max', () => {
      render(<Progress value={150} max={100} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveStyle({ width: '100%' });
    });

    it('clamps negative values', () => {
      render(<Progress value={-10} max={100} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveStyle({ width: '0%' });
    });

    it('works with custom max values', () => {
      render(<Progress value={25} max={50} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveStyle({ width: '50%' });
    });
  });

  describe('Variants', () => {
    const variants = ['default', 'success', 'warning', 'danger'] as const;

    variants.forEach((variant) => {
      it(`renders ${variant} variant correctly`, () => {
        render(<Progress variant={variant} value={50} />);
        const progressbar = screen.getByRole('progressbar');
        expect(progressbar).toBeInTheDocument();
      });
    });

    it('applies default variant classes', () => {
      render(<Progress variant="default" value={50} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveClass('bg-primary-500');
    });

    it('applies success variant classes', () => {
      render(<Progress variant="success" value={50} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveClass('bg-cyber-matrix-500');
    });

    it('applies warning variant classes', () => {
      render(<Progress variant="warning" value={50} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveClass('bg-cyber-warning-500');
    });

    it('applies danger variant classes', () => {
      render(<Progress variant="danger" value={50} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveClass('bg-cyber-danger-500');
    });
  });

  describe('Sizes', () => {
    const sizes = ['sm', 'md', 'lg'] as const;

    sizes.forEach((size) => {
      it(`renders ${size} size correctly`, () => {
        render(<Progress size={size} value={50} />);
        const container = screen.getByRole('progressbar').parentElement;
        expect(container).toBeInTheDocument();
      });
    });

    it('applies small size classes', () => {
      render(<Progress size="sm" value={50} />);
      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-2');
    });

    it('applies medium size classes', () => {
      render(<Progress size="md" value={50} />);
      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-3');
    });

    it('applies large size classes', () => {
      render(<Progress size="lg" value={50} />);
      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-4');
    });

    it('defaults to medium size', () => {
      render(<Progress value={50} />);
      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('h-3');
    });
  });

  describe('Labels and values', () => {
    it('renders label when provided', () => {
      render(<Progress label="Loading..." value={50} />);
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('shows percentage when showValue is true', () => {
      render(<Progress value={75} showValue />);
      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    it('rounds percentage values', () => {
      render(<Progress value={33.333} showValue />);
      expect(screen.getByText('33%')).toBeInTheDocument();
    });

    it('shows both label and value', () => {
      render(<Progress label="Progress" value={60} showValue />);
      expect(screen.getByText('Progress')).toBeInTheDocument();
      expect(screen.getByText('60%')).toBeInTheDocument();
    });

    it('does not render label section when neither label nor showValue', () => {
      render(<Progress value={50} />);
      expect(screen.queryByText('50%')).not.toBeInTheDocument();

      // Check that the label container is not rendered
      const progressContainer = screen.getByRole('progressbar').closest('div');
      const labelContainer = progressContainer?.querySelector(
        '.flex.justify-between',
      );
      expect(labelContainer).not.toBeInTheDocument();
    });

    it('positions label and value correctly', () => {
      render(<Progress label="Upload" value={80} showValue />);
      const labelElement = screen.getByText('Upload');
      const valueElement = screen.getByText('80%');

      // Both should be in the same flex container
      expect(labelElement.parentElement).toBe(valueElement.parentElement);
      expect(labelElement.parentElement).toHaveClass('flex', 'justify-between');
    });
  });

  describe('Custom props', () => {
    it('applies custom className', () => {
      render(<Progress className="custom-progress" value={50} />);
      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass('custom-progress');
    });

    it('passes through additional props', () => {
      render(<Progress data-testid="custom-progress" value={50} />);
      expect(screen.getByTestId('custom-progress')).toBeInTheDocument();
    });

    it('forwards ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<Progress ref={ref} value={50} />);
      expect(ref.current).toBeInstanceOf(HTMLDivElement);
    });
  });

  describe('Base classes', () => {
    it('applies container base classes', () => {
      render(<Progress value={50} />);
      const container = screen.getByRole('progressbar').parentElement;
      expect(container).toHaveClass(
        'relative',
        'w-full',
        'overflow-hidden',
        'rounded-full',
        'bg-slate-800',
      );
    });

    it('applies progress bar base classes', () => {
      render(<Progress value={50} />);
      const progressbar = screen.getByRole('progressbar');
      expect(progressbar).toHaveClass(
        'h-full',
        'transition-all',
        'duration-500',
        'ease-out',
      );
    });
  });

  describe('Edge cases', () => {
    it('handles decimal values', () => {
      render(<Progress value={33.7} max={100} showValue />);
      expect(screen.getByText('34%')).toBeInTheDocument();
    });

    it('handles zero max value', () => {
      render(<Progress value={50} max={0} />);
      const progressbar = screen.getByRole('progressbar');
      // Should handle division by zero gracefully
      expect(progressbar).toBeInTheDocument();
    });

    it('handles large numbers', () => {
      render(<Progress value={1000} max={2000} showValue />);
      expect(screen.getByText('50%')).toBeInTheDocument();
    });
  });
});
