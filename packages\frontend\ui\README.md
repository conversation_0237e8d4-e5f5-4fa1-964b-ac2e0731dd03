# @telesoft/ui

A comprehensive React component library for the Telesoft application ecosystem, featuring modern, accessible components built with TypeScript and Tailwind CSS.

## Features

- **Modern React Components** built with TypeScript for type safety
- **Tailwind CSS Integration** for consistent styling and theming
- **Accessibility First** with ARIA support and keyboard navigation
- **Dark Theme Optimized** for cybersecurity and monitoring environments
- **Comprehensive Testing** with Jest and React Testing Library
- **Tree Shakable** exports for optimal bundle sizes

## Installation

This package is part of the Telesoft UI monorepo and should be installed via workspace dependencies:

```json
{
  "dependencies": {
    "@telesoft/ui": "workspace:*"
  }
}
```

Don't forget to import the styles in your application:

```typescript
import '@telesoft/ui/styles.css';
```

## Usage

```tsx
import {
  Button,
  Card,
  Alert,
  Badge,
  Input,
  Progress,
  Status,
} from '@telesoft/ui';

function App() {
  return (
    <div>
      <Card>
        <h2>Welcome to Telesoft</h2>
        <Alert variant="success">System is operational</Alert>

        <Input label="Username" placeholder="Enter your username" />

        <Button variant="primary" size="lg">
          Login
        </Button>

        <Progress value={75} max={100} />

        <Badge variant="success">Active</Badge>
        <Status status="online" />
      </Card>
    </div>
  );
}
```

## Available Components

### Button

Customizable button component with multiple variants and sizes.

**Props:**

- `variant`: 'primary' | 'secondary' | 'danger' | 'ghost'
- `size`: 'sm' | 'md' | 'lg'
- `disabled`: boolean

### Card

Container component for grouping related content.

### Alert

Alert component for displaying important messages.

**Props:**

- `variant`: 'info' | 'success' | 'warning' | 'error'

### Badge

Small status indicator component.

**Props:**

- `variant`: 'default' | 'success' | 'warning' | 'error'

### Input

Form input component with label support.

**Props:**

- `label`: string
- `placeholder`: string
- `type`: string
- `disabled`: boolean

### Progress

Progress bar component for showing completion status.

**Props:**

- `value`: number
- `max`: number
- `label`: string (optional)

### Status

Status indicator component for showing online/offline states.

**Props:**

- `status`: 'online' | 'offline' | 'away' | 'busy'

### ThemeProvider

Context provider for theme management across the application.

## Development

### Building

```bash
# From monorepo root
pnpm --filter @telesoft/ui build

# Or from this directory
pnpm build
```

### Development Mode

```bash
# From monorepo root
pnpm --filter @telesoft/ui dev

# Or from this directory
pnpm dev
```

### Testing

```bash
# From monorepo root
pnpm --filter @telesoft/ui test

# Or from this directory
pnpm test
```

### Linting

```bash
# From monorepo root
pnpm --filter @telesoft/ui lint

# Or from this directory
pnpm lint
```

### Type Checking

```bash
# From monorepo root
pnpm --filter @telesoft/ui type-check

# Or from this directory
pnpm type-check
```

## Styling

Components are styled using Tailwind CSS with a custom configuration that:

- Uses the `@telesoft/color-palette` for consistent theming
- Optimized for dark theme cybersecurity interfaces
- Includes accessibility-focused color contrast ratios
- Provides responsive design utilities

## Testing

This package includes comprehensive tests using:

- **Jest** for test running
- **React Testing Library** for component testing
- **@testing-library/jest-dom** for additional matchers

See `TESTING.md` for detailed testing guidelines.

## Contributing

When adding new components:

1. Create the component in `src/ComponentName.tsx`
2. Add comprehensive TypeScript types
3. Include accessibility features (ARIA labels, keyboard navigation)
4. Write tests in `src/__tests__/ComponentName.test.tsx`
5. Export the component from `index.tsx`
6. Update this README with component documentation

## Dependencies

- **React 18+** - Peer dependency
- **Tailwind CSS** - For styling
- **@telesoft/color-palette** - Color system
- **@telesoft/types** - Shared TypeScript types
