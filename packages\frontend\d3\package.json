{"name": "@telesoft/d3", "version": "0.0.0", "private": true, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"build": "tsup", "dev": "tsup --watch --no-dts", "dev:fast": "tsup --watch --no-dts --no-sourcemap --format esm", "watch": "tsup --watch", "lint": "eslint .", "format": "prettier --write . --ignore-path ../../../.prettierignore", "test": "echo 'No tests to run'", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "files": ["dist", "README.md"], "peerDependencies": {"react": "^18.3.0", "react-dom": "^18.3.0"}, "dependencies": {"d3": "^7.9.0"}, "devDependencies": {"@telesoft/eslint-config": "workspace:*", "@telesoft/types": "workspace:*", "@telesoft/typescript-config": "workspace:*", "@types/d3": "^7.4.3", "@types/react": "catalog:typescript-5", "@types/react-dom": "catalog:typescript-5", "eslint": "catalog:eslint", "react": "catalog:react-18", "react-dom": "catalog:react-18", "tsup": "catalog:build", "typescript": "catalog:typescript-5"}}