---
sidebar_position: 2
---

# Card

The Card component provides a flexible container for grouping related content with consistent styling and layout options.

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
  <PERSON><PERSON>,
  Badge,
} from '@telesoft/ui';
import { ComponentShowcase } from '../../src/components/ComponentShowcase';
import { UIThemeProvider } from '../../src/components/UIThemeProvider';

<UIThemeProvider>

## Basic Usage

<ComponentShowcase
  title="Simple Card"
  description="A basic card with content inside."
  component={
    <Card style={{ maxWidth: '300px' }}>
      <CardContent>
        <p>This is a simple card with some content inside.</p>
      </CardContent>
    </Card>
  }
  code={`<Card>
  <CardContent>
    <p>This is a simple card with some content inside.</p>
  </CardContent>
</Card>`}
/>

## Card Structure

Cards can be composed of header, content, and footer sections for organized layouts.

<ComponentShowcase
  title="Complete Card Structure"
  description="A card with header, content, and footer sections."
  component={
    <Card style={{ maxWidth: '350px' }}>
      <CardHeader>
        <h3 style={{ margin: 0, fontSize: '1.2rem' }}>Card Title</h3>
        <p
          style={{
            margin: '0.5rem 0 0 0',
            color: 'var(--ifm-color-content-secondary)',
          }}
        >
          Subtitle or description
        </p>
      </CardHeader>
      <CardContent>
        <p>
          This is the main content area of the card. You can put any content
          here including text, images, or other components.
        </p>
      </CardContent>
      <CardFooter
        style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}
      >
        <Button variant="ghost" size="sm">
          Cancel
        </Button>
        <Button variant="primary" size="sm">
          Action
        </Button>
      </CardFooter>
    </Card>
  }
  code={`<Card>
  <CardHeader>
    <h3>Card Title</h3>
    <p>Subtitle or description</p>
  </CardHeader>
  <CardContent>
    <p>This is the main content area...</p>
  </CardContent>
  <CardFooter>
    <Button variant="ghost" size="sm">Cancel</Button>
    <Button variant="primary" size="sm">Action</Button>
  </CardFooter>
</Card>`}
/>

## Content Examples

<ComponentShowcase
  title="User Profile Card"
  description="Example of a user profile card with avatar, information, and actions."
  component={
    <Card style={{ maxWidth: '320px' }}>
      <CardHeader>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <div
            style={{
              width: '48px',
              height: '48px',
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '1.2rem',
              fontWeight: 'bold',
            }}
          >
            JD
          </div>
          <div>
            <h4 style={{ margin: 0, fontSize: '1.1rem' }}>John Doe</h4>
            <p
              style={{
                margin: '0.25rem 0 0 0',
                color: 'var(--ifm-color-content-secondary)',
                fontSize: '0.9rem',
              }}
            >
              Software Engineer
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem' }}>
          <Badge variant="primary">React</Badge>
          <Badge variant="secondary">TypeScript</Badge>
          <Badge variant="success">Node.js</Badge>
        </div>
        <p
          style={{
            fontSize: '0.9rem',
            color: 'var(--ifm-color-content-secondary)',
          }}
        >
          Passionate developer with 5+ years of experience building web
          applications.
        </p>
      </CardContent>
      <CardFooter>
        <Button variant="outline" size="sm" style={{ width: '100%' }}>
          View Profile
        </Button>
      </CardFooter>
    </Card>
  }
  code={`<Card>
  <CardHeader>
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <Avatar>JD</Avatar>
      <div>
        <h4>John Doe</h4>
        <p>Software Engineer</p>
      </div>
    </div>
  </CardHeader>
  <CardContent>
    <div style={{ display: 'flex', gap: '0.5rem' }}>
      <Badge variant="primary">React</Badge>
      <Badge variant="secondary">TypeScript</Badge>
    </div>
    <p>Passionate developer with 5+ years...</p>
  </CardContent>
  <CardFooter>
    <Button variant="outline" size="sm">View Profile</Button>
  </CardFooter>
</Card>`}
/>

<ComponentShowcase
  title="Statistics Card"
  description="A card displaying metrics and statistics."
  component={
    <Card style={{ maxWidth: '280px' }}>
      <CardHeader>
        <h4
          style={{
            margin: 0,
            fontSize: '1rem',
            color: 'var(--ifm-color-content-secondary)',
          }}
        >
          Total Revenue
        </h4>
      </CardHeader>
      <CardContent>
        <div
          style={{ fontSize: '2.5rem', fontWeight: 'bold', margin: '0.5rem 0' }}
        >
          $24,500
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <Badge variant="success">+12.5%</Badge>
          <span
            style={{
              fontSize: '0.9rem',
              color: 'var(--ifm-color-content-secondary)',
            }}
          >
            from last month
          </span>
        </div>
      </CardContent>
    </Card>
  }
  code={`<Card>
  <CardHeader>
    <h4>Total Revenue</h4>
  </CardHeader>
  <CardContent>
    <div className="stat-value">$24,500</div>
    <div>
      <Badge variant="success">+12.5%</Badge>
      <span>from last month</span>
    </div>
  </CardContent>
</Card>`}
/>

## Layout Examples

<ComponentShowcase
  title="Card Grid"
  description="Multiple cards arranged in a responsive grid layout."
  component={
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1rem',
        maxWidth: '600px',
      }}
    >
      <Card>
        <CardContent>
          <h4 style={{ margin: '0 0 0.5rem 0' }}>Feature 1</h4>
          <p style={{ margin: 0, fontSize: '0.9rem' }}>
            Description of the first feature.
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardContent>
          <h4 style={{ margin: '0 0 0.5rem 0' }}>Feature 2</h4>
          <p style={{ margin: 0, fontSize: '0.9rem' }}>
            Description of the second feature.
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardContent>
          <h4 style={{ margin: '0 0 0.5rem 0' }}>Feature 3</h4>
          <p style={{ margin: 0, fontSize: '0.9rem' }}>
            Description of the third feature.
          </p>
        </CardContent>
      </Card>
    </div>
  }
  code={`<div className="card-grid">
  <Card>
    <CardContent>
      <h4>Feature 1</h4>
      <p>Description of the first feature.</p>
    </CardContent>
  </Card>
  <Card>
    <CardContent>
      <h4>Feature 2</h4>
      <p>Description of the second feature.</p>
    </CardContent>
  </Card>
  <Card>
    <CardContent>
      <h4>Feature 3</h4>
      <p>Description of the third feature.</p>
    </CardContent>
  </Card>
</div>`}
/>

## Props

### Card Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description:
        'Card content (typically CardHeader, CardContent, CardFooter)',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

### CardHeader Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description: 'Header content (titles, descriptions, actions)',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

### CardContent Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description: 'Main card content',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

### CardFooter Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description: 'Footer content (typically actions or additional info)',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

## Accessibility

The Card component follows accessibility best practices:

- **Semantic structure**: Uses proper div elements with appropriate roles
- **Keyboard navigation**: Content within cards is accessible via keyboard
- **Focus management**: Maintains proper focus flow through card content
- **Screen reader support**: Works well with assistive technologies

## Best Practices

### Do ✅

- Use CardHeader for titles and metadata
- Place main content in CardContent
- Use CardFooter for actions and secondary information
- Keep card content focused and related
- Use consistent spacing and alignment

### Don't ❌

- Don't overcrowd cards with too much information
- Don't nest cards too deeply
- Don't forget to provide proper headings for screen readers
- Don't make cards too wide on large screens
- Don't mix unrelated content in a single card

</UIThemeProvider>
