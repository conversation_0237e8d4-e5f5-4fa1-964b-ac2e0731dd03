import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON>, CardHeader, CardContent, CardFooter } from '../Card';

describe('Card', () => {
  describe('Basic rendering', () => {
    it('renders with default props', () => {
      render(<Card>Card content</Card>);
      const card = screen.getByText('Card content');
      expect(card).toBeInTheDocument();
      expect(card.tagName).toBe('DIV');
    });

    it('renders children correctly', () => {
      render(
        <Card>
          <span>Custom card content</span>
        </Card>,
      );
      expect(screen.getByText('Custom card content')).toBeInTheDocument();
    });

    it('applies default classes', () => {
      render(<Card>Card</Card>);
      const card = screen.getByText('Card');
      expect(card).toHaveClass(
        'rounded-lg',
        'border',
        'border-border-primary',
        'bg-background-secondary',
        'shadow-sm',
      );
    });
  });

  describe('Custom props', () => {
    it('applies custom className', () => {
      render(<Card className="custom-card">Card</Card>);
      const card = screen.getByText('Card');
      expect(card).toHaveClass('custom-card');
    });

    it('passes through additional props', () => {
      render(<Card data-testid="custom-card">Card</Card>);
      expect(screen.getByTestId('custom-card')).toBeInTheDocument();
    });

    it('forwards ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<Card ref={ref}>Card</Card>);
      expect(ref.current).toBeInstanceOf(HTMLDivElement);
    });
  });
});

describe('CardHeader', () => {
  it('renders as div element', () => {
    render(<CardHeader>Header content</CardHeader>);
    const header = screen.getByText('Header content');
    expect(header).toBeInTheDocument();
    expect(header.tagName).toBe('DIV');
  });

  it('applies default classes', () => {
    render(<CardHeader>Header</CardHeader>);
    const header = screen.getByText('Header');
    expect(header).toHaveClass(
      'flex',
      'flex-col',
      'space-y-1.5',
      'p-6',
      'text-text-primary',
    );
  });

  it('applies custom className', () => {
    render(<CardHeader className="custom-header">Header</CardHeader>);
    const header = screen.getByText('Header');
    expect(header).toHaveClass('custom-header');
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<CardHeader ref={ref}>Header</CardHeader>);
    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('passes through additional props', () => {
    render(<CardHeader data-testid="card-header">Header</CardHeader>);
    expect(screen.getByTestId('card-header')).toBeInTheDocument();
  });
});

describe('CardContent', () => {
  it('renders as div element', () => {
    render(<CardContent>Content text</CardContent>);
    const content = screen.getByText('Content text');
    expect(content).toBeInTheDocument();
    expect(content.tagName).toBe('DIV');
  });

  it('applies default classes', () => {
    render(<CardContent>Content</CardContent>);
    const content = screen.getByText('Content');
    expect(content).toHaveClass('p-6', 'pt-0', 'text-text-secondary');
  });

  it('applies custom className', () => {
    render(<CardContent className="custom-content">Content</CardContent>);
    const content = screen.getByText('Content');
    expect(content).toHaveClass('custom-content');
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<CardContent ref={ref}>Content</CardContent>);
    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('passes through additional props', () => {
    render(<CardContent data-testid="card-content">Content</CardContent>);
    expect(screen.getByTestId('card-content')).toBeInTheDocument();
  });
});

describe('CardFooter', () => {
  it('renders as div element', () => {
    render(<CardFooter>Footer content</CardFooter>);
    const footer = screen.getByText('Footer content');
    expect(footer).toBeInTheDocument();
    expect(footer.tagName).toBe('DIV');
  });

  it('applies default classes', () => {
    render(<CardFooter>Footer</CardFooter>);
    const footer = screen.getByText('Footer');
    expect(footer).toHaveClass(
      'flex',
      'items-center',
      'p-6',
      'pt-0',
      'text-text-secondary',
    );
  });

  it('applies custom className', () => {
    render(<CardFooter className="custom-footer">Footer</CardFooter>);
    const footer = screen.getByText('Footer');
    expect(footer).toHaveClass('custom-footer');
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<CardFooter ref={ref}>Footer</CardFooter>);
    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('passes through additional props', () => {
    render(<CardFooter data-testid="card-footer">Footer</CardFooter>);
    expect(screen.getByTestId('card-footer')).toBeInTheDocument();
  });
});

describe('Card Composition', () => {
  it('renders complete card with all components', () => {
    render(
      <Card>
        <CardHeader>Card Title</CardHeader>
        <CardContent>This is the card content area.</CardContent>
        <CardFooter>Card footer actions</CardFooter>
      </Card>,
    );

    expect(screen.getByText('Card Title')).toBeInTheDocument();
    expect(
      screen.getByText('This is the card content area.'),
    ).toBeInTheDocument();
    expect(screen.getByText('Card footer actions')).toBeInTheDocument();
  });

  it('works with nested components', () => {
    render(
      <Card>
        <CardHeader>
          <h2>Title</h2>
          <p>Subtitle</p>
        </CardHeader>
        <CardContent>
          <p>Main content</p>
          <div>Additional content</div>
        </CardContent>
        <CardFooter>
          <button>Action</button>
        </CardFooter>
      </Card>,
    );

    expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent(
      'Title',
    );
    expect(screen.getByText('Subtitle')).toBeInTheDocument();
    expect(screen.getByText('Main content')).toBeInTheDocument();
    expect(screen.getByText('Additional content')).toBeInTheDocument();
    expect(screen.getByRole('button')).toHaveTextContent('Action');
  });

  it('works with partial composition', () => {
    render(
      <Card>
        <CardHeader>Header Only</CardHeader>
        <CardContent>Content Only</CardContent>
      </Card>,
    );

    expect(screen.getByText('Header Only')).toBeInTheDocument();
    expect(screen.getByText('Content Only')).toBeInTheDocument();
    expect(screen.queryByText('Footer')).not.toBeInTheDocument();
  });

  it('maintains proper structure and styling', () => {
    render(
      <Card className="custom-card">
        <CardHeader className="custom-header">Header</CardHeader>
        <CardContent className="custom-content">Content</CardContent>
        <CardFooter className="custom-footer">Footer</CardFooter>
      </Card>,
    );

    const card = screen.getByText('Header').closest('div')?.parentElement;
    expect(card).toHaveClass('custom-card');
    expect(screen.getByText('Header')).toHaveClass('custom-header');
    expect(screen.getByText('Content')).toHaveClass('custom-content');
    expect(screen.getByText('Footer')).toHaveClass('custom-footer');
  });
});
