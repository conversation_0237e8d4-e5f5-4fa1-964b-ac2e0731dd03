{"name": "@telesoft/branding", "version": "0.0.0", "private": true, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./assets": {"types": "./dist/assets.d.ts", "import": "./dist/assets.js", "require": "./dist/assets.cjs"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch --no-dts", "dev:fast": "tsup --watch --no-dts --no-sourcemap --format esm", "watch": "tsup --watch", "lint": "eslint .", "format": "prettier --write . --ignore-path ../../../.prettierignore", "test": "echo 'No tests to run'", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "peerDependencies": {"react": "^18.3.0"}, "devDependencies": {"@telesoft/eslint-config": "workspace:*", "@telesoft/typescript-config": "workspace:*", "@types/react": "catalog:typescript-5", "@types/react-dom": "catalog:typescript-5", "eslint": "catalog:eslint", "tsup": "catalog:build", "typescript": "catalog:typescript-5"}}