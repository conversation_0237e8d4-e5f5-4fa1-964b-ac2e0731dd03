# @telesoft/typescript-config

Shared TypeScript configurations for Telesoft projects, optimized by extending community-standard base configurations.

## Available Configurations

- **Base** (`@telesoft/typescript-config` or `@telesoft/typescript-config/base`): Core TypeScript configuration extending `@tsconfig/recommended`
- **React Library** (`@telesoft/typescript-config/react-library`): Configuration for React component libraries, extending `@tsconfig/vite-react`
- **Next.js** (`@telesoft/typescript-config/nextjs`): Configuration optimized for Next.js applications, extending `@tsconfig/next`
- **Node.js** (`@telesoft/typescript-config/node`): Configuration for Node.js backend applications, extending `@tsconfig/node20`
- **Strict** (`@telesoft/typescript-config/strict`): Strictest TypeScript configuration extending `@tsconfig/strictest`

## Usage

### In your tsconfig.json

```json
{
  "extends": "@telesoft/typescript-config/react-library",
  "include": ["src"],
  "exclude": ["dist", "build", "node_modules"]
}
```

### For stricter type checking

```json
{
  "extends": "@telesoft/typescript-config/strict",
  "include": ["src"],
  "exclude": ["dist", "build", "node_modules"]
}
```

### Configuration Details

#### Base Configuration

Extends `@tsconfig/recommended` with Telesoft-specific overrides:

- **Target**: ES2022
- **Module**: ESNext
- **Module Resolution**: bundler
- **Declaration files**: Generated with source maps
- **Incremental compilation**: Enabled

#### React Library Configuration

Extends `@tsconfig/vite-react` and base configuration:

- **JSX**: react-jsx
- **Target**: ES2022
- **Optimized for Vite bundling**
- **Declaration files**: Enabled for library builds

#### Next.js Configuration

Extends `@tsconfig/next`:

- **JSX**: preserve (for Next.js processing)
- **Target**: ES5 (for broad browser compatibility)
- **Next.js plugins**: Included
- **No emit**: Enabled (Next.js handles compilation)

#### Node.js Configuration

Extends `@tsconfig/node20` and base configuration:

- **Target**: ES2022
- **Module**: NodeNext
- **Node types**: Included
- **ES module interop**: Enabled

#### Strict Configuration

Extends `@tsconfig/strictest` and base configuration:

- **All strict options**: Enabled
- **No unused locals/parameters**: Enforced
- **Exact optional property types**: Enabled
- **No unchecked indexed access**: Enabled
- **Module resolution**: Node.js style
- **Additional features**: JSON imports, incremental compilation

#### React Library Configuration

- Extends base configuration
- **JSX**: react-jsx (React 17+ style)
- **Target**: ES6 for broader compatibility
- **Libraries**: ES2015 + DOM APIs

#### Next.js Configuration

- Extends base configuration
- **JSX**: preserve (Next.js handles transformation)
- **Target**: ES5 for broad browser support
- **No emit**: Next.js handles compilation
- **Includes**: Built-in Next.js type definitions

#### Node.js Configuration

- Extends base configuration
- **Libraries**: ES2022 (no DOM)
- **Types**: Node.js built-in types
- **Module resolution**: Node.js optimized

## Development

### Build Scripts

- `pnpm run build` - Validates all configurations
- `pnpm run validate` - Same as build
- `pnpm run validate:configs` - Comprehensive validation of TypeScript configs
- `pnpm run validate:syntax` - Validates JSON syntax
- `pnpm run validate:json` - Quick JSON syntax check

### Validation Features

The build process validates:

1. **JSON Syntax** - Ensures all config files are valid JSON
2. **Schema Validation** - Checks against TypeScript JSON schema
3. **Extends Resolution** - Verifies that extended configurations exist
4. **Compiler Options** - Validates required options are present
5. **TypeScript Compilation** - Tests configs with TypeScript compiler

### Adding New Configurations

1. Create a new `.json` file with your TypeScript configuration
2. Add it to the `exports` field in `package.json`
3. Add it to the `files` array in `package.json`
4. Update the validation script to include the new config
5. Run `pnpm run build` to validate

### File Structure

```
typescript-config/
├── base.json              # Core configuration
├── react-library.json     # React library setup
├── nextjs.json           # Next.js application setup
├── node.json             # Node.js backend setup
├── package.json          # Package configuration
├── README.md            # This file
└── scripts/
    └── validate-configs.js # Validation script
```

## Best Practices

### For Libraries

Use `react-library.json` for React component libraries that will be consumed by other applications.

### For Applications

- Use `nextjs.json` for Next.js web applications
- Use `node.json` for backend services and APIs

### For Monorepos

Each package should extend the appropriate configuration and add package-specific settings:

```json
{
  "extends": "@telesoft/typescript-config/react-library",
  "include": ["src", "index.tsx"],
  "exclude": ["dist", "build", "node_modules"],
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./"
  }
}
```

## Optimization Benefits

This package has been optimized by extending well-maintained community standard configurations:

- **`@tsconfig/recommended`**: Provides TypeScript team's recommended defaults
- **`@tsconfig/node20`**: Optimized for Node.js 20 runtime environment
- **`@tsconfig/next`**: Official Next.js TypeScript configuration
- **`@tsconfig/vite-react`**: Optimized for Vite + React development
- **`@tsconfig/strictest`**: Enables all strict TypeScript checking options

### Benefits:

- **Reduced maintenance**: Community maintains base configurations
- **Best practices**: Inherits TypeScript team recommendations
- **Up-to-date**: Base configs are regularly updated for new TypeScript features
- **Consistency**: Follows industry standards while allowing Telesoft customizations
- **Smaller configs**: Less duplication, only override what's needed
