# intsoc Backend

Node.js backend API for the intsoc application built with Express.js and TypeScript.

## Features

- **Express.js** server with TypeScript
- **Health check endpoints** for monitoring
- **Security middleware** (He<PERSON>et, CORS)
- **Environment configuration**
- **Error handling**
- **Development tools** (hot reload, linting)

## Getting Started

### Prerequisites

- Node.js 24+
- pnpm 10+ (recommended)

### Installation

```bash
# Install dependencies
pnpm install

# Copy environment variables
cp .env.example .env
```

### Development

```bash
# Start development server with hot reload
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Lint code
pnpm lint

# Type check
pnpm type-check
```

## API Endpoints

### Health Check

- **GET** `/api/health` - Basic health check
- **GET** `/api/health/detailed` - Detailed health check with system info

### Example Response

```json
{
  "status": "OK",
  "message": "intsoc backend is healthy",
  "timestamp": "2025-06-04T10:00:00.000Z",
  "uptime": 123.456,
  "environment": "development",
  "version": "1.0.0",
  "memory": {
    "used": 25.67,
    "total": 32.45,
    "unit": "MB"
  }
}
```

## Environment Variables

| Variable   | Description                          | Default       |
| ---------- | ------------------------------------ | ------------- |
| `NODE_ENV` | Environment (development/production) | `development` |
| `PORT`     | Server port                          | `3001`        |
| `HOST`     | Server host                          | `localhost`   |

## Project Structure

```
src/
├── index.ts          # Main application entry point
└── routes/
    └── health.ts      # Health check routes
```
