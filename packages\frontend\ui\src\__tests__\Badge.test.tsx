import React from 'react';
import { render, screen } from '@testing-library/react';
import { Badge, BadgeProps } from '../Badge';

describe('Badge', () => {
  const defaultProps: BadgeProps = {
    children: 'Test Badge',
  };

  it('renders with default props', () => {
    render(<Badge {...defaultProps} />);
    const badge = screen.getByText('Test Badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('inline-flex', 'items-center', 'rounded-full');
  });

  it('renders with custom className', () => {
    render(<Badge {...defaultProps} className="custom-class" />);
    const badge = screen.getByText('Test Badge');
    expect(badge).toHaveClass('custom-class');
  });

  describe('variants', () => {
    it('renders default variant by default', () => {
      render(<Badge {...defaultProps} />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('bg-primary-500');
    });

    it('renders secondary variant', () => {
      render(<Badge {...defaultProps} variant="secondary" />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass(
        'bg-background-tertiary',
        'border-border-primary',
      );
    });

    it('renders success variant', () => {
      render(<Badge {...defaultProps} variant="success" />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('bg-cyber-matrix-500');
    });

    it('renders warning variant', () => {
      render(<Badge {...defaultProps} variant="warning" />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('bg-cyber-warning-500');
    });

    it('renders danger variant', () => {
      render(<Badge {...defaultProps} variant="danger" />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('bg-cyber-danger-500');
    });

    it('renders outline variant', () => {
      render(<Badge {...defaultProps} variant="outline" />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('border', 'border-border-primary');
    });
  });

  describe('sizes', () => {
    it('renders medium size by default', () => {
      render(<Badge {...defaultProps} />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('px-2.5', 'py-0.5', 'text-sm');
    });

    it('renders small size', () => {
      render(<Badge {...defaultProps} size="sm" />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('px-2', 'py-0.5', 'text-xs');
    });

    it('renders large size', () => {
      render(<Badge {...defaultProps} size="lg" />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('px-3', 'py-1', 'text-base');
    });
  });

  describe('accessibility', () => {
    it('forwards ref to div element', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<Badge {...defaultProps} ref={ref} />);

      expect(ref.current).toBeInstanceOf(HTMLDivElement);
      expect(ref.current).toBe(screen.getByText('Test Badge'));
    });

    it('supports custom attributes', () => {
      render(
        <Badge
          {...defaultProps}
          data-testid="custom-badge"
          aria-label="Custom badge"
        />,
      );

      const badge = screen.getByTestId('custom-badge');
      expect(badge).toHaveAttribute('aria-label', 'Custom badge');
    });

    it('has focus styles', () => {
      render(<Badge {...defaultProps} />);
      const badge = screen.getByText('Test Badge');
      expect(badge).toHaveClass('focus:outline-none', 'focus:ring-2');
    });
  });

  describe('children', () => {
    it('renders text children', () => {
      render(<Badge>Status: Active</Badge>);
      expect(screen.getByText('Status: Active')).toBeInTheDocument();
    });

    it('renders React element children', () => {
      render(
        <Badge>
          <span>🔥</span>
          <span>Hot</span>
        </Badge>,
      );
      expect(screen.getByText('🔥')).toBeInTheDocument();
      expect(screen.getByText('Hot')).toBeInTheDocument();
    });

    it('renders with number children', () => {
      render(<Badge>{42}</Badge>);
      expect(screen.getByText('42')).toBeInTheDocument();
    });
  });
});
