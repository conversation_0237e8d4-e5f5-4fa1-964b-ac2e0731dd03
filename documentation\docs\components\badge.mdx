---
sidebar_position: 4
---

# Badge

The Badge component is used to display small pieces of information like status indicators, labels, counts, or categories.

import { <PERSON>ge, <PERSON>ton, Card, CardContent, CardHeader } from '@telesoft/ui';
import { ComponentShowcase } from '../../src/components/ComponentShowcase';
import { UIThemeProvider } from '../../src/components/UIThemeProvider';

<UIThemeProvider>

## Variants

Badges come in different variants to convey different types of information and importance levels.

<ComponentShowcase
  title="Primary Badge"
  description="Default badge for general labeling and categorization."
  component={<Badge variant="primary">Primary</Badge>}
  code={`<Badge variant="primary">Primary</Badge>`}
/>

<ComponentShowcase
  title="Secondary Badge"
  description="Subtle badge for less prominent information."
  component={<Badge variant="secondary">Secondary</Badge>}
  code={`<Badge variant="secondary">Secondary</Badge>`}
/>

<ComponentShowcase
  title="Success Badge"
  description="Green badge for positive states, completed tasks, or success indicators."
  component={<Badge variant="success">Success</Badge>}
  code={`<Badge variant="success">Success</Badge>`}
/>

<ComponentShowcase
  title="Warning Badge"
  description="Yellow badge for cautionary states or pending actions."
  component={<Badge variant="warning">Warning</Badge>}
  code={`<Badge variant="warning">Warning</Badge>`}
/>

<ComponentShowcase
  title="Danger Badge"
  description="Red badge for error states, critical issues, or destructive actions."
  component={<Badge variant="danger">Danger</Badge>}
  code={`<Badge variant="danger">Danger</Badge>`}
/>

## Sizes

Badges are available in different sizes to fit various contexts and layouts.

<ComponentShowcase
  title="Badge Sizes"
  description="Small, medium (default), and large badge sizes."
  component={
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <Badge size="sm" variant="primary">
        Small
      </Badge>
      <Badge size="md" variant="primary">
        Medium
      </Badge>
      <Badge size="lg" variant="primary">
        Large
      </Badge>
    </div>
  }
  code={`<Badge size="sm" variant="primary">Small</Badge>
<Badge size="md" variant="primary">Medium</Badge>
<Badge size="lg" variant="primary">Large</Badge>`}
/>

## Practical Examples

<ComponentShowcase
  title="Status Indicators"
  description="Common status badges used in applications."
  component={
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
      <Badge variant="success">Active</Badge>
      <Badge variant="warning">Pending</Badge>
      <Badge variant="danger">Inactive</Badge>
      <Badge variant="secondary">Draft</Badge>
      <Badge variant="primary">Published</Badge>
    </div>
  }
  code={`<Badge variant="success">Active</Badge>
<Badge variant="warning">Pending</Badge>
<Badge variant="danger">Inactive</Badge>
<Badge variant="secondary">Draft</Badge>
<Badge variant="primary">Published</Badge>`}
/>

<ComponentShowcase
  title="Technology Tags"
  description="Badges used to display technology stacks or categories."
  component={
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
      <Badge variant="primary">React</Badge>
      <Badge variant="secondary">TypeScript</Badge>
      <Badge variant="success">Node.js</Badge>
      <Badge variant="warning">JavaScript</Badge>
      <Badge variant="primary">CSS</Badge>
    </div>
  }
  code={`<Badge variant="primary">React</Badge>
<Badge variant="secondary">TypeScript</Badge>
<Badge variant="success">Node.js</Badge>
<Badge variant="warning">JavaScript</Badge>
<Badge variant="primary">CSS</Badge>`}
/>

<ComponentShowcase
  title="Priority Levels"
  description="Badges indicating different priority or urgency levels."
  component={
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
        alignItems: 'flex-start',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <Badge variant="danger" size="sm">
          High
        </Badge>
        <span>Critical bug fix needed</span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <Badge variant="warning" size="sm">
          Medium
        </Badge>
        <span>Feature enhancement</span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <Badge variant="secondary" size="sm">
          Low
        </Badge>
        <span>Documentation update</span>
      </div>
    </div>
  }
  code={`<div>
  <Badge variant="danger" size="sm">High</Badge>
  <span>Critical bug fix needed</span>
</div>
<div>
  <Badge variant="warning" size="sm">Medium</Badge>
  <span>Feature enhancement</span>
</div>
<div>
  <Badge variant="secondary" size="sm">Low</Badge>
  <span>Documentation update</span>
</div>`}
/>

## Count Badges

<ComponentShowcase
  title="Notification Counts"
  description="Badges displaying numerical counts for notifications or items."
  component={
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
      <div style={{ position: 'relative', display: 'inline-block' }}>
        <Button variant="ghost">Messages</Button>
        <Badge
          variant="danger"
          size="sm"
          style={{
            position: 'absolute',
            top: '-8px',
            right: '-8px',
            minWidth: '20px',
            height: '20px',
            borderRadius: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.75rem',
          }}
        >
          3
        </Badge>
      </div>
      <div style={{ position: 'relative', display: 'inline-block' }}>
        <Button variant="ghost">Notifications</Button>
        <Badge
          variant="primary"
          size="sm"
          style={{
            position: 'absolute',
            top: '-8px',
            right: '-8px',
            minWidth: '20px',
            height: '20px',
            borderRadius: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.75rem',
          }}
        >
          12
        </Badge>
      </div>
    </div>
  }
  code={`<div className="relative">
  <Button variant="ghost">Messages</Button>
  <Badge 
    variant="danger" 
    size="sm" 
    className="notification-badge"
  >
    3
  </Badge>
</div>`}
/>

## In Content Context

<ComponentShowcase
  title="User Profile Card with Badges"
  description="Badges integrated within a user profile card."
  component={
    <Card style={{ maxWidth: '350px' }}>
      <CardHeader>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
          }}
        >
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0' }}>Sarah Johnson</h4>
            <p
              style={{ margin: 0, color: 'var(--ifm-color-content-secondary)' }}
            >
              Senior Developer
            </p>
          </div>
          <Badge variant="success" size="sm">
            Online
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div style={{ marginBottom: '1rem' }}>
          <h5 style={{ margin: '0 0 0.5rem 0', fontSize: '0.9rem' }}>Skills</h5>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.375rem' }}>
            <Badge variant="primary" size="sm">
              React
            </Badge>
            <Badge variant="secondary" size="sm">
              TypeScript
            </Badge>
            <Badge variant="success" size="sm">
              Node.js
            </Badge>
            <Badge variant="warning" size="sm">
              Python
            </Badge>
          </div>
        </div>
        <div>
          <h5 style={{ margin: '0 0 0.5rem 0', fontSize: '0.9rem' }}>
            Department
          </h5>
          <Badge variant="primary">Engineering</Badge>
        </div>
      </CardContent>
    </Card>
  }
  code={`<Card>
  <CardHeader>
    <div className="profile-header">
      <div>
        <h4>Sarah Johnson</h4>
        <p>Senior Developer</p>
      </div>
      <Badge variant="success" size="sm">Online</Badge>
    </div>
  </CardHeader>
  <CardContent>
    <div>
      <h5>Skills</h5>
      <div className="skill-badges">
        <Badge variant="primary" size="sm">React</Badge>
        <Badge variant="secondary" size="sm">TypeScript</Badge>
        <Badge variant="success" size="sm">Node.js</Badge>
      </div>
    </div>
    <div>
      <h5>Department</h5>
      <Badge variant="primary">Engineering</Badge>
    </div>
  </CardContent>
</Card>`}
/>

<ComponentShowcase
  title="Task List with Status Badges"
  description="Badges used to indicate task status in a list."
  component={
    <Card style={{ maxWidth: '400px' }}>
      <CardHeader>
        <h4 style={{ margin: 0 }}>Project Tasks</h4>
      </CardHeader>
      <CardContent>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <span>Set up development environment</span>
            <Badge variant="success" size="sm">
              Complete
            </Badge>
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <span>Design system components</span>
            <Badge variant="warning" size="sm">
              In Progress
            </Badge>
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <span>Write documentation</span>
            <Badge variant="secondary" size="sm">
              Todo
            </Badge>
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <span>Deploy to production</span>
            <Badge variant="secondary" size="sm">
              Todo
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  }
  code={`<Card>
  <CardHeader>
    <h4>Project Tasks</h4>
  </CardHeader>
  <CardContent>
    <div className="task-list">
      <div className="task-item">
        <span>Set up development environment</span>
        <Badge variant="success" size="sm">Complete</Badge>
      </div>
      <div className="task-item">
        <span>Design system components</span>
        <Badge variant="warning" size="sm">In Progress</Badge>
      </div>
      <div className="task-item">
        <span>Write documentation</span>
        <Badge variant="secondary" size="sm">Todo</Badge>
      </div>
    </div>
  </CardContent>
</Card>`}
/>

</UIThemeProvider>

## Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'variant',
      type: "'primary' | 'secondary' | 'success' | 'warning' | 'danger'",
      default: "'primary'",
      description: 'Visual style variant of the badge',
    },
    {
      name: 'size',
      type: "'sm' | 'md' | 'lg'",
      default: "'md'",
      description: 'Size of the badge',
    },
    {
      name: 'children',
      type: 'React.ReactNode',
      required: true,
      description: 'Badge content (text, numbers, icons)',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

## Accessibility

The Badge component follows accessibility guidelines:

- **Semantic meaning**: Uses appropriate ARIA labels when needed
- **Color independence**: Doesn't rely solely on color to convey information
- **Text contrast**: Maintains proper contrast ratios for readability
- **Screen reader support**: Content is properly announced by assistive technologies

## Best Practices

### Do ✅

- Use badges to highlight important information
- Keep badge text short and concise
- Use consistent color meanings throughout your application
- Position badges logically relative to their associated content
- Use appropriate sizes for the context

### Don't ❌

- Don't overuse badges as they can become noise
- Don't use badges for large amounts of text
- Don't rely solely on color to convey critical information
- Don't make badges too small to read comfortably
- Don't use too many different badge variants in one interface

## Common Patterns

### Notification Badges

```tsx
// Notification count badge
<div className="relative">
  <Button>Messages</Button>
  <Badge className="notification-badge" variant="danger">
    {unreadCount}
  </Badge>
</div>
```

### Status Indicators

```tsx
// User status with badge
<div className="user-info">
  <span>John Doe</span>
  <Badge variant={user.isOnline ? 'success' : 'secondary'}>
    {user.isOnline ? 'Online' : 'Offline'}
  </Badge>
</div>
```

### Filter Tags

```tsx
// Removable filter badges
<div className="filter-badges">
  {filters.map((filter) => (
    <Badge key={filter.id} variant="primary">
      {filter.label}
      <button onClick={() => removeFilter(filter.id)}>×</button>
    </Badge>
  ))}
</div>
```
