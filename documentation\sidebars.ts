import type { SidebarsConfig } from '@docusaurus/plugin-content-docs';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */
const sidebars: SidebarsConfig = {
  // Main documentation sidebar
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: 'Getting Started',
      items: [{ type: 'autogenerated', dirName: 'tutorial-basics' }],
    },
    {
      type: 'category',
      label: 'Advanced',
      items: [{ type: 'autogenerated', dirName: 'tutorial-extras' }],
    },
  ],

  // Architecture documentation sidebar
  architectureSidebar: [
    {
      type: 'category',
      label: 'Architecture',
      items: [{ type: 'autogenerated', dirName: 'architecture' }],
    },
  ],

  // Coding practices sidebar
  codingPracticesSidebar: [
    {
      type: 'category',
      label: 'Coding Practices',
      items: [{ type: 'autogenerated', dirName: 'coding-practices' }],
    },
  ],

  // Components showcase sidebar
  componentsSidebar: [
    {
      type: 'category',
      label: 'UI Components',
      items: [{ type: 'autogenerated', dirName: 'components' }],
    },
  ],
};

export default sidebars;
