import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface BarChartData {
  label: string;
  value: number;
}

export interface BarChartProps {
  data: BarChartData[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  color?: string;
  className?: string;
}

export const BarChart: React.FC<BarChartProps> = ({
  data,
  width = 400,
  height = 300,
  margin = { top: 20, right: 20, bottom: 40, left: 40 },
  color = '#3b82f6',
  className = '',
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const xScale = d3
      .scaleBand()
      .domain(data.map((d) => d.label))
      .range([0, innerWidth])
      .padding(0.1);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(data, (d: BarChartData) => d.value) || 0])
      .range([innerHeight, 0]);

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add bars
    g.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', (d: BarChartData) => xScale(d.label)!)
      .attr('width', xScale.bandwidth())
      .attr('y', (d: BarChartData) => yScale(d.value))
      .attr('height', (d: BarChartData) => innerHeight - yScale(d.value))
      .attr('fill', color)
      .style('cursor', 'pointer')
      .on('mouseover', function (event, d: BarChartData) {
        d3.select(this).attr('opacity', 0.8);

        // Add tooltip
        const tooltip = d3
          .select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip
          .html(`${d.label}: ${d.value}`)
          .style('left', event.pageX + 10 + 'px')
          .style('top', event.pageY - 10 + 'px');
      })
      .on('mouseout', function () {
        d3.select(this).attr('opacity', 1);
        d3.selectAll('.tooltip').remove();
      });

    // Add x-axis
    const xAxis = g
      .append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    xAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    xAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add y-axis
    const yAxis = g.append('g').call(d3.axisLeft(yScale));

    yAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    yAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');
  }, [data, width, height, margin, color]);

  return (
    <div className={className}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ display: 'block' }}
      />
    </div>
  );
};
