import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { ConditionalHeader } from '../components/ConditionalHeader';
import { ThemeProvider } from '../components/ThemeProvider';
import { ConfigProvider } from '../lib/contexts/config-provider';
import { ThreatsProvider } from '../lib/contexts/ThreatsContext';

// Import global error handler to set up error handling
import '../lib/global-error-handler';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Telesoft UI - IntSOC',
  description: 'IntSOC User Interface for Telesoft',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${inter.className} bg-background-primary text-text-primary min-h-screen`}
      >
        <ConfigProvider>
          <ThemeProvider>
            <ThreatsProvider>
              <ConditionalHeader />
              {children}
            </ThreatsProvider>
          </ThemeProvider>
        </ConfigProvider>
      </body>
    </html>
  );
}
