# @telesoft/react-hooks

Common React hooks for WebSocket connections, data fetching, and state management.

## Features

- `useWebSocket` - Standardized WebSocket connection management with auto-reconnect
- `useDataFetching` - Data fetching with error handling, retries, and caching
- `useConnectionStatus` - Network connection monitoring

## Installation

```bash
pnpm add @telesoft/react-hooks
```

## Usage

### useWebSocket

```typescript
import { useWebSocket } from '@telesoft/react-hooks';

const MyComponent = () => {
  const { data, isConnected, error, connect, disconnect } = useWebSocket({
    url: 'wss://api.example.com/ws',
    autoConnect: true,
    reconnectInterval: 5000
  });

  return (
    <div>
      Status: {isConnected ? 'Connected' : 'Disconnected'}
      {error && <p>Error: {error}</p>}
    </div>
  );
};
```

### useDataFetching

```typescript
import { useDataFetching } from '@telesoft/react-hooks';

const MyComponent = () => {
  const { data, loading, error, refetch } = useDataFetching(
    () => fetch('/api/data').then(res => res.json()),
    { autoFetch: true, retryAttempts: 3 }
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return <div>{JSON.stringify(data)}</div>;
};
```

### useConnectionStatus

```typescript
import { useConnectionStatus } from '@telesoft/react-hooks';

const MyComponent = () => {
  const { isOnline, isConnected, checkConnection } = useConnectionStatus({
    endpoint: '/api/health',
    checkInterval: 30000
  });

  return (
    <div>
      Online: {isOnline ? 'Yes' : 'No'}
      Backend Connected: {isConnected ? 'Yes' : 'No'}
    </div>
  );
};
```
