import * as React from 'react';

export interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  children: React.ReactNode;
}

export interface AlertTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
}

export interface AlertDescriptionProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  ({ className = '', variant = 'default', children, ...props }, ref) => {
    const baseClasses = 'relative w-full rounded-lg border p-4';

    const variantClasses = {
      default:
        'border-border-primary bg-background-secondary text-text-primary',
      success:
        'border-cyber-matrix-500/50 bg-cyber-matrix-500/10 text-cyber-matrix-500',
      warning:
        'border-cyber-warning-500/50 bg-cyber-warning-500/10 text-cyber-warning-500',
      danger:
        'border-cyber-danger-500/50 bg-cyber-danger-500/10 text-cyber-danger-500',
      info: 'border-primary-500/50 bg-primary-500/10 text-primary-500',
    };

    const classes =
      `${baseClasses} ${variantClasses[variant]} ${className}`.trim();

    return (
      <div ref={ref} role="alert" className={classes} {...props}>
        {children}
      </div>
    );
  },
);
Alert.displayName = 'Alert';

export const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  AlertTitleProps
>(({ className = '', children, ...props }, ref) => (
  <h5
    ref={ref}
    className={`mb-1 font-medium leading-none tracking-tight ${className}`.trim()}
    {...props}
  >
    {children}
  </h5>
));
AlertTitle.displayName = 'AlertTitle';

export const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  AlertDescriptionProps
>(({ className = '', children, ...props }, ref) => (
  <div
    ref={ref}
    className={`text-sm opacity-90 ${className}`.trim()}
    {...props}
  >
    {children}
  </div>
));
AlertDescription.displayName = 'AlertDescription';
