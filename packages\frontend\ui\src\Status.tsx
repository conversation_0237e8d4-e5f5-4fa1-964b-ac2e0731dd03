import * as React from 'react';

export interface StatusProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'online' | 'offline' | 'warning' | 'error' | 'processing';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  label?: string;
  children?: React.ReactNode;
}

export const Status = React.forwardRef<HTMLDivElement, StatusProps>(
  (
    {
      className = '',
      variant = 'online',
      size = 'md',
      animated = true,
      label,
      children,
      ...props
    },
    ref,
  ) => {
    const sizeClasses = {
      sm: 'w-2 h-2',
      md: 'w-3 h-3',
      lg: 'w-4 h-4',
    };

    const variantClasses = {
      online: 'bg-cyber-matrix-500',
      offline: 'bg-slate-500',
      warning: 'bg-cyber-warning-500',
      error: 'bg-cyber-danger-500',
      processing: 'bg-primary-500',
    };

    const animationClass =
      animated && (variant === 'online' || variant === 'processing')
        ? 'animate-pulse'
        : '';

    const dotClasses =
      `rounded-full ${sizeClasses[size]} ${variantClasses[variant]} ${animationClass}`.trim();

    if (label || children) {
      return (
        <div
          ref={ref}
          className={`flex items-center gap-2 ${className}`.trim()}
          {...props}
        >
          <div className={dotClasses} />
          {label && (
            <span className="text-sm text-text-secondary">{label}</span>
          )}
          {children}
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={`${dotClasses} ${className}`.trim()}
        {...props}
      />
    );
  },
);

Status.displayName = 'Status';
