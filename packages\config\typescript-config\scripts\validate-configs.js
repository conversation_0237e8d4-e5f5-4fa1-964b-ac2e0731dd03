#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Validates TypeScript configuration files
 */

const configs = [
  { file: 'base.json', name: 'Base Configuration' },
  { file: 'react-library.json', name: 'React Library Configuration' },
  {
    file: 'react-library-jest.json',
    name: 'React Library with Jest Configuration',
  },
  { file: 'nextjs.json', name: 'Next.js Configuration' },
  { file: 'node.json', name: 'Node.js Configuration' },
  { file: 'strict.json', name: 'Strict Configuration' },
];

console.log('🔍 Validating TypeScript configurations...\n');

let hasErrors = false;

configs.forEach(({ file, name }) => {
  console.log(`Validating ${name} (${file}):`);

  try {
    // 1. Validate JSON syntax
    const configPath = path.join(process.cwd(), file);
    if (!fs.existsSync(configPath)) {
      console.error(`  ✗ File not found: ${file}`);
      hasErrors = true;
      return;
    }

    const content = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(content);
    console.log(`  ✓ Valid JSON syntax`);

    // 2. Validate schema if specified
    if (config.$schema) {
      console.log(`  ✓ Has schema: ${config.$schema}`);
    }

    // 3. Validate extends path if specified
    if (config.extends) {
      const extendsValue = Array.isArray(config.extends)
        ? config.extends
        : [config.extends];
      let allExtendsValid = true;

      for (const extend of extendsValue) {
        // Check if it's a node_modules package or a relative path
        if (extend.startsWith('@') || !extend.startsWith('.')) {
          // It's a package reference, skip file existence check
          console.log(`  ✓ Extends package: ${extend}`);
        } else {
          // It's a relative path, check if file exists
          const extendsPath = path.resolve(path.dirname(configPath), extend);
          if (fs.existsSync(extendsPath)) {
            console.log(`  ✓ Extends path exists: ${extend}`);
          } else {
            console.error(`  ✗ Extends path not found: ${extend}`);
            hasErrors = true;
            allExtendsValid = false;
          }
        }
      }
    }

    // 4. Validate compilerOptions
    if (config.compilerOptions) {
      const compilerOptions = config.compilerOptions;
      console.log(
        `  ✓ Has compilerOptions with ${Object.keys(compilerOptions).length} options`,
      );

      // Check for common required options
      const requiredForBase = ['target', 'module'];
      if (file === 'base.json') {
        const missingOptions = requiredForBase.filter(
          (opt) => !(opt in compilerOptions),
        );
        if (missingOptions.length > 0) {
          console.error(
            `  ✗ Missing required options: ${missingOptions.join(', ')}`,
          );
          hasErrors = true;
        } else {
          console.log(`  ✓ Has all required base options`);
        }
      }
    }

    // 5. Basic structure validation
    if (config.display) {
      console.log(`  ✓ Has display name: ${config.display}`);
    }

    console.log(`  ✅ ${name} is valid\n`);
  } catch (error) {
    console.error(`  ✗ Validation failed: ${error.message}\n`);
    hasErrors = true;
  }
});

if (hasErrors) {
  console.error('❌ Some TypeScript configurations have errors');
  process.exit(1);
} else {
  console.log('✅ All TypeScript configurations are valid');
}
