import React, { useState } from 'react';
import CodeBlock from '@theme/CodeBlock';
import '../css/ui-components.css';

interface ComponentShowcaseProps {
  title: string;
  description: string;
  component: React.ReactNode;
  code: string;
  props?: Array<{
    name: string;
    type: string;
    default?: string;
    description: string;
    required?: boolean;
  }>;
}

export function ComponentShowcase({
  title,
  description,
  component,
  code,
  props = []
}: ComponentShowcaseProps) {
  const [showCode, setShowCode] = useState(false);

  return (
    <div className="component-showcase">
      <div className="showcase-header">
        <h3>{title}</h3>
        <p>{description}</p>
      </div>

      <div className="showcase-preview">
        <div className="preview-container ui-component-demo">
          <div className="showcase-container">
            {component}
          </div>
        </div>

        <div className="preview-controls">
          <button
            onClick={() => setShowCode(!showCode)}
            className="toggle-code-btn"
          >
            {showCode ? 'Hide Code' : 'Show Code'}
          </button>
        </div>
      </div>

      {showCode && (
        <div className="showcase-code">
          <CodeBlock language="tsx">{code}</CodeBlock>
        </div>
      )}

      {props.length > 0 && (
        <div className="showcase-props">
          <h4>Props</h4>
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Default</th>
                <th>Required</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              {props.map((prop) => (
                <tr key={prop.name}>
                  <td><code>{prop.name}</code></td>
                  <td><code>{prop.type}</code></td>
                  <td>{prop.default ? <code>{prop.default}</code> : '-'}</td>
                  <td>{prop.required ? '✓' : '-'}</td>
                  <td>{prop.description}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
