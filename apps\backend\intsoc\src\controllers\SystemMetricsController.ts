import { Router, Router as ExpressRouter, Request, Response } from 'express';
import SystemMetricsService from '../services/SystemMetricsService';
import UnifiedWebSocketService from '../services/UnifiedWebSocketService';

const router: ExpressRouter = Router();
const metricsService = SystemMetricsService.getInstance();
const wsService = UnifiedWebSocketService.getInstance();

// Get current system metrics
router.get('/', async (req: Request, res: Response): Promise<void> => {
  try {
    const metrics = await metricsService.getCurrentMetrics();
    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting system metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system metrics',
      timestamp: new Date().toISOString(),
    });
  }
});

// Get WebSocket connection info
router.get('/websocket/info', (req: Request, res: Response): void => {
  res.json({
    success: true,
    data: {
      connectionCount: wsService.getConnectionCount('metrics'),
      subscriberCount: metricsService.getSubscriberCount(),
      endpoint: '/ws/metrics',
    },
    timestamp: new Date().toISOString(),
  });
});

// Get simplified metrics for quick polling (fallback)
router.get('/simple', async (req: Request, res: Response): Promise<void> => {
  try {
    const metrics = await metricsService.getCurrentMetrics();

    // Return simplified version
    res.json({
      success: true,
      data: {
        cpu: metrics.cpu.system,
        memory: {
          used: metrics.memory.used,
          total: metrics.memory.total,
          percent: metrics.memory.usagePercent,
        },
        uptime: metrics.system.uptime,
        timestamp: metrics.timestamp,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting simple metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system metrics',
      timestamp: new Date().toISOString(),
    });
  }
});

export default class SystemMetricsController {
  constructor(parentRouter: ExpressRouter) {
    parentRouter.use('/system-metrics', router);
  }

  /**
   * Cleanup method for consistency with other controllers
   */
  public cleanup(): void {
    console.log('SystemMetricsController: Cleaning up resources');

    try {
      // Stop metrics monitoring if it's running
      metricsService.stopMonitoring();

      console.log('SystemMetricsController: Cleanup completed successfully');
    } catch (error) {
      console.error('SystemMetricsController: Error during cleanup:', error);
    }
  }
}

export { router as systemMetricsRouter };
