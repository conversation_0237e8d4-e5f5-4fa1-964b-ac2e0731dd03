export interface TimestampFormatOptions {
  format?: 'datetime' | 'date' | 'time' | 'relative';
  locale?: string;
  timeZone?: string;
  includeSeconds?: boolean;
  use24Hour?: boolean;
}

/**
 * Formats a timestamp to a human-readable string
 * Handles both seconds and milliseconds timestamps
 */
export function formatTimestamp(
  timestamp: number,
  options: TimestampFormatOptions = {},
): string {
  const {
    format = 'datetime',
    locale = 'en-GB',
    timeZone,
    includeSeconds = true,
    use24Hour = true,
  } = options;

  // Handle invalid/missing timestamps
  if (!timestamp || isNaN(timestamp)) {
    return '--:--:--';
  }

  // Determine if timestamp is in seconds or milliseconds
  let date: Date;
  if (timestamp.toString().length >= 13) {
    // Assume milliseconds
    date = new Date(timestamp);
  } else {
    // Assume seconds, convert to milliseconds
    date = new Date(timestamp * 1000);
  }

  // Check if the resulting date is valid
  if (isNaN(date.getTime())) {
    return '--:--:--';
  }

  const formatOptions: Intl.DateTimeFormatOptions = {
    timeZone,
    hour12: !use24Hour,
  };

  switch (format) {
    case 'date':
      formatOptions.day = '2-digit';
      formatOptions.month = '2-digit';
      formatOptions.year = 'numeric';
      break;

    case 'time':
      formatOptions.hour = '2-digit';
      formatOptions.minute = '2-digit';
      if (includeSeconds) {
        formatOptions.second = '2-digit';
      }
      break;

    case 'datetime':
      formatOptions.day = '2-digit';
      formatOptions.month = '2-digit';
      formatOptions.year = 'numeric';
      formatOptions.hour = '2-digit';
      formatOptions.minute = '2-digit';
      if (includeSeconds) {
        formatOptions.second = '2-digit';
      }
      break;

    case 'relative':
      return formatRelativeTime(date);

    default:
      return date.toLocaleString(locale, formatOptions);
  }

  return date.toLocaleString(locale, formatOptions);
}

/**
 * Formats a date as relative time (e.g., "2 minutes ago", "in 1 hour")
 */
export function formatRelativeTime(
  date: Date,
  baseDate: Date = new Date(),
): string {
  const diffMs = baseDate.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (Math.abs(diffSeconds) < 60) {
    return diffSeconds >= 0 ? 'just now' : 'in a few seconds';
  } else if (Math.abs(diffMinutes) < 60) {
    const mins = Math.abs(diffMinutes);
    const suffix = diffMinutes >= 0 ? 'ago' : 'from now';
    return `${mins} minute${mins !== 1 ? 's' : ''} ${suffix}`;
  } else if (Math.abs(diffHours) < 24) {
    const hours = Math.abs(diffHours);
    const suffix = diffHours >= 0 ? 'ago' : 'from now';
    return `${hours} hour${hours !== 1 ? 's' : ''} ${suffix}`;
  } else if (Math.abs(diffDays) < 7) {
    const days = Math.abs(diffDays);
    const suffix = diffDays >= 0 ? 'ago' : 'from now';
    return `${days} day${days !== 1 ? 's' : ''} ${suffix}`;
  } else {
    // For longer periods, show the actual date
    return formatTimestamp(date.getTime(), { format: 'date' });
  }
}

/**
 * Legacy format function for compatibility with existing code
 */
export function formatTimestampLegacy(timestamp: number): string {
  // Check if timestamp is in seconds or milliseconds
  const date =
    timestamp < 10000000000 ? new Date(timestamp * 1000) : new Date(timestamp);

  // Format as DD/MM/YYYY HH:MM:SS (24-hour clock)
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
}

/**
 * Formats timestamp for dashboard time display (HH:MM:SS format)
 */
export function formatTimestampTime(timestamp: number): string {
  // Handle invalid/missing timestamps
  if (!timestamp || isNaN(timestamp)) {
    return '--:--:--';
  }

  // Try different timestamp formats
  let date: Date;

  // If timestamp looks like it's already in milliseconds (13+ digits)
  if (timestamp.toString().length >= 13) {
    date = new Date(timestamp);
  } else {
    // Assume it's in seconds, convert to milliseconds
    date = new Date(timestamp * 1000);
  }

  // Check if the resulting date is valid
  if (isNaN(date.getTime())) {
    return '--:--:--';
  }

  return date.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}
