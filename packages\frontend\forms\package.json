{"name": "@telesoft/forms", "version": "1.0.0", "description": "Reusable form field components built on top of @telesoft/ui", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "format": "prettier --write . --ignore-path ../../../.prettierignore"}, "dependencies": {"react": "^18.2.0", "@telesoft/ui": "workspace:*", "@telesoft/utils": "workspace:*"}, "devDependencies": {"@telesoft/typescript-config": "workspace:*", "@types/react": "^18.2.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist"]}