import { EventEmitter } from 'events';
import WebSocket from 'ws';

export interface ConnectionConfig {
  url: string;
  reconnectDelayMs?: number;
  maxReconnectAttempts?: number;
  connectionTimeoutMs?: number;
  enableAutoReconnect?: boolean;
  enableLogging?: boolean;
}

export interface ConnectionEvents {
  connected: (connectionId: string) => void;
  disconnected: (connectionId: string, code: number, reason?: string) => void;
  message: (connectionId: string, data: WebSocket.Data) => void;
  error: (connectionId: string, error: Error) => void;
  reconnecting: (connectionId: string, attempt: number) => void;
  reconnectFailed: (connectionId: string) => void;
}

interface Connection {
  id: string;
  config: Required<ConnectionConfig>;
  ws: WebSocket | null;
  reconnectInterval: ReturnType<typeof setTimeout> | null;
  reconnectAttempts: number;
  isManuallyDisconnected: boolean;
}

/**
 * Manages multiple WebSocket connections with automatic reconnection
 */
export class WebSocketConnectionManager extends EventEmitter {
  private connections = new Map<string, Connection>();
  private static instance: WebSocketConnectionManager;

  private constructor() {
    super();
  }

  public static getInstance(): WebSocketConnectionManager {
    if (!WebSocketConnectionManager.instance) {
      WebSocketConnectionManager.instance = new WebSocketConnectionManager();
    }
    return WebSocketConnectionManager.instance;
  }

  /**
   * Create a new WebSocket connection
   */
  public async createConnection(
    connectionId: string,
    config: ConnectionConfig,
  ): Promise<void> {
    if (this.connections.has(connectionId)) {
      throw new Error(`Connection ${connectionId} already exists`);
    }

    const connection: Connection = {
      id: connectionId,
      config: {
        reconnectDelayMs: 5000,
        maxReconnectAttempts: -1,
        connectionTimeoutMs: 10000,
        enableAutoReconnect: true,
        enableLogging: true,
        ...config,
      },
      ws: null,
      reconnectInterval: null,
      reconnectAttempts: 0,
      isManuallyDisconnected: false,
    };

    this.connections.set(connectionId, connection);
    return this.connect(connectionId);
  }

  /**
   * Connect to WebSocket server
   */
  public async connect(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      throw new Error(`Connection ${connectionId} not found`);
    }

    if (connection.ws?.readyState === WebSocket.OPEN) {
      this.log(connectionId, 'Already connected');
      return;
    }

    connection.isManuallyDisconnected = false;
    return this.establishConnection(connection);
  }

  /**
   * Disconnect from WebSocket server
   */
  public disconnect(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    this.log(connectionId, 'Manually disconnecting');
    connection.isManuallyDisconnected = true;
    this.clearReconnectTimer(connection);

    if (connection.ws) {
      connection.ws.close();
      connection.ws = null;
    }
  }

  /**
   * Send data through a specific connection
   */
  public send(connectionId: string, data: string | Buffer): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection?.ws || connection.ws.readyState !== WebSocket.OPEN) {
      this.log(connectionId, 'Cannot send data: not connected', 'warn');
      return false;
    }

    try {
      connection.ws.send(data);
      return true;
    } catch (error) {
      this.log(connectionId, `Error sending data: ${error}`, 'error');
      return false;
    }
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(connectionId: string): string {
    const connection = this.connections.get(connectionId);
    if (!connection?.ws) return 'disconnected';

    switch (connection.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  /**
   * Check if connection is active
   */
  public isConnected(connectionId: string): boolean {
    const connection = this.connections.get(connectionId);
    return connection?.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection statistics
   */
  public getStats(connectionId: string) {
    const connection = this.connections.get(connectionId);
    if (!connection) return null;

    return {
      status: this.getConnectionStatus(connectionId),
      reconnectAttempts: connection.reconnectAttempts,
      url: connection.config.url,
      isAutoReconnectEnabled: connection.config.enableAutoReconnect,
    };
  }

  /**
   * Remove a connection
   */
  public removeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    this.disconnect(connectionId);
    this.connections.delete(connectionId);
    this.log(connectionId, 'Connection removed');
  }

  /**
   * Get all connection IDs
   */
  public getConnectionIds(): string[] {
    return Array.from(this.connections.keys());
  }

  /**
   * Cleanup all connections
   */
  public cleanup(): void {
    for (const connectionId of this.connections.keys()) {
      this.removeConnection(connectionId);
    }
    this.removeAllListeners();
  }

  private async establishConnection(connection: Connection): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.log(connection.id, `Connecting to ${connection.config.url}`);
        connection.ws = new WebSocket(connection.config.url);

        const connectionTimeout = setTimeout(() => {
          if (connection.ws?.readyState === WebSocket.CONNECTING) {
            this.log(connection.id, 'Connection timeout', 'error');
            connection.ws.terminate();
            reject(new Error('Connection timeout'));
          }
        }, connection.config.connectionTimeoutMs);

        connection.ws.on('open', () => {
          clearTimeout(connectionTimeout);
          this.log(connection.id, 'Connection established');
          connection.reconnectAttempts = 0;
          this.clearReconnectTimer(connection);
          this.emit('connected', connection.id);
          resolve();
        });

        connection.ws.on('message', (data: WebSocket.Data) => {
          this.emit('message', connection.id, data);
        });

        connection.ws.on('close', (code: number, reason: Buffer) => {
          clearTimeout(connectionTimeout);
          const reasonString = reason.toString();
          this.log(
            connection.id,
            `Connection closed (${code}): ${reasonString}`,
          );
          connection.ws = null;
          this.emit('disconnected', connection.id, code, reasonString);

          if (
            !connection.isManuallyDisconnected &&
            connection.config.enableAutoReconnect
          ) {
            this.scheduleReconnect(connection);
          }
        });

        connection.ws.on('error', (error: Error) => {
          clearTimeout(connectionTimeout);
          this.log(connection.id, `Error: ${error.message}`, 'error');
          connection.ws = null;
          this.emit('error', connection.id, error);

          if (
            !connection.isManuallyDisconnected &&
            connection.config.enableAutoReconnect
          ) {
            this.scheduleReconnect(connection);
          }

          reject(error);
        });
      } catch (error) {
        this.log(
          connection.id,
          `Failed to create connection: ${error}`,
          'error',
        );
        reject(error);
      }
    });
  }

  private scheduleReconnect(connection: Connection): void {
    if (connection.reconnectInterval || connection.isManuallyDisconnected) {
      return;
    }

    if (
      connection.config.maxReconnectAttempts > 0 &&
      connection.reconnectAttempts >= connection.config.maxReconnectAttempts
    ) {
      this.log(connection.id, `Max reconnect attempts exceeded`, 'error');
      this.emit('reconnectFailed', connection.id);
      return;
    }

    connection.reconnectAttempts++;
    this.log(
      connection.id,
      `Scheduling reconnect in ${connection.config.reconnectDelayMs}ms (attempt ${connection.reconnectAttempts})`,
    );
    this.emit('reconnecting', connection.id, connection.reconnectAttempts);

    connection.reconnectInterval = setTimeout(async () => {
      connection.reconnectInterval = null;
      try {
        await this.establishConnection(connection);
      } catch (error) {
        this.log(connection.id, `Reconnect attempt failed: ${error}`, 'error');
      }
    }, connection.config.reconnectDelayMs);
  }

  private clearReconnectTimer(connection: Connection): void {
    if (connection.reconnectInterval) {
      clearTimeout(connection.reconnectInterval);
      connection.reconnectInterval = null;
    }
  }

  private log(
    connectionId: string,
    message: string,
    level: 'info' | 'warn' | 'error' = 'info',
  ): void {
    const connection = this.connections.get(connectionId);
    if (!connection?.config.enableLogging) return;

    const prefix = `WebSocket[${connectionId}]:`;
    switch (level) {
      case 'warn':
        console.warn(`${prefix} ${message}`);
        break;
      case 'error':
        console.error(`${prefix} ${message}`);
        break;
      default:
        console.log(`${prefix} ${message}`);
        break;
    }
  }
}
