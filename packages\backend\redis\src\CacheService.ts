import { RedisClient } from './RedisClient';
import type { RedisConfig, RedisConnectionOptions } from './types';

/**
 * Generic Redis cache service with configurable Redis connection
 */
export class CacheService {
  private redisClient: RedisClient;
  private isConnected = false;

  constructor(
    redisConfig: RedisConfig,
    connectionOptions?: RedisConnectionOptions,
  ) {
    this.redisClient = new RedisClient(redisConfig, {
      onConnect: () => {
        this.isConnected = true;
        console.log('Cache service connected');
      },
      onError: (error: Error) => {
        this.isConnected = false;
        console.error('Cache service error:', error.message);
      },
      onClose: () => {
        this.isConnected = false;
        console.log('Cache service disconnected');
      },
      ...connectionOptions,
    });
  }

  /**
   * Initialize cache connection
   */
  public async initialize(): Promise<void> {
    try {
      await this.redisClient.set('cache:health', 'ok', 5);
      await this.redisClient.delete('cache:health');
      console.log('Cache service initialized');
    } catch (error) {
      console.warn(
        'Cache service initialization failed:',
        error instanceof Error ? error.message : error,
      );
    }
  }

  /**
   * Get data from cache
   */
  public async get<T = unknown>(key: string): Promise<T | null> {
    if (!this.isConnected) return null;

    try {
      return (await this.redisClient.get(key)) as T;
    } catch (error) {
      console.error(
        `Cache get failed for key "${key}":`,
        error instanceof Error ? error.message : error,
      );
      return null;
    }
  }

  /**
   * Store data in cache
   */
  public async set(
    key: string,
    value: unknown,
    ttlSeconds: number = 3600,
  ): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      await this.redisClient.set(key, value, ttlSeconds);
      return true;
    } catch (error) {
      console.error(
        `Cache set failed for key "${key}":`,
        error instanceof Error ? error.message : error,
      );
      return false;
    }
  }

  /**
   * Delete data from cache
   */
  public async delete(key: string): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      await this.redisClient.delete(key);
      return true;
    } catch (error) {
      console.error(
        `Cache delete failed for key "${key}":`,
        error instanceof Error ? error.message : error,
      );
      return false;
    }
  }

  /**
   * Check if key exists in cache
   */
  public async exists(key: string): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      return await this.redisClient.exists(key);
    } catch (error) {
      console.error(
        `Cache exists check failed for key "${key}":`,
        error instanceof Error ? error.message : error,
      );
      return false;
    }
  }

  /**
   * Get multiple keys at once
   */
  public async mget<T = unknown>(keys: string[]): Promise<(T | null)[]> {
    if (!this.isConnected) return keys.map(() => null);

    try {
      return await this.redisClient.getMany<T>(keys);
    } catch (error) {
      console.error(
        `Cache mget failed for keys "${keys.join(', ')}":`,
        error instanceof Error ? error.message : error,
      );
      return keys.map(() => null);
    }
  }

  /**
   * Set multiple key-value pairs at once
   */
  public async mset(
    data: Record<string, unknown>,
    ttlSeconds?: number,
  ): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      if (ttlSeconds) {
        // Redis mset doesn't support TTL, so we need to set each key individually if TTL is specified
        const promises = Object.entries(data).map(([key, value]) =>
          this.redisClient.set(key, value, ttlSeconds),
        );
        await Promise.all(promises);
      } else {
        await this.redisClient.setMany(data);
      }
      return true;
    } catch (error) {
      console.error(
        `Cache mset failed:`,
        error instanceof Error ? error.message : error,
      );
      return false;
    }
  }

  /**
   * Delete multiple keys at once
   */
  public async mdel(keys: string[]): Promise<number> {
    if (!this.isConnected) return 0;

    try {
      return await this.redisClient.deleteMany(keys);
    } catch (error) {
      console.error(
        `Cache mdel failed for keys "${keys.join(', ')}":`,
        error instanceof Error ? error.message : error,
      );
      return 0;
    }
  }

  /**
   * Get all keys matching a pattern
   */
  public async keys(pattern: string): Promise<string[]> {
    if (!this.isConnected) return [];

    try {
      return await this.redisClient.keys(pattern);
    } catch (error) {
      console.error(
        `Cache keys failed for pattern "${pattern}":`,
        error instanceof Error ? error.message : error,
      );
      return [];
    }
  }

  /**
   * Get connection status
   */
  public getStatus(): 'connected' | 'disconnected' {
    return this.isConnected ? 'connected' : 'disconnected';
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      const testKey = `health:${Date.now()}`;
      await this.redisClient.set(testKey, 'ok', 1);
      await this.redisClient.delete(testKey);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  public async getStats(): Promise<{
    connected: boolean;
    keyCount?: number;
    memoryUsage?: string;
    uptime?: number;
  }> {
    const stats = {
      connected: this.isConnected,
    };

    if (!this.isConnected) return stats;

    try {
      const client = this.redisClient.getClient();
      const info = await client.info('memory');
      const dbsize = await client.dbsize();

      return {
        ...stats,
        keyCount: dbsize,
        memoryUsage: this.parseMemoryFromInfo(info),
        uptime: this.parseUptimeFromInfo(info),
      };
    } catch (error) {
      console.error(
        'Failed to get cache stats:',
        error instanceof Error ? error.message : error,
      );
      return stats;
    }
  }

  /**
   * Cleanup
   */
  public cleanup(): void {
    try {
      this.redisClient.disconnect();
      this.isConnected = false;
    } catch (error) {
      console.error(
        'Cache cleanup error:',
        error instanceof Error ? error.message : error,
      );
    }
  }

  /**
   * Get the underlying Redis client for advanced operations
   */
  public getClient(): RedisClient {
    return this.redisClient;
  }

  private parseMemoryFromInfo(info: string): string {
    const match = info.match(/used_memory_human:(.+)/);
    return match ? match[1].trim() : 'unknown';
  }

  private parseUptimeFromInfo(info: string): number {
    const match = info.match(/uptime_in_seconds:(\d+)/);
    return match ? parseInt(match[1], 10) : 0;
  }
}
