# @telesoft/d3

A React component library for D3.js visualizations, providing reusable chart components for the Telesoft application ecosystem.

## Features

- **Bar Chart**: Responsive bar charts with customizable styling
- **Line Chart**: Interactive line charts with hover effects
- **Pie Chart**: Customizable pie/donut charts
- **Scatter Plot**: Data visualization with customizable axes
- **Area Chart**: Filled area charts for trend visualization
- **Histogram**: Data distribution visualization

## Installation

This package is part of the Telesoft UI monorepo and should be installed via workspace dependencies:

```json
{
  "dependencies": {
    "@telesoft/d3": "workspace:*"
  }
}
```

## Usage

```tsx
import { <PERSON><PERSON><PERSON>, Line<PERSON><PERSON>, Pie<PERSON>hart } from '@telesoft/d3';

// Bar Chart Example
<BarChart
  data={[
    { label: 'A', value: 10 },
    { label: 'B', value: 20 },
    { label: 'C', value: 15 }
  ]}
  width={400}
  height={300}
/>

// Line Chart Example
<LineChart
  data={[
    { x: 0, y: 10 },
    { x: 1, y: 20 },
    { x: 2, y: 15 }
  ]}
  width={400}
  height={300}
/>

// Pie Chart Example
<PieChart
  data={[
    { label: 'Category A', value: 30 },
    { label: 'Category B', value: 70 }
  ]}
  width={300}
  height={300}
/>
```

## Components

All components are built with React and D3.js, providing:

- Responsive design
- TypeScript support
- Customizable styling
- Accessibility features
- Interactive elements

## Development

```bash
# Build the package
pnpm build

# Run linting
pnpm lint

# Type checking
pnpm type-check

# Development mode with watch
pnpm dev
```
