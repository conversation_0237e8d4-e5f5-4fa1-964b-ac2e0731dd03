# @telesoft/intsoc-frontend

A modern Next.js frontend application for the Intelligence and Security Operations Center (INTSOC) platform, built with TypeScript and Tailwind CSS.

## Features

- **Next.js 15** with App Router for modern React development
- **TypeScript** for type safety and enhanced developer experience
- **Tailwind CSS** with custom color palette for cybersecurity-focused UI
- **Component Library Integration** using `@telesoft/ui` components
- **Data Visualization** with `@telesoft/d3` charts and graphs
- **Brand Consistency** through `@telesoft/branding` assets
- **Runtime Configuration** for deployment flexibility without rebuilds
- **Turbo Mode** for faster development builds

## Getting Started

### Prerequisites

- Node.js 24+
- pnpm 10+

### Installation

From the monorepo root:

```bash
pnpm install
```

### Environment Configuration

This application uses a **runtime configuration system** that allows environment variables to be changed at deployment time without rebuilding.

1. Copy the example environment file:

   ```bash
   cp .env.example .env
   ```

2. Configure your environment variables (see .env.example)

### Development

Run the development server:

```bash
# From monorepo root
pnpm --filter @telesoft/intsoc-frontend dev

# Or from this directory
pnpm dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

### Build

Build the application for production:

```bash
# From monorepo root
pnpm --filter @telesoft/intsoc-frontend build

# Or from this directory
pnpm build
```

### Packaging for Deployment

Create a production-ready package that can be deployed with different configurations:

```bash
# Build and package the application
pnpm run build:package
```

This creates a tarball in the `dist/` directory containing:

- Pre-built Next.js application
- Production dependencies only
- Runtime configuration system
- Deployment scripts

### Deployment

Extract the package and configure for your environment:

```bash
# Extract the package
tar -xzf dist/intsoc-frontend-*.tar.gz
cd intsoc-frontend

# Configure for your environment
cp .env.example .env
# Edit .env.production.local with your settings

# Install dependencies and start
pnpm install --prod
pnpm start
```

### Other Commands

```bash
# Type checking
pnpm type-check

# Linting
pnpm lint

# Start production server
pnpm start

# Clean build artifacts
pnpm clean

# Copy branding assets
pnpm copy-assets

# Create deployment package
pnpm package
```

## Project Structure

```
src/
├── app/              # Next.js App Router pages and layouts
├── components/       # React components specific to this app
└── lib/             # Utility functions and configurations
```

## Dependencies

This application uses the following internal packages:

- `@telesoft/ui` - Shared UI component library
- `@telesoft/branding` - Company logos and brand assets
- `@telesoft/d3` - Data visualization components
- `@telesoft/color-palette` - Tailwind CSS color system
- `@telesoft/types` - Shared TypeScript type definitions

## Configuration

The application uses a **runtime configuration system** that provides several advantages over traditional build-time environment variables:

- **Deployment Flexibility**: Change configuration without rebuilding
- **Environment Portability**: Same build works across all environments
- **Container Friendly**: Override config with environment variables at runtime
- **Security**: Sensitive config not embedded in client bundles

### Configuration Files

- `.env.example` - Template with all available variables
- `.env.local` - Local development overrides
- `.env.production.local` - Production-specific configuration

## Technologies

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Configuration**: Runtime API-based system
- **Package Manager**: pnpm
- **Monorepo**: Turborepo

## Development Notes

- Uses Turbo mode by default for faster development builds
- Configured with ESLint for code quality
- Type checking with TypeScript
- Integrates with the monorepo's shared packages for consistency
