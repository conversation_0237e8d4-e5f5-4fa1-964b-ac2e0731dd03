// MITRE ATT&CK Technique Database
// This is a subset of commonly used techniques for demonstration
// In a real application, this would likely be loaded from an API or database

export interface MitreAttackTechnique {
  id: string;
  name: string;
  tactic: string;
  description: string;
}

export const MITRE_ATTACK_TECHNIQUES: Record<string, MitreAttackTechnique> = {
  T1055: {
    id: 'T1055',
    name: 'Process Injection',
    tactic: 'Defense Evasion',
    description:
      'Adversaries may inject code into processes in order to evade process-based defenses as well as possibly elevate privileges.',
  },
  T1003: {
    id: 'T1003',
    name: 'OS Credential Dumping',
    tactic: 'Credential Access',
    description:
      'Adversaries may attempt to dump credentials to obtain account login and credential material.',
  },
  T1083: {
    id: 'T1083',
    name: 'File and Directory Discovery',
    tactic: 'Discovery',
    description:
      'Adversaries may enumerate files and directories or may search in specific locations of a host or network share.',
  },
  T1059: {
    id: 'T1059',
    name: 'Command and Scripting Interpreter',
    tactic: 'Execution',
    description:
      'Adversaries may abuse command and script interpreters to execute commands, scripts, or binaries.',
  },
  T1071: {
    id: 'T1071',
    name: 'Application Layer Protocol',
    tactic: 'Command and Control',
    description:
      'Adversaries may communicate using OSI application layer protocols to avoid detection/network filtering.',
  },
  T1090: {
    id: 'T1090',
    name: 'Proxy',
    tactic: 'Command and Control',
    description:
      'Adversaries may use a connection proxy to direct network traffic between systems or act as an intermediary.',
  },
  T1105: {
    id: 'T1105',
    name: 'Ingress Tool Transfer',
    tactic: 'Command and Control',
    description:
      'Adversaries may transfer tools or other files from an external system into a compromised environment.',
  },
  T1566: {
    id: 'T1566',
    name: 'Phishing',
    tactic: 'Initial Access',
    description:
      'Adversaries may send phishing messages to gain access to victim systems.',
  },
  T1078: {
    id: 'T1078',
    name: 'Valid Accounts',
    tactic: 'Defense Evasion',
    description:
      'Adversaries may obtain and abuse credentials of existing accounts as a means of gaining Initial Access.',
  },
  T1087: {
    id: 'T1087',
    name: 'Account Discovery',
    tactic: 'Discovery',
    description:
      'Adversaries may attempt to get a listing of valid accounts that they can use during an attack.',
  },
  T1057: {
    id: 'T1057',
    name: 'Process Discovery',
    tactic: 'Discovery',
    description:
      'Adversaries may attempt to get information about running processes on a system.',
  },
  T1082: {
    id: 'T1082',
    name: 'System Information Discovery',
    tactic: 'Discovery',
    description:
      'An adversary may attempt to get detailed information about the operating system and hardware.',
  },
  T1027: {
    id: 'T1027',
    name: 'Obfuscated Files or Information',
    tactic: 'Defense Evasion',
    description:
      'Adversaries may attempt to make an executable or file difficult to discover or analyze.',
  },
  T1112: {
    id: 'T1112',
    name: 'Modify Registry',
    tactic: 'Defense Evasion',
    description:
      'Adversaries may interact with the Windows Registry to hide configuration information.',
  },
  T1547: {
    id: 'T1547',
    name: 'Boot or Logon Autostart Execution',
    tactic: 'Persistence',
    description:
      'Adversaries may configure system settings to automatically execute a program during system boot.',
  },
  'T1016.001': {
    id: 'T1016.001',
    name: 'Internet Connection Discovery',
    tactic: 'Discovery',
    description:
      'Adversaries may check for Internet connectivity on compromised systems. This may be performed during automated discovery and can be accomplished in numerous ways such as using Ping, tracert on Windows, or ping and traceroute on Linux/macOS.',
  },
  'T1562.004': {
    id: 'T1562.004',
    name: 'Disable or Modify System Firewall',
    tactic: 'Defense Evasion',
    description:
      'Adversaries may disable or modify system firewalls in order to bypass controls limiting network usage.',
  },
  'T1562.007': {
    id: 'T1562.007',
    name: 'Disable or Modify Cloud Firewall',
    tactic: 'Defense Evasion',
    description:
      'Adversaries may disable or modify a firewall within a cloud environment to bypass controls that limit access to cloud resources. Cloud firewalls are separate from system firewalls that are described in Disable or Modify System Firewall.',
  },
  'T1584.007': {
    id: 'T1584.007',
    name: 'Acquire or Compromise SSL/TLS Certificates',
    tactic: 'Resource Development',
    description:
      "Adversaries may acquire or compromise SSL/TLS certificates that can be used during targeting. SSL/TLS certificates are designed to instill trust. They include information about the key, information about its owner's identity, and the digital signature of an entity that has verified the certificate's contents are correct.",
  },
  T1190: {
    id: 'T1190',
    name: 'Exploit Public-Facing Application',
    tactic: 'Initial Access',
    description:
      'Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program using software, data, or commands in order to cause unintended or unanticipated behavior. The weakness in the system can be a bug, a glitch, or a design vulnerability.',
  },
  T1049: {
    id: 'T1049',
    name: 'System Network Connections Discovery',
    tactic: 'Discovery',
    description:
      'Adversaries may attempt to get a listing of network connections to or from the compromised system they are currently accessing or from remote systems by querying for information over the network.',
  },
};

/**
 * Get MITRE ATT&CK technique information by ID
 */
export function getMitreAttackTechnique(
  id: string,
): MitreAttackTechnique | null {
  return MITRE_ATTACK_TECHNIQUES[id] || null;
}

/**
 * Get multiple MITRE ATT&CK techniques by IDs
 */
export function getMitreAttackTechniques(
  ids: string[],
): Array<MitreAttackTechnique & { found: boolean }> {
  return ids.map((id) => {
    const technique = getMitreAttackTechnique(id);
    if (technique) {
      return { ...technique, found: true };
    }
    return {
      id,
      name: `Unknown Technique (${id})`,
      tactic: 'Unknown',
      description: `No information available for technique ${id}`,
      found: false,
    };
  });
}

/**
 * Get tactic color based on tactic name
 */
export function getTacticColor(tactic: string): string {
  const tacticColors: Record<string, string> = {
    'Initial Access': 'bg-red-500/20 text-red-400 border-red-500/30',
    Execution: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
    Persistence: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    'Privilege Escalation':
      'bg-amber-500/20 text-amber-400 border-amber-500/30',
    'Defense Evasion': 'bg-green-500/20 text-green-400 border-green-500/30',
    'Credential Access': 'bg-teal-500/20 text-teal-400 border-teal-500/30',
    Discovery: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
    'Lateral Movement': 'bg-indigo-500/20 text-indigo-400 border-indigo-500/30',
    Collection: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
    'Command and Control': 'bg-pink-500/20 text-pink-400 border-pink-500/30',
    Exfiltration: 'bg-rose-500/20 text-rose-400 border-rose-500/30',
    Impact: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
    Unknown: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
  };

  return tacticColors[tactic] || tacticColors['Unknown'];
}
