# Usage Examples

## Basic Usage

```tsx
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@telesoft/d3';

// Bar Chart
const barData = [
  { label: 'Q1', value: 100 },
  { label: 'Q2', value: 150 },
  { label: 'Q3', value: 125 },
  { label: 'Q4', value: 200 },
];

<Bar<PERSON>hart data={barData} width={500} height={300} color="#3b82f6" />;

// Line Chart
const lineData = [
  { x: 0, y: 10 },
  { x: 1, y: 25 },
  { x: 2, y: 15 },
  { x: 3, y: 30 },
];

<LineChart
  data={lineData}
  width={500}
  height={300}
  color="#10b981"
  xLabel="Time"
  yLabel="Value"
/>;

// Pie Chart
const pieData = [
  { label: 'Product A', value: 45 },
  { label: 'Product B', value: 35 },
  { label: 'Product C', value: 20 },
];

<PieChart data={pieData} width={400} height={400} showLabels={true} />;
```

## Advanced Examples

### Scatter Plot with Custom Labels

```tsx
import { ScatterPlot } from '@telesoft/d3';

const scatterData = [
  { x: 10, y: 20, label: 'Data Point A' },
  { x: 15, y: 25, label: 'Data Point B' },
  { x: 20, y: 30, label: 'Data Point C' },
];

<ScatterPlot
  data={scatterData}
  width={400}
  height={300}
  color="#f59e0b"
  radius={6}
  xLabel="X Variable"
  yLabel="Y Variable"
/>;
```

### Area Chart with Custom Styling

```tsx
import { AreaChart } from '@telesoft/d3';

const areaData = [
  { x: 0, y: 5 },
  { x: 1, y: 15 },
  { x: 2, y: 10 },
  { x: 3, y: 25 },
  { x: 4, y: 20 },
];

<AreaChart
  data={areaData}
  width={600}
  height={350}
  color="#8b5cf6"
  opacity={0.7}
  xLabel="Time Period"
  yLabel="Volume"
  className="my-custom-chart"
/>;
```

### Histogram for Data Distribution

```tsx
import { Histogram } from '@telesoft/d3';

const histogramData = [
  { value: 1.2 },
  { value: 2.3 },
  { value: 2.8 },
  { value: 3.1 },
  { value: 3.5 },
  { value: 4.2 },
  { value: 4.8 },
  { value: 5.1 },
  { value: 5.9 },
];

<Histogram
  data={histogramData}
  width={500}
  height={300}
  color="#ef4444"
  bins={15}
  xLabel="Value Range"
  yLabel="Frequency"
/>;
```

## Integration with React State

```tsx
import React, { useState, useEffect } from 'react';
import { LineChart } from '@telesoft/d3';

const DynamicChart: React.FC = () => {
  const [data, setData] = useState([]);

  useEffect(() => {
    // Fetch data from API
    fetchChartData().then(setData);
  }, []);

  return (
    <div>
      <h2>Real-time Data</h2>
      <LineChart
        data={data}
        width={800}
        height={400}
        color="#06b6d4"
        xLabel="Time"
        yLabel="Metrics"
      />
    </div>
  );
};
```

## Responsive Design

```tsx
import React, { useState, useEffect } from 'react';
import { BarChart } from '@telesoft/d3';

const ResponsiveChart: React.FC = () => {
  const [dimensions, setDimensions] = useState({ width: 800, height: 400 });

  useEffect(() => {
    const handleResize = () => {
      const container = document.getElementById('chart-container');
      if (container) {
        setDimensions({
          width: container.offsetWidth,
          height: 400,
        });
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial call

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div id="chart-container" className="w-full">
      <BarChart
        data={barData}
        width={dimensions.width}
        height={dimensions.height}
        color="#3b82f6"
      />
    </div>
  );
};
```
