import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Histogram,
} from '../../index';

// Sample data for demonstrations
const barData = [
  { label: 'Jan', value: 30 },
  { label: 'Feb', value: 45 },
  { label: 'Mar', value: 35 },
  { label: 'Apr', value: 50 },
  { label: 'May', value: 40 },
];

const lineData = [
  { x: 0, y: 10 },
  { x: 1, y: 25 },
  { x: 2, y: 15 },
  { x: 3, y: 30 },
  { x: 4, y: 20 },
  { x: 5, y: 35 },
];

const pieData = [
  { label: 'Desktop', value: 60 },
  { label: 'Mobile', value: 30 },
  { label: 'Tablet', value: 10 },
];

const scatterData = [
  { x: 10, y: 20, label: 'Point A' },
  { x: 15, y: 25, label: 'Point B' },
  { x: 20, y: 30, label: 'Point C' },
  { x: 25, y: 35, label: 'Point D' },
  { x: 30, y: 40, label: 'Point E' },
];

const areaData = [
  { x: 0, y: 5 },
  { x: 1, y: 15 },
  { x: 2, y: 10 },
  { x: 3, y: 25 },
  { x: 4, y: 20 },
  { x: 5, y: 30 },
];

const histogramData = [
  { value: 1 },
  { value: 2 },
  { value: 2 },
  { value: 3 },
  { value: 3 },
  { value: 3 },
  { value: 4 },
  { value: 4 },
  { value: 5 },
  { value: 5 },
  { value: 5 },
  { value: 5 },
  { value: 6 },
  { value: 6 },
  { value: 7 },
];

export const D3ChartsDemo: React.FC = () => {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-3xl font-bold mb-8">D3 Charts Demo</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Bar Chart</h2>
          <BarChart data={barData} width={400} height={300} color="#3b82f6" />
        </div>

        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Line Chart</h2>
          <LineChart
            data={lineData}
            width={400}
            height={300}
            color="#10b981"
            xLabel="Time"
            yLabel="Value"
          />
        </div>

        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Pie Chart</h2>
          <PieChart data={pieData} width={400} height={300} />
        </div>

        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Scatter Plot</h2>
          <ScatterPlot
            data={scatterData}
            width={400}
            height={300}
            color="#f59e0b"
            xLabel="X Axis"
            yLabel="Y Axis"
          />
        </div>

        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Area Chart</h2>
          <AreaChart
            data={areaData}
            width={400}
            height={300}
            color="#8b5cf6"
            xLabel="Time"
            yLabel="Volume"
          />
        </div>

        <div className="bg-white p-4 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Histogram</h2>
          <Histogram
            data={histogramData}
            width={400}
            height={300}
            color="#ef4444"
            bins={10}
            xLabel="Value"
            yLabel="Frequency"
          />
        </div>
      </div>
    </div>
  );
};
