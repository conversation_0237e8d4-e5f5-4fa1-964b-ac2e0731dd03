import React from 'react';
import { Input } from '@telesoft/ui';

export interface NumberFieldProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  min?: string;
  max?: string;
  helperText?: string;
  error?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export const NumberField: React.FC<NumberFieldProps> = ({
  label,
  value,
  onChange,
  min,
  max,
  helperText,
  error,
  disabled,
  placeholder,
  className,
}) => (
  <Input
    type="number"
    label={label}
    value={value.toString()}
    onChange={(e) => onChange(parseInt(e.target.value) || 0)}
    min={min}
    max={max}
    helperText={helperText}
    error={error}
    disabled={disabled}
    placeholder={placeholder}
    className={className}
  />
);
