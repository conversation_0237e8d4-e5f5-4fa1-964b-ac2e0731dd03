'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { ClientConfig, fetchConfig } from './client-config';

// Default configuration used during SSR and as fallback
const DEFAULT_CONFIG: ClientConfig = {
  api: {
    baseUrl: 'http://localhost:3000', // Default to frontend server
    wsUrl: 'ws://localhost:4001/ws', // Fixed: include /ws path in fallback
    timeout: 10000,
    useProxy: true, // Default to proxy mode
    enableCache: false,
    cacheTimeout: 60000,
  },
  debug: {
    apiDebug: false,
  },
  app: {
    name: 'IntSOC',
  },
};

interface ConfigContextValue {
  config: ClientConfig;
  loading: boolean;
  error: string | null;
}

const ConfigContext = createContext<ConfigContextValue>({
  config: DEFAULT_CONFIG,
  loading: false,
  error: null,
});

export function ConfigProvider({ children }: { children: React.ReactNode }) {
  const [config, setConfig] = useState<ClientConfig>(DEFAULT_CONFIG);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadConfig = async () => {
      try {
        const fetchedConfig = await fetchConfig();
        setConfig(fetchedConfig);
        setGlobalConfig(fetchedConfig); // Update global config
        setError(null);

        console.log('[ConfigProvider] Setting global config:', {
          baseUrl: fetchedConfig.api.baseUrl,
          wsUrl: fetchedConfig.api.wsUrl,
          useProxy: fetchedConfig.api.useProxy,
        });
      } catch (err) {
        console.warn('Failed to load configuration:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to load configuration',
        );
        // Keep using default config on error
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, []);

  // Refresh service connections when config changes
  useEffect(() => {
    if (!loading && config) {
      // Dynamically import services to avoid circular dependencies
      Promise.all([
        import('./services/threats'),
        import('./services/systemMetrics'),
        import('./services/deployments'),
      ])
        .then(([threatsModule, systemMetricsModule, deploymentsModule]) => {
          console.log(
            '[ConfigProvider] Refreshing service connections with new config',
          );
          threatsModule.threatsService.refreshConnectionWithNewConfig();
          systemMetricsModule.systemMetricsService.refreshConnectionWithNewConfig();
          deploymentsModule.deploymentsService.refreshConnectionWithNewConfig();
        })
        .catch((err) => {
          console.warn(
            '[ConfigProvider] Failed to refresh service connections:',
            err,
          );
        });
    }
  }, [config, loading]);

  // Show loading state while config is being fetched
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <ConfigContext.Provider value={{ config, loading, error }}>
      {children}
    </ConfigContext.Provider>
  );
}

/**
 * Primary hook for accessing application configuration
 * Includes safety check to ensure usage within ConfigProvider
 */
export function useAppConfig() {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useAppConfig must be used within a ConfigProvider');
  }
  return context;
}

// Global config reference for non-React code
let globalConfig: ClientConfig = DEFAULT_CONFIG;

// Update global config when context changes
export function setGlobalConfig(config: ClientConfig) {
  globalConfig = config;
}

// Get global config for non-React code (like API clients)
export function getGlobalConfig(): ClientConfig {
  return globalConfig;
}
