import WebSocket from 'ws';
import { TextDecoder } from 'util';

export interface ParsedMessage {
  success: boolean;
  data?: unknown;
  error?: string;
  raw: string;
}

/**
 * Utility class for parsing WebSocket messages
 */
export class MessageParser {
  /**
   * Convert WebSocket data to string
   */
  static dataToString(data: WebSocket.Data): string {
    if (Buffer.isBuffer(data)) {
      return data.toString('utf8');
    } else if (typeof data === 'string') {
      return data;
    } else if (data instanceof ArrayBuffer) {
      return new TextDecoder().decode(data);
    } else {
      return String(data);
    }
  }

  /**
   * Parse JSON message with various fallback strategies
   */
  static parseJson(message: string): ParsedMessage {
    const trimmed = message.trim();

    if (!trimmed) {
      return {
        success: false,
        error: 'Empty message',
        raw: message,
      };
    }

    // Try standard JSON parsing first
    try {
      const data = JSON.parse(trimmed);
      return {
        success: true,
        data,
        raw: message,
      };
    } catch {
      // If standard parsing fails, try fixing common issues
      return this.parseJsonWithFallbacks(trimmed, message);
    }
  }

  /**
   * Parse JSON with fallback strategies for malformed JSON
   */
  private static parseJsonWithFallbacks(
    trimmed: string,
    originalMessage: string,
  ): ParsedMessage {
    // Strategy 1: Fix single quotes
    if (trimmed.includes("'")) {
      try {
        const fixedMessage = this.convertSingleQuotesToDoubleQuotes(trimmed);
        const data = JSON.parse(fixedMessage);
        return {
          success: true,
          data,
          raw: originalMessage,
        };
      } catch {
        // Continue to next strategy
      }
    }

    // Strategy 2: Simple quote replacement
    try {
      const simpleFixed = trimmed.replace(/'/g, '"');
      const data = JSON.parse(simpleFixed);
      return {
        success: true,
        data,
        raw: originalMessage,
      };
    } catch {
      // Continue to next strategy
    }

    // Strategy 3: Try to evaluate as object literal (use with caution)
    if (this.looksLikeObjectLiteral(trimmed)) {
      try {
        // Only allow if it looks safe (no function calls, etc.)
        const data = eval('(' + trimmed + ')');
        return {
          success: true,
          data,
          raw: originalMessage,
        };
      } catch {
        // Final fallback failed
      }
    }

    return {
      success: false,
      error: 'Unable to parse JSON with any strategy',
      raw: originalMessage,
    };
  }

  /**
   * Convert single quotes to double quotes in JSON-like strings
   */
  private static convertSingleQuotesToDoubleQuotes(jsonString: string): string {
    let result = '';
    let inString = false;
    let escapeNext = false;

    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString[i];

      if (escapeNext) {
        result += char;
        escapeNext = false;
        continue;
      }

      if (char === '\\') {
        result += char;
        escapeNext = true;
        continue;
      }

      if (char === "'" && !inString) {
        result += '"';
        inString = true;
      } else if (char === "'" && inString) {
        result += '"';
        inString = false;
      } else {
        result += char;
      }
    }

    return result;
  }

  /**
   * Check if string looks like a safe object literal
   */
  private static looksLikeObjectLiteral(str: string): boolean {
    const trimmed = str.trim();

    // Must start with { and end with }
    if (!trimmed.startsWith('{') || !trimmed.endsWith('}')) {
      return false;
    }

    // Reject if contains potentially dangerous patterns
    const dangerousPatterns = [
      /function\s*\(/,
      /=>\s*\{/,
      /\.\s*constructor/,
      /\.\s*prototype/,
      /require\s*\(/,
      /import\s+/,
      /eval\s*\(/,
      /setTimeout/,
      /setInterval/,
      /process\./,
      /__proto__/,
    ];

    return !dangerousPatterns.some((pattern) => pattern.test(trimmed));
  }

  /**
   * Validate that parsed data has expected structure
   */
  static validateStructure<T>(
    data: unknown,
    validator: (data: unknown) => data is T,
  ): T | null {
    if (validator(data)) {
      return data;
    }
    return null;
  }
}
