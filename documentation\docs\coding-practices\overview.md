# Development Standards

This guide outlines the coding standards, best practices, and development workflows for the Telesoft UI project. Following these guidelines ensures consistency, maintainability, and quality across the entire codebase.

## 🎯 Overview

Our development standards cover:

- **Code Quality**: ESLint, TypeScript, and Prettier configurations
- **Testing**: Jest and React Testing Library practices
- **Project Structure**: Monorepo organization and file naming
- **Development Tools**: VSCode setup and recommended extensions
- **Git Workflow**: Branching strategies and commit conventions

## 📋 Quick Reference

### Technology Stack

```mermaid
graph TB
    subgraph "Languages & Frameworks"
        TS[TypeScript]
        REACT[React 18.3]
        NEXT[Next.js 15.3]
        NODE[Node.js 24+]
    end

    subgraph "Build Tools"
        TURBO[Turbo Monorepo]
        TSUP[tsup Bundler]
        TAILWIND[Tailwind CSS]
        PNPM[pnpm Package Manager]
    end

    subgraph "Quality Tools"
        ESLINT[ESLint 9.x]
        PRETTIER[Prettier]
        JEST[Jest 29.x]
        RTL[React Testing Library]
    end

    TS --> REACT
    REACT --> NEXT
    NODE --> TS

    TURBO --> TSUP
    TSUP --> TAILWIND
    TAILWIND --> PNPM

    ESLINT --> PRETTIER
    PRETTIER --> JEST
    JEST --> RTL
```

## 🛠 Configuration Packages

Our monorepo includes shared configuration packages that enforce consistent standards:

### `@telesoft/eslint-config`

- **Base**: TypeScript + JavaScript linting
- **React Internal**: React + TypeScript + DOM globals
- **Next.js**: Next.js specific optimizations

### `@telesoft/typescript-config`

- **Base**: Core TypeScript configuration
- **React Library**: Component library optimized
- **Next.js**: Next.js application setup
- **Node.js**: Backend service configuration
- **Strict**: Maximum type safety

### `@telesoft/jest-config`

- **Base Configuration**: TypeScript + jsdom environment
- **Test Utilities**: Custom helpers and matchers
- **React Testing**: React Testing Library integration

## 📁 Project Structure Standards

```
package/
├── src/
│   ├── __tests__/           # Test files
│   ├── components/          # React components
│   ├── hooks/              # Custom hooks
│   ├── types/              # Type definitions
│   ├── utils/              # Utility functions
│   └── index.ts            # Main export
├── package.json
├── tsconfig.json           # Extends shared config
├── eslint.config.mjs       # Extends shared config
└── README.md
```

## 🔧 Development Workflow

### 1. Setup

```bash
# Install dependencies
pnpm install

# Run type checking
pnpm type-check

# Run linting
pnpm lint

# Run tests
pnpm test
```

### 2. Development Commands

```bash
# Development with watch mode
pnpm dev

# Build all packages
pnpm build

# Run documentation
pnpm docs
```

### 3. Quality Checks

All packages include these standard scripts:

- `lint`: ESLint checking
- `type-check`: TypeScript validation
- `test`: Jest test runner
- `build`: Production build
- `dev`: Development mode

## 📖 Next Steps

Dive deeper into specific areas:

- **[Coding Standards](./standards.md)** - Detailed coding guidelines
- **[Unit Testing](./testing.md)** - Testing best practices and examples
- **[VSCode Setup](./vscode-setup.md)** - IDE configuration and extensions

## 🎨 Code Style Examples

### TypeScript Interface

```typescript
interface ComponentProps {
  /**
   * The primary content of the component
   */
  children: React.ReactNode;

  /**
   * Visual style variant
   * @default "primary"
   */
  variant?: 'primary' | 'secondary' | 'danger';

  /**
   * Whether the component is disabled
   * @default false
   */
  disabled?: boolean;

  /**
   * Click handler function
   */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
}
```

### React Component

```typescript
import React, { forwardRef } from 'react';
import { cn } from '../utils/classNames';

export interface ButtonProps extends ComponentProps {
  type?: 'button' | 'submit' | 'reset';
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, variant = 'primary', disabled = false, onClick, type = 'button', ...props }, ref) => {
    return (
      <button
        ref={ref}
        type={type}
        disabled={disabled}
        onClick={onClick}
        className={cn(
          'px-4 py-2 rounded font-medium transition-colors',
          {
            'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
            'bg-gray-600 text-white hover:bg-gray-700': variant === 'secondary',
            'bg-red-600 text-white hover:bg-red-700': variant === 'danger',
            'opacity-50 cursor-not-allowed': disabled,
          }
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';
```

## 🚦 Quality Gates

Before merging code, ensure:

- ✅ All tests pass
- ✅ ESLint shows no errors
- ✅ TypeScript compiles without errors
- ✅ Code is properly formatted with Prettier
- ✅ Component has proper TypeScript types
- ✅ New features include tests
- ✅ Documentation is updated

---

Ready to start coding? Check out our [detailed coding standards](./standards.md) and [testing guide](./testing.md)!
