import { useState, useEffect, useRef, useCallback } from 'react';

export interface WebSocketConfig {
  url: string;
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectInterval?: number;
  maxReconnectAttempts?: number;
  fallbackToRest?: boolean;
  pingInterval?: number;
  protocols?: string | string[];
}

export interface WebSocketResult<T> {
  data: T | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastUpdate: string | null;
  connect: () => void;
  disconnect: () => void;
  send: (message: any) => void;
  reconnectAttempts: number;
}

export interface WebSocketMessage<T = any> {
  type: string;
  data?: T;
  timestamp?: string;
  [key: string]: any;
}

export function useWebSocket<T = any>(
  config: WebSocketConfig,
  onMessage?: (message: WebSocketMessage<T>) => void,
  onError?: (error: string) => void,
): WebSocketResult<T> {
  const {
    url,
    autoConnect = true,
    reconnectInterval = 5000,
    maxReconnectInterval = 30000,
    maxReconnectAttempts = 10,
    pingInterval = 30000,
    protocols,
  } = config;

  const [data, setData] = useState<T | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(
    null,
  );
  const pingIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const isConnectingRef = useRef(false);
  const currentReconnectInterval = useRef(reconnectInterval);

  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    isConnectingRef.current = false;
  }, []);

  const startPing = useCallback(() => {
    if (pingInterval && pingInterval > 0) {
      pingIntervalRef.current = setInterval(() => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({ type: 'ping' }));
        }
      }, pingInterval);
    }
  }, [pingInterval]);

  const scheduleReconnect = useCallback(() => {
    if (reconnectAttempts >= maxReconnectAttempts) {
      setError(`Max reconnection attempts (${maxReconnectAttempts}) reached`);
      return;
    }

    reconnectTimeoutRef.current = setTimeout(() => {
      setReconnectAttempts((prev) => prev + 1);
      connect();
    }, currentReconnectInterval.current);

    // Exponential backoff with cap
    currentReconnectInterval.current = Math.min(
      currentReconnectInterval.current * 1.5,
      maxReconnectInterval,
    );
  }, [reconnectAttempts, maxReconnectAttempts, maxReconnectInterval]);

  const connect = useCallback(() => {
    if (
      isConnectingRef.current ||
      wsRef.current?.readyState === WebSocket.OPEN
    ) {
      return;
    }

    try {
      setIsConnecting(true);
      setError(null);
      isConnectingRef.current = true;

      const ws = new WebSocket(url, protocols);
      wsRef.current = ws;

      ws.onopen = () => {
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        setReconnectAttempts(0);
        currentReconnectInterval.current = reconnectInterval;
        isConnectingRef.current = false;
        startPing();
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data) as WebSocketMessage<T>;
          setLastUpdate(new Date().toISOString());

          if (message.type === 'pong') {
            return; // Handle ping/pong internally
          }

          if (onMessage) {
            onMessage(message);
          }

          // Update data if message contains data
          if (message.data !== undefined) {
            setData(message.data);
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      ws.onclose = (event) => {
        setIsConnected(false);
        setIsConnecting(false);
        isConnectingRef.current = false;

        if (pingIntervalRef.current) {
          clearInterval(pingIntervalRef.current);
          pingIntervalRef.current = null;
        }

        if (!event.wasClean && reconnectAttempts < maxReconnectAttempts) {
          const errorMsg = `Connection closed unexpectedly (Code: ${event.code})`;
          setError(errorMsg);
          onError?.(errorMsg);
          scheduleReconnect();
        } else if (reconnectAttempts >= maxReconnectAttempts) {
          const errorMsg = 'Max reconnection attempts reached';
          setError(errorMsg);
          onError?.(errorMsg);
        }
      };

      ws.onerror = () => {
        const errorMsg = 'WebSocket connection error';
        setError(errorMsg);
        onError?.(errorMsg);
        setIsConnecting(false);
        isConnectingRef.current = false;
      };
    } catch (err) {
      const errorMsg =
        err instanceof Error
          ? err.message
          : 'Failed to create WebSocket connection';
      setError(errorMsg);
      onError?.(errorMsg);
      setIsConnecting(false);
      isConnectingRef.current = false;
    }
  }, [
    url,
    protocols,
    onMessage,
    onError,
    reconnectAttempts,
    maxReconnectAttempts,
    reconnectInterval,
    scheduleReconnect,
    startPing,
  ]);

  const disconnect = useCallback(() => {
    cleanup();
    setReconnectAttempts(0);
    currentReconnectInterval.current = reconnectInterval;
  }, [cleanup, reconnectInterval]);

  const send = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }, []);

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return cleanup;
  }, [autoConnect, connect, cleanup]);

  return {
    data,
    isConnected,
    isConnecting,
    error,
    lastUpdate,
    connect,
    disconnect,
    send,
    reconnectAttempts,
  };
}
