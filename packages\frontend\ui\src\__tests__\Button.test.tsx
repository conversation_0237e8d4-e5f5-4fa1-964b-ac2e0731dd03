import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button, ButtonProps } from '../Button';

describe('Button', () => {
  const defaultProps: ButtonProps = {
    children: 'Test Button',
  };

  it('renders with default props', () => {
    render(<Button {...defaultProps} />);
    const button = screen.getByRole('button', { name: 'Test Button' });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center');
  });

  it('renders with custom className', () => {
    render(<Button {...defaultProps} className="custom-class" />);
    const button = screen.getByRole('button', { name: 'Test Button' });
    expect(button).toHaveClass('custom-class');
  });

  describe('variants', () => {
    it('renders primary variant by default', () => {
      render(<Button {...defaultProps} />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-primary-500');
    });

    it('renders secondary variant', () => {
      render(<Button {...defaultProps} variant="secondary" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-background-tertiary');
    });

    it('renders danger variant', () => {
      render(<Button {...defaultProps} variant="danger" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-cyber-danger-500');
    });

    it('renders success variant', () => {
      render(<Button {...defaultProps} variant="success" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-cyber-matrix-500');
    });

    it('renders warning variant', () => {
      render(<Button {...defaultProps} variant="warning" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('bg-cyber-warning-500');
    });

    it('renders ghost variant', () => {
      render(<Button {...defaultProps} variant="ghost" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'text-text-primary',
        'hover:bg-background-hover',
      );
    });

    it('renders outline variant', () => {
      render(<Button {...defaultProps} variant="outline" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('border', 'border-border-primary');
    });
  });

  describe('sizes', () => {
    it('renders medium size by default', () => {
      render(<Button {...defaultProps} />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-10', 'px-4', 'py-2');
    });

    it('renders small size', () => {
      render(<Button {...defaultProps} size="sm" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-8', 'px-3', 'text-sm');
    });

    it('renders large size', () => {
      render(<Button {...defaultProps} size="lg" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('h-12', 'px-8', 'text-lg');
    });
  });

  describe('loading state', () => {
    it('shows loading spinner when loading is true', () => {
      render(<Button {...defaultProps} loading />);
      const spinner = screen.getByRole('button').querySelector('svg');
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass('animate-spin');
    });

    it('disables button when loading', () => {
      render(<Button {...defaultProps} loading />);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('does not show loading spinner when loading is false', () => {
      render(<Button {...defaultProps} loading={false} />);
      const button = screen.getByRole('button');
      const spinner = button.querySelector('svg');
      expect(spinner).not.toBeInTheDocument();
    });
  });

  describe('disabled state', () => {
    it('disables button when disabled prop is true', () => {
      render(<Button {...defaultProps} disabled />);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('applies disabled classes', () => {
      render(<Button {...defaultProps} disabled />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'disabled:pointer-events-none',
        'disabled:opacity-50',
      );
    });
  });

  describe('interactions', () => {
    it('calls onClick when clicked', async () => {
      const user = userEvent.setup();
      const onClick = jest.fn();
      render(<Button {...defaultProps} onClick={onClick} />);

      const button = screen.getByRole('button');
      await user.click(button);

      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('does not call onClick when disabled', async () => {
      const user = userEvent.setup();
      const onClick = jest.fn();
      render(<Button {...defaultProps} onClick={onClick} disabled />);

      const button = screen.getByRole('button');
      await user.click(button);

      expect(onClick).not.toHaveBeenCalled();
    });

    it('does not call onClick when loading', async () => {
      const user = userEvent.setup();
      const onClick = jest.fn();
      render(<Button {...defaultProps} onClick={onClick} loading />);

      const button = screen.getByRole('button');
      await user.click(button);

      expect(onClick).not.toHaveBeenCalled();
    });
  });

  describe('accessibility', () => {
    it('forwards ref to button element', () => {
      const ref = React.createRef<HTMLButtonElement>();
      render(<Button {...defaultProps} ref={ref} />);

      expect(ref.current).toBeInstanceOf(HTMLButtonElement);
      expect(ref.current).toBe(screen.getByRole('button'));
    });

    it('supports custom attributes', () => {
      render(
        <Button
          {...defaultProps}
          data-testid="custom-button"
          aria-label="Custom button"
        />,
      );

      const button = screen.getByTestId('custom-button');
      expect(button).toHaveAttribute('aria-label', 'Custom button');
    });

    it('has focus styles', () => {
      render(<Button {...defaultProps} />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(
        'focus-visible:outline-none',
        'focus-visible:ring-2',
      );
    });
  });

  describe('children', () => {
    it('renders text children', () => {
      render(<Button>Hello World</Button>);
      expect(screen.getByText('Hello World')).toBeInTheDocument();
    });

    it('renders React element children', () => {
      render(
        <Button>
          <span>Icon</span>
          <span>Text</span>
        </Button>,
      );
      expect(screen.getByText('Icon')).toBeInTheDocument();
      expect(screen.getByText('Text')).toBeInTheDocument();
    });
  });
});
