# @telesoft/branding

A reusable branding package containing Telesoft company logos, brand assets, and related components.

## Installation

This package is part of the Telesoft UI monorepo and should be installed via workspace dependencies:

```json
{
  "dependencies": {
    "@telesoft/branding": "workspace:*"
  }
}
```

## Usage

```tsx
import { TelesoftLogo, CompanyLogo, BrandIcon } from '@telesoft/branding';
import { useTheme } from './ThemeProvider';

// Full logo with text - perfect for headers
<TelesoftLogo size="large" variant="full" />

// Theme-aware logo switching (recommended for headers)
const { theme } = useTheme();
const logoVariant = theme === 'dark' ? 'full-logo-white-blue' : 'full-logo';
<TelesoftLogo size="medium" variant={logoVariant} />

// Specific themed logos
<TelesoftLogo size="medium" variant="full-logo-white-blue" /> // Dark theme
<TelesoftLogo size="medium" variant="full-logo" />            // Light theme

// Icon-only version - great for compact spaces
<CompanyLogo size="small" />

// Minimal brand icon - for favicons, etc.
<BrandIcon size={24} />
```

## Asset Handling

This package provides logo components that reference assets in the public directory of consuming applications.

### For Next.js Applications

Next.js applications need to copy the branding assets to their `public` directory. This is handled automatically via build scripts.

### Build Integration

For consuming applications (like `@telesoft/intsoc-frontend`), assets are automatically copied during the build process via scripts that:

1. Copy required assets from the branding package to the app's public directory
2. Run before each build/dev command to ensure assets are always available
3. Only copy the specific assets that are needed

### Available Variants

- `full`: Default Telesoft logo with icon and text
- `icon`: Icon-only version
- `text`: Text-only version
- `full-logo-white-blue`: White and blue logo variant (PNG format) - **recommended for dark theme**
- `full-logo`: Main branded logo variant (PNG format) - **recommended for light theme**

### Theme-Based Logo Usage

For optimal branding, use theme-aware logo switching:

```tsx
const { theme } = useTheme();
const logoVariant = theme === 'dark' ? 'full-logo-white-blue' : 'full-logo';
<TelesoftLogo size="medium" variant={logoVariant} />;
```

### Adding New Assets

When adding new logo variants:

1. Add the asset file to `src/assets/logos/`
2. Update the consuming application's asset copying script to include the new asset
3. Add the variant to the component's type definitions

## Components

### TelesoftLogo

The main company logo component with flexible variants.

**Props:**

- `size`: `'small' | 'medium' | 'large' | 'xlarge' | number` (default: `'medium'`)
- `variant`: `'full' | 'icon' | 'text'` (default: `'full'`)
- `className`: `string` - Additional CSS classes

**Examples:**

```tsx
<TelesoftLogo size="large" variant="full" />     // Full logo
<TelesoftLogo size="medium" variant="icon" />    // Icon only
<TelesoftLogo size="small" variant="text" />     // Text only
<TelesoftLogo size={64} variant="full" />        // Custom size
```

### CompanyLogo

Simplified icon-only logo component.

**Props:**

- `size`: `'small' | 'medium' | 'large' | 'xlarge' | number` (default: `'medium'`)
- `className`: `string` - Additional CSS classes

**Examples:**

```tsx
<CompanyLogo size="small" />    // Predefined size
<CompanyLogo size={32} />       // Custom size
```

### BrandIcon

Minimal text-based brand icon.

**Props:**

- `size`: `number` (default: `24`)
- `className`: `string` - Additional CSS classes
- `color`: `string` (default: `'currentColor'`)

**Examples:**

```tsx
<BrandIcon />                           // Default
<BrandIcon size={48} color="#3B82F6" /> // Custom size and color
```

## Logo Assets

The package includes SVG logo files:

- `telesoft-full.svg` - Complete logo with icon and text
- `telesoft-icon.svg` - Icon/symbol only
- `telesoft-text.svg` - Text-only version

Access asset paths via:

```tsx
import { LOGO_ASSETS } from '@telesoft/branding';

// LOGO_ASSETS.svg.full -> '/logos/telesoft-full.svg'
// LOGO_ASSETS.svg.icon -> '/logos/telesoft-icon.svg'
```

## Size Reference

Predefined sizes:

- `small`: 24px
- `medium`: 32px
- `large`: 48px
- `xlarge`: 64px

## Best Practices

1. **Headers**: Use `TelesoftLogo` with `variant="full"`
2. **Navigation**: Use `CompanyLogo` for compact spaces
3. **Favicons**: Use `BrandIcon` for minimal representations
4. **Responsive**: Consider different sizes for mobile vs desktop
5. **Accessibility**: Components include proper semantic structure

## Customization

All components accept `className` props for additional styling:

```tsx
<TelesoftLogo size="large" className="hover:opacity-80 transition-opacity" />
```
