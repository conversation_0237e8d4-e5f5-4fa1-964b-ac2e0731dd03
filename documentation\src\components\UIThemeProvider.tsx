import React from 'react';

interface UIThemeProviderProps {
  children: React.ReactNode;
}

/**
 * A lightweight theme provider for UI components in documentation
 * This provides the necessary CSS custom properties without importing
 * the full Tailwind CSS that conflicts with Docusaurus
 */
export function UIThemeProvider({ children }: UIThemeProviderProps) {
  return (
    <div className="ui-component-demo">
      {children}
    </div>
  );
}
