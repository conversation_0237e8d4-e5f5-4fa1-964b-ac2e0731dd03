export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  enableReadyCheck?: boolean;
  maxRetriesPerRequest?: number | null;
  lazyConnect?: boolean;
  connectTimeout?: number;
  commandTimeout?: number;
  family?: 4 | 6;
  keepAlive?: number;
  keyPrefix?: string;
}

export interface RedisConnectionOptions {
  maxRetries?: number;
  retryDelay?: number;
  onConnect?: () => void;
  onError?: (error: Error) => void;
  onReconnecting?: () => void;
  onClose?: () => void;
}
