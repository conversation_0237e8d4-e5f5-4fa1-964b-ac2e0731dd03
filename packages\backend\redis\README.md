# @telesoft-ui/redis

A Redis client wrapper built on top of ioredis with dependency injection support, providing a clean and type-safe interface for Redis operations.

## Features

- **Type-safe**: Full TypeScript support with generic types
- **Dependency Injection**: Configuration and connection options passed through constructor
- **Connection Management**: Automatic reconnection and connection pooling
- **Advanced Operations**: Versioning, distributed locks, rate limiting, and caching utilities
- **Error Handling**: Comprehensive error handling with descriptive messages
- **Factory Pattern**: Manage multiple Redis instances with named connections
- **Framework Integration**: Easy integration with Express, NestJS, and other frameworks
- **Testing Support**: Simple mocking for unit tests

## Installation

```bash
npm install @telesoft-ui/redis
# or
pnpm add @telesoft-ui/redis
```

## Quick Start

```typescript
import { RedisClient } from '@telesoft-ui/redis';

// Configuration via dependency injection
const config = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
};

const client = new RedisClient(config);
await client.connect();

// Basic operations
await client.set('key', { data: 'value' });
const result = await client.get('key');
console.log(result); // { data: 'value' }

await client.disconnect();
```

## Basic Usage

### Creating a Redis Client

```typescript
import { RedisClient, RedisConfig } from '@telesoft-ui/redis';

const config: RedisConfig = {
  host: 'localhost',
  port: 6379,
  password: 'your-password', // optional
  db: 0, // optional, defaults to 0
};

const client = new RedisClient(config);

// Connect to Redis
await client.connect();
```

### Basic Operations

```typescript
// Set a value
await client.set('user:123', { name: 'John', age: 30 });

// Get a value
const user = await client.get<{ name: string; age: number }>('user:123');

// Set with TTL (expires in 60 seconds)
await client.set('session:abc', 'active', 60);

// Delete a key
await client.delete('user:123');

// Check if key exists
const exists = await client.exists('user:123');
```

### Using the Factory Pattern

```typescript
import { RedisClientFactory } from '@telesoft-ui/redis';

// Create named instances
const primaryClient = RedisClientFactory.create(
  { host: 'primary.redis.com', port: 6379 },
  {},
  'primary',
);

const cacheClient = RedisClientFactory.create(
  { host: 'cache.redis.com', port: 6379 },
  {},
  'cache',
);

// Get instances later
const primary = RedisClientFactory.getInstance('primary');
const cache = RedisClientFactory.getInstance('cache');
```

### Advanced Operations with RedisService

```typescript
import { RedisService } from '@telesoft-ui/redis';

const service = new RedisService(client);

// Cache function results
const userData = await service.cacheFunction(
  'user:123:profile',
  async () => {
    // Expensive operation
    return await fetchUserFromDatabase(123);
  },
  300, // Cache for 5 minutes
);

// Distributed locking
await service.withLock('critical-section', async () => {
  // This code will only run when the lock is acquired
  await performCriticalOperation();
});

// Rate limiting
const isLimited = await service.isRateLimited(
  'api:user:123',
  100, // 100 requests
  3600, // per hour
);

if (isLimited) {
  throw new Error('Rate limit exceeded');
}
```

### Connection Options

```typescript
import { RedisConnectionOptions } from '@telesoft-ui/redis';

const options: RedisConnectionOptions = {
  maxRetries: 3,
  retryDelay: 1000,
  onConnect: () => console.log('Connected to Redis'),
  onError: (error) => console.error('Redis error:', error),
  onReconnecting: () => console.log('Reconnecting to Redis'),
  onClose: () => console.log('Redis connection closed'),
};

const client = new RedisClient(config, options);
```

### Configuration Options

```typescript
const config: RedisConfig = {
  host: 'localhost',
  port: 6379,
  password: 'your-password',
  db: 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
  lazyConnect: false,
  connectTimeout: 10000,
  commandTimeout: 5000,
  family: 4, // 4 for IPv4, 6 for IPv6
  keepAlive: 30000,
  keyPrefix: 'myapp:', // Prefix for all keys
};
```

## API Reference

### RedisClient

#### Core Methods

- `connect()`: Connect to Redis server
- `disconnect()`: Disconnect from Redis server
- `isConnected()`: Check connection status

#### Data Operations

- `get<T>(key: string): Promise<T | null>`
- `set<T>(key: string, value: T, ttlSeconds?: number): Promise<void>`
- `delete(key: string): Promise<boolean>`
- `exists(key: string): Promise<boolean>`
- `expire(key: string, ttlSeconds: number): Promise<boolean>`
- `ttl(key: string): Promise<number>`

#### Batch Operations

- `getMany<T>(keys: string[]): Promise<(T | null)[]>`
- `setMany<T>(keyValuePairs: Record<string, T>): Promise<void>`
- `deleteMany(keys: string[]): Promise<number>`

#### Numeric Operations

- `increment(key: string, amount?: number): Promise<number>`
- `decrement(key: string, amount?: number): Promise<number>`

#### Utility Methods

- `keys(pattern: string): Promise<string[]>`
- `flushDb(): Promise<void>`
- `getClient(): Redis` - Get underlying ioredis client

### RedisService

#### Caching

- `cacheFunction<T>(key: string, fn: () => Promise<T>, ttlSeconds?: number): Promise<T>`

#### Locking

- `withLock<T>(lockKey: string, fn: () => Promise<T>, lockTimeoutSeconds?: number): Promise<T>`

#### Versioning

- `setVersioned<T>(baseKey: string, value: T, ttlSeconds?: number): Promise<string>`
- `getLatestVersioned<T>(baseKey: string): Promise<T | null>`
- `getVersioned<T>(baseKey: string, version: string): Promise<T | null>`

#### Rate Limiting

- `isRateLimited(key: string, limit: number, windowSeconds: number): Promise<boolean>`

#### Counters

- `boundedIncrement(key: string, min?: number, max?: number, ttlSeconds?: number): Promise<number>`

## Error Handling

All methods throw descriptive errors that include the operation being performed and the underlying Redis error:

```typescript
try {
  await client.get('nonexistent-key');
} catch (error) {
  console.error(error.message); // "Failed to get key "nonexistent-key": ..."
}
```

## Dependency Injection Example

```typescript
// In your DI container setup
import { RedisClient, RedisConfig } from '@telesoft-ui/redis';

class DatabaseService {
  constructor(
    private redisClient: RedisClient,
    private config: RedisConfig,
  ) {}

  async cacheUserData(userId: string, data: any): Promise<void> {
    await this.redisClient.set(`user:${userId}`, data, 3600);
  }
}

// Container registration
const redisConfig: RedisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
};

const redisClient = new RedisClient(redisConfig);
const databaseService = new DatabaseService(redisClient, redisConfig);
```

For more detailed dependency injection examples with Express, NestJS, testing, and multi-environment setups, see [DEPENDENCY_INJECTION.md](./DEPENDENCY_INJECTION.md).

## License

MIT
