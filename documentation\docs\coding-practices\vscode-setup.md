# VSCode Setup Guide

This guide helps you configure Visual Studio Code for optimal development experience with the Telesoft UI project. Follow these steps to set up your IDE with the right extensions, settings, and workflows.

## 🚀 Essential Extensions

### Core Development Extensions

#### 1. **TypeScript & JavaScript**

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint"
  ]
}
```

- **TypeScript Importer** (`pmneo.tsimporter`) - Auto import TypeScript modules
- **TypeScript Hero** (`rbbit.typescript-hero`) - Additional TypeScript tooling
- **Auto Rename Tag** (`formulahendry.auto-rename-tag`) - Automatically rename paired HTML/JSX tags

#### 2. **Code Quality & Formatting**

- **ESLint** (`dbaeumer.vscode-eslint`) - JavaScript/TypeScript linting
- **Prettier** (`esbenp.prettier-vscode`) - Code formatting
- **Code Spell Checker** (`streetsidesoftware.code-spell-checker`) - Spell checking
- **Error Lens** (`usernamehw.errorlens`) - Inline error highlighting

#### 3. **React Development**

- **ES7+ React/Redux/React-Native snippets** (`dsznajder.es7-react-js-snippets`)
- **Bracket Pair Colorizer 2** (`coenraads.bracket-pair-colorizer-2`)
- **Auto Close Tag** (`formulahendry.auto-close-tag`)
- **React PropTypes Intellisense** (`OfHumanBondage.react-proptypes-intellisense`)

#### 4. **Styling & CSS**

- **Tailwind CSS IntelliSense** (`bradlc.vscode-tailwindcss`)
- **CSS Peek** (`pranaygp.vscode-css-peek`)
- **PostCSS Language Support** (`csstools.postcss`)

#### 5. **Git & Version Control**

- **GitLens** (`eamodio.gitlens`) - Enhanced Git capabilities
- **Git History** (`donjayamanne.githistory`) - View git log and file history
- **GitHub Pull Requests and Issues** (`github.vscode-pull-request-github`)

#### 6. **Testing**

- **Jest** (`orta.vscode-jest`) - Jest test runner integration
- **Jest Runner** (`firsttris.vscode-jest-runner`) - Run individual tests
- **Test Explorer UI** (`hbenl.vscode-test-explorer`) - Test management interface

#### 7. **Productivity**

- **Thunder Client** (`rangav.vscode-thunder-client`) - API testing
- **Path Intellisense** (`christian-kohler.path-intellisense`) - File path autocompletion
- **Import Cost** (`wix.vscode-import-cost`) - Display import size
- **Todo Tree** (`gruntfuggly.todo-tree`) - TODO/FIXME highlighting
- **Bookmarks** (`alefragnani.bookmarks`) - Code bookmarking

#### 8. **Documentation**

- **Markdown All in One** (`yzhang.markdown-all-in-one`) - Markdown support
- **markdownlint** (`davidanson.vscode-markdownlint`) - Markdown linting
- **Mermaid Markdown Syntax Highlighting** (`bpruitt-goddard.mermaid-markdown-syntax-highlighting`)

### Installation Script

Create this script to install all recommended extensions:

```bash
#!/bin/bash
# install-vscode-extensions.sh

extensions=(
  "ms-vscode.vscode-typescript-next"
  "bradlc.vscode-tailwindcss"
  "esbenp.prettier-vscode"
  "dbaeumer.vscode-eslint"
  "pmneo.tsimporter"
  "rbbit.typescript-hero"
  "formulahendry.auto-rename-tag"
  "streetsidesoftware.code-spell-checker"
  "usernamehw.errorlens"
  "dsznajder.es7-react-js-snippets"
  "coenraads.bracket-pair-colorizer-2"
  "formulahendry.auto-close-tag"
  "pranaygp.vscode-css-peek"
  "csstools.postcss"
  "eamodio.gitlens"
  "donjayamanne.githistory"
  "github.vscode-pull-request-github"
  "orta.vscode-jest"
  "firsttris.vscode-jest-runner"
  "hbenl.vscode-test-explorer"
  "rangav.vscode-thunder-client"
  "christian-kohler.path-intellisense"
  "wix.vscode-import-cost"
  "gruntfuggly.todo-tree"
  "alefragnani.bookmarks"
  "yzhang.markdown-all-in-one"
  "davidanson.vscode-markdownlint"
  "bpruitt-goddard.mermaid-markdown-syntax-highlighting"
)

for extension in "\${extensions[@]}"; do
  code --install-extension "$extension"
done

echo "All extensions installed!"
```

## ⚙️ Workspace Settings

### `.vscode/settings.json`

Create this file in your project root:

```json
{
  // Editor Configuration
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.wordWrap": "on",
  "editor.rulers": [80, 120],

  // File Configuration
  "files.eol": "\\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.next": true,
    "**/coverage": true,
    "**/.turbo": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/.next/**": true,
    "**/coverage/**": true
  },

  // TypeScript Configuration
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.includePackageJsonAutoImports": "on",

  // ESLint Configuration
  "eslint.workingDirectories": [
    "apps/frontend/intsoc",
    "apps/backend/intsoc",
    "packages/frontend/ui",
    "packages/frontend/branding",
    "packages/frontend/d3",
    "packages/common/types",
    "packages/config/eslint-config",
    "packages/config/typescript-config",
    "packages/config/jest-config"
  ],
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],

  // Jest Configuration
  "jest.jestCommandLine": "pnpm test",
  "jest.autoRun": {
    "watch": false,
    "onStartup": ["all-tests"]
  },

  // Prettier Configuration
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,

  // Tailwind CSS Configuration
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cn\\\\(([^)]*)\\\\)", "'([^']*)'"],
    ["clsx\\\\(([^)]*)\\\\)", "'([^']*)'"]
  ],

  // Emmet Configuration
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },

  // Search Configuration
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.next": true,
    "**/coverage": true,
    "pnpm-lock.yaml": true
  },

  // Explorer Configuration
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "\${capture}.js, \${capture}.*.ts, \${capture}_*.ts",
    "*.tsx": "\${capture}.*.tsx, \${capture}_*.tsx, \${capture}.module.css",
    "package.json": "package-lock.json, pnpm-lock.yaml, yarn.lock, .npmrc",
    "tsconfig.json": "tsconfig.*.json",
    ".eslintrc.*": ".eslintignore, .eslintcache",
    "tailwind.config.*": "postcss.config.*, autoprefixer.config.*"
  }
}
```

### `.vscode/extensions.json`

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "pmneo.tsimporter",
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "dsznajder.es7-react-js-snippets",
    "eamodio.gitlens",
    "orta.vscode-jest",
    "christian-kohler.path-intellisense",
    "wix.vscode-import-cost",
    "yzhang.markdown-all-in-one"
  ],
  "unwantedRecommendations": ["ms-vscode.vscode-json"]
}
```

### `.vscode/tasks.json`

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Build All",
      "type": "shell",
      "command": "pnpm",
      "args": ["build"],
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": ["$tsc"]
    },
    {
      "label": "Type Check All",
      "type": "shell",
      "command": "pnpm",
      "args": ["type-check"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": ["$tsc"]
    },
    {
      "label": "Lint All",
      "type": "shell",
      "command": "pnpm",
      "args": ["lint"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": ["$eslint-stylish"]
    },
    {
      "label": "Test All",
      "type": "shell",
      "command": "pnpm",
      "args": ["test"],
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "Start Documentation",
      "type": "shell",
      "command": "pnpm",
      "args": ["docs"],
      "group": "build",
      "isBackground": true,
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "problemMatcher": []
    }
  ]
}
```

### `.vscode/launch.json`

Debug configuration for testing and development:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Jest Tests",
      "type": "node",
      "request": "launch",
      "program": "\${workspaceFolder}/node_modules/.bin/jest",
      "args": ["--runInBand", "--no-cache", "--watchAll=false"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "env": {
        "NODE_ENV": "test"
      }
    },
    {
      "name": "Debug Current Test File",
      "type": "node",
      "request": "launch",
      "program": "\${workspaceFolder}/node_modules/.bin/jest",
      "args": [
        "\${relativeFile}",
        "--runInBand",
        "--no-cache",
        "--watchAll=false"
      ],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "env": {
        "NODE_ENV": "test"
      }
    },
    {
      "name": "Debug Next.js App",
      "type": "node",
      "request": "launch",
      "program": "\${workspaceFolder}/apps/frontend/intsoc/node_modules/.bin/next",
      "args": ["dev"],
      "cwd": "\${workspaceFolder}/apps/frontend/intsoc",
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "name": "Debug Backend API",
      "type": "node",
      "request": "launch",
      "program": "\${workspaceFolder}/apps/backend/intsoc/src/bin/http.ts",
      "cwd": "\${workspaceFolder}/apps/backend/intsoc",
      "runtimeArgs": ["--loader", "tsx/esm"],
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development"
      }
    }
  ]
}
```

## 🎨 Code Snippets

### Custom TypeScript React Snippets

Create `.vscode/typescript-react.code-snippets`:

```json
{
  "React Functional Component": {
    "prefix": "tfc",
    "body": [
      "import React from 'react';",
      "",
      "export interface \${1:ComponentName}Props {",
      "  $2",
      "}",
      "",
      "export const \${1:ComponentName}: React.FC<\${1:ComponentName}Props> = ({",
      "  $3",
      "}) => {",
      "  return (",
      "    <div>",
      "      $0",
      "    </div>",
      "  );",
      "};"
    ],
    "description": "Create a TypeScript React functional component"
  },

  "React Component with forwardRef": {
    "prefix": "tfr",
    "body": [
      "import React, { forwardRef } from 'react';",
      "",
      "export interface \${1:ComponentName}Props {",
      "  $2",
      "}",
      "",
      "export const \${1:ComponentName} = forwardRef<HTML\${3:Div}Element, \${1:ComponentName}Props>(",
      "  ({ $4 }, ref) => {",
      "    return (",
      "      <\${5:div} ref={ref}>",
      "        $0",
      "      </\${5:div}>",
      "    );",
      "  }",
      ");",
      "",
      "\${1:ComponentName}.displayName = '\${1:ComponentName}';"
    ],
    "description": "Create a TypeScript React component with forwardRef"
  },

  "Custom Hook": {
    "prefix": "thook",
    "body": [
      "import { useState, useEffect } from 'react';",
      "",
      "export interface Use\${1:HookName}Options {",
      "  $2",
      "}",
      "",
      "export interface Use\${1:HookName}Return {",
      "  $3",
      "}",
      "",
      "export function use\${1:HookName}(options: Use\${1:HookName}Options = {}): Use\${1:HookName}Return {",
      "  const { $4 } = options;",
      "  ",
      "  $0",
      "  ",
      "  return {",
      "    $5",
      "  };",
      "}"
    ],
    "description": "Create a custom TypeScript React hook"
  },

  "Jest Test Suite": {
    "prefix": "ttest",
    "body": [
      "import { render, screen } from '@testing-library/react';",
      "import userEvent from '@testing-library/user-event';",
      "import { \${1:ComponentName} } from './\${1:ComponentName}';",
      "",
      "describe('\${1:ComponentName}', () => {",
      "  it('renders correctly', () => {",
      "    render(<\${1:ComponentName} $2 />);",
      "    ",
      "    $0",
      "  });",
      "  ",
      "  it('handles user interactions', async () => {",
      "    const user = userEvent.setup();",
      "    const handleClick = jest.fn();",
      "    ",
      "    render(<\${1:ComponentName} onClick={handleClick} />);",
      "    ",
      "    // Add interaction tests here",
      "  });",
      "});"
    ],
    "description": "Create a Jest test suite for a React component"
  }
}
```

## 🔧 Workspace Configuration

### Multi-root Workspace

For better organization, you can create a multi-root workspace:

```json
{
  "folders": [
    {
      "name": "📱 Frontend Apps",
      "path": "./apps/frontend"
    },
    {
      "name": "🔧 Backend Apps",
      "path": "./apps/backend"
    },
    {
      "name": "📦 Frontend Packages",
      "path": "./packages/frontend"
    },
    {
      "name": "⚙️ Config Packages",
      "path": "./packages/config"
    },
    {
      "name": "🔗 Common Packages",
      "path": "./packages/common"
    },
    {
      "name": "📚 Documentation",
      "path": "./documentation"
    }
  ],
  "settings": {
    // Global workspace settings
  }
}
```

## 🛠 Development Workflow

### Daily Development Commands

#### 1. **Starting Development**

```bash
# Open workspace
code telesoft-ui.code-workspace

# Install dependencies (if needed)
pnpm install

# Start development servers
pnpm dev

# Start documentation
pnpm docs
```

#### 2. **Code Quality Checks**

- **Ctrl+Shift+P** → "Tasks: Run Task" → "Type Check All"
- **Ctrl+Shift+P** → "Tasks: Run Task" → "Lint All"
- **Ctrl+Shift+P** → "Tasks: Run Task" → "Test All"

#### 3. **Useful Keyboard Shortcuts**

- **Ctrl+Shift+E** - Explorer panel
- **Ctrl+Shift+F** - Search across files
- **Ctrl+Shift+G** - Source control panel
- **Ctrl+Shift+X** - Extensions panel
- **Ctrl+`** - Toggle terminal
- **Ctrl+Shift+`** - New terminal
- **F5** - Start debugging
- **Ctrl+F5** - Start without debugging

### Git Integration

#### GitLens Features

- **Blame annotations** - See who changed each line
- **Code lens** - Recent changes and authorship
- **File history** - Visual git history for files
- **Repository view** - Branch and commit management

#### Recommended Git Workflow

1. **Create feature branch**: `git checkout -b feature/new-component`
2. **Make changes** with auto-formatting and linting
3. **Run tests**: `pnpm test`
4. **Commit with conventional format**: `feat: add new Button component`
5. **Push and create PR** via GitHub integration

## 🎯 Productivity Tips

### 1. **Efficient File Navigation**

- **Ctrl+P** - Quick open files
- **Ctrl+Shift+P** - Command palette
- **Ctrl+R** - Switch between recent workspaces
- **Ctrl+G** - Go to line number

### 2. **Code Editing**

- **Alt+Up/Down** - Move line up/down
- **Shift+Alt+Up/Down** - Copy line up/down
- **Ctrl+D** - Select next occurrence
- **Ctrl+Shift+L** - Select all occurrences
- **Ctrl+/** - Toggle line comment
- **Shift+Alt+A** - Toggle block comment

### 3. **Multi-cursor Editing**

- **Alt+Click** - Add cursor
- **Ctrl+Alt+Up/Down** - Add cursor above/below
- **Ctrl+Shift+L** - Select all matching selections

### 4. **IntelliSense Features**

- **Ctrl+Space** - Trigger IntelliSense
- **Ctrl+Shift+Space** - Parameter hints
- **F12** - Go to definition
- **Alt+F12** - Peek definition
- **Shift+F12** - Find all references

## 🧪 Testing Integration

### Jest Extension Setup

1. Install Jest extension (`orta.vscode-jest`)
2. Configure workspace settings for Jest
3. Tests will run automatically and show results inline
4. Use test explorer for visual test management

### Debugging Tests

1. Set breakpoints in test files
2. Use "Debug Current Test File" launch configuration
3. Step through test execution
4. Inspect variables and state

## 📋 VSCode Setup Checklist

### ✅ Installation

- [ ] All recommended extensions installed
- [ ] Workspace settings configured
- [ ] Custom snippets added
- [ ] Debug configurations set up

### ✅ Configuration

- [ ] ESLint working correctly
- [ ] Prettier formatting on save
- [ ] TypeScript IntelliSense active
- [ ] Tailwind CSS autocomplete working
- [ ] Jest tests running and reporting

### ✅ Productivity

- [ ] File nesting patterns configured
- [ ] Custom tasks set up
- [ ] Git integration working
- [ ] Multi-root workspace (optional)
- [ ] Custom keyboard shortcuts (optional)

### ✅ Quality Assurance

- [ ] Auto-fix on save enabled
- [ ] Import organization working
- [ ] Spell checking active
- [ ] Error lens showing inline errors
- [ ] Code coverage reports accessible

---

Your VSCode environment is now optimized for Telesoft UI development! The configuration provides a seamless development experience with automatic formatting, linting, testing, and intelligent code completion.

🎉 **Happy Coding!** Your IDE is now ready for productive development work on the Telesoft UI project.
