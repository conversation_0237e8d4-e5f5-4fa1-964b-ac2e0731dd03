export interface LogoSize {
  small: string;
  medium: string;
  large: string;
  xlarge: string;
}

export interface LogoProps {
  size?: keyof LogoSize | number;
  className?: string;
  variant?: 'full' | 'icon' | 'text' | 'full-logo-white-blue' | 'full-logo';
}

const sizeMap: LogoSize = {
  small: '24px',
  medium: '32px',
  large: '48px',
  xlarge: '64px',
};

/**
 * Telesoft company logo component with icon and text
 */
export function TelesoftLogo({
  size = 'medium',
  className = '',
  variant = 'full',
}: LogoProps) {
  const logoSize = typeof size === 'number' ? `${size}px` : sizeMap[size];

  if (variant === 'text') {
    return (
      <span
        className={`font-bold text-primary-500 ${className}`}
        style={{ fontSize: logoSize }}
      >
        TELESOFT
      </span>
    );
  }

  if (variant === 'icon') {
    return (
      <div
        className={`bg-primary-500 rounded flex items-center justify-center ${className}`}
        style={{
          width: logoSize,
          height: logoSize,
          minWidth: logoSize,
          minHeight: logoSize,
        }}
      >
        <span
          className="text-white font-bold"
          style={{ fontSize: `calc(${logoSize} * 0.6)` }}
        >
          TS
        </span>
      </div>
    );
  }

  if (variant === 'full-logo-white-blue') {
    return (
      <img
        src="/logos/full-logo-white-blue.png"
        alt="Telesoft Logo"
        className={className}
        style={{
          height: logoSize,
          width: 'auto',
        }}
      />
    );
  }

  if (variant === 'full-logo') {
    return (
      <img
        src="/logos/full-logo.png"
        alt="Telesoft Logo"
        className={className}
        style={{
          height: logoSize,
          width: 'auto',
        }}
      />
    );
  }

  // Full variant - icon + text
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div
        className="bg-primary-500 rounded flex items-center justify-center"
        style={{
          width: logoSize,
          height: logoSize,
          minWidth: logoSize,
          minHeight: logoSize,
        }}
      >
        <span
          className="text-white font-bold"
          style={{ fontSize: `calc(${logoSize} * 0.6)` }}
        >
          TS
        </span>
      </div>
      <span
        className="font-bold text-text-primary"
        style={{ fontSize: `calc(${logoSize} * 0.75)` }}
      >
        TELESOFT
      </span>
    </div>
  );
}
