import { RedisClient, RedisClientFactory, RedisService } from '../index';

describe('RedisClient', () => {
  let client: RedisClient;

  beforeEach(() => {
    // Mock Redis config for testing
    const mockConfig = {
      host: 'localhost',
      port: 6379,
    };

    client = new RedisClient(mockConfig);
  });

  afterEach(async () => {
    if (client.isConnected()) {
      await client.disconnect();
    }
  });

  describe('Configuration', () => {
    it('should create client with basic config', () => {
      expect(client).toBeDefined();
      expect(client.isConnected()).toBe(false);
    });

    it('should create client with full config', () => {
      const fullConfig = {
        host: 'redis.example.com',
        port: 6380,
        password: 'secret',
        db: 1,
        keyPrefix: 'test:',
        connectTimeout: 5000,
      };

      const clientWithFullConfig = new RedisClient(fullConfig);
      expect(clientWithFullConfig).toBeDefined();
    });
  });

  describe('Factory Pattern', () => {
    it('should create and retrieve named instances', () => {
      const config = { host: 'localhost', port: 6379 };

      const client1 = RedisClientFactory.create(config, {}, 'instance1');
      const client2 = RedisClientFactory.create(config, {}, 'instance2');

      expect(RedisClientFactory.getInstance('instance1')).toBe(client1);
      expect(RedisClientFactory.getInstance('instance2')).toBe(client2);
      expect(RedisClientFactory.getInstanceNames()).toContain('instance1');
      expect(RedisClientFactory.getInstanceNames()).toContain('instance2');
    });

    it('should remove instances', async () => {
      const config = { host: 'localhost', port: 6379 };
      RedisClientFactory.create(config, {}, 'temp');

      expect(RedisClientFactory.getInstance('temp')).toBeDefined();

      await RedisClientFactory.removeInstance('temp');
      expect(RedisClientFactory.getInstance('temp')).toBeUndefined();
    });
  });

  describe('RedisService', () => {
    let service: RedisService;

    beforeEach(() => {
      service = new RedisService(client);
    });

    it('should create service with client', () => {
      expect(service).toBeDefined();
    });

    // Note: Actual Redis operations would require a real Redis instance
    // These are structural tests to ensure the API is properly defined
  });
});

// Integration test example (requires actual Redis instance)
describe('Redis Integration', () => {
  const shouldRunIntegrationTests =
    process.env.REDIS_INTEGRATION_TESTS === 'true';

  if (!shouldRunIntegrationTests) {
    it.skip('Integration tests skipped - set REDIS_INTEGRATION_TESTS=true to run', () => {});
    return;
  }

  let client: RedisClient;

  beforeAll(async () => {
    client = new RedisClient({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
    });
    await client.connect();
  });

  afterAll(async () => {
    if (client.isConnected()) {
      await client.flushDb(); // Clean up test data
      await client.disconnect();
    }
  });

  it('should perform basic operations', async () => {
    const testKey = 'test:basic';
    const testValue = { message: 'Hello Redis!' };

    // Set and get
    await client.set(testKey, testValue);
    const retrieved = await client.get(testKey);
    expect(retrieved).toEqual(testValue);

    // Delete
    const deleted = await client.delete(testKey);
    expect(deleted).toBe(true);

    // Verify deletion
    const afterDelete = await client.get(testKey);
    expect(afterDelete).toBeNull();
  });

  it('should handle TTL', async () => {
    const testKey = 'test:ttl';
    const testValue = 'expires soon';

    await client.set(testKey, testValue, 1); // 1 second TTL

    const ttl = await client.ttl(testKey);
    expect(ttl).toBeGreaterThan(0);
    expect(ttl).toBeLessThanOrEqual(1);

    // Wait for expiration
    await new Promise((resolve) => setTimeout(resolve, 1100));

    const expired = await client.get(testKey);
    expect(expired).toBeNull();
  });
});
