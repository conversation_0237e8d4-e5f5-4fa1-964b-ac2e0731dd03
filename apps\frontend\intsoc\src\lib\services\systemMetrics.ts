import { getGlobalConfig } from '../contexts/config-provider';

export interface SystemMetrics {
  timestamp: string;
  cpu: {
    system: number; // Total system CPU usage percentage
    process: number; // Node.js process CPU usage percentage
    temperature?: number;
  };
  memory: {
    used: number;
    total: number;
    usagePercent: number;
    available: number;
  };
  system: {
    uptime: number;
    platform: string;
    arch: string;
    nodeVersion: string;
  };
  network?: {
    bytesReceived: number;
    bytesSent: number;
  };
}

export interface SystemMetricsWebSocketMessage {
  type: 'system-metrics' | 'ping' | 'pong' | 'subscribe' | 'unsubscribe';
  data?: SystemMetrics;
  timestamp: string;
}

/**
 * Service for managing system metrics data
 */
export class SystemMetricsService {
  private wsConnection: WebSocket | null = null;
  private wsUrl: string | null = null;
  private reconnectTimeoutId: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private currentReconnectInterval = 5000; // Start with 5 seconds
  private maxReconnectInterval = 30000; // Cap at 30 seconds
  private shouldReconnect = false;

  // Callback storage for reconnection
  private onMessageCallback:
    | ((message: SystemMetricsWebSocketMessage) => void)
    | null = null;
  private onErrorCallback: ((error: Event) => void) | null = null;
  private onCloseCallback: ((event: CloseEvent) => void) | null = null;
  private onOpenCallback: (() => void) | null = null;

  /**
   * Get WebSocket URL for system metrics
   * Note: WebSocket connections are ALWAYS direct to backend (no Next.js proxy)
   * Always gets fresh URL from current config to handle runtime config changes
   */
  private getWebSocketUrl(): string {
    const config = getGlobalConfig().api;
    // WebSocket connections bypass Next.js and connect directly to backend
    const wsUrl = config.wsUrl.replace(/^http/, 'ws');
    const fullWsUrl = `${wsUrl}/metrics`;

    return fullWsUrl;
  }

  /**
   * Connect to WebSocket for real-time metrics updates with automatic reconnection
   */
  connectWebSocket(
    onMessage: (message: SystemMetricsWebSocketMessage) => void,
    onError?: (error: Event) => void,
    onClose?: (event: CloseEvent) => void,
    onOpen?: () => void,
  ): WebSocket {
    // Store callbacks for reconnection
    this.onMessageCallback = onMessage;
    this.onErrorCallback = onError || null;
    this.onCloseCallback = onClose || null;
    this.onOpenCallback = onOpen || null;
    this.shouldReconnect = true;

    return this.establishConnection();
  }

  /**
   * Internal method to establish WebSocket connection
   */
  private establishConnection(): WebSocket {
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      return this.wsConnection;
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    const ws = new WebSocket(this.getWebSocketUrl());
    this.wsConnection = ws;

    ws.onopen = () => {
      console.log('System Metrics WebSocket connected');
      this.reconnectAttempts = 0;
      this.currentReconnectInterval = 5000; // Reset interval on successful connection

      // Subscribe to metrics updates
      const subscribeMessage: SystemMetricsWebSocketMessage = {
        type: 'subscribe',
        timestamp: new Date().toISOString(),
      };
      ws.send(JSON.stringify(subscribeMessage));
      this.onOpenCallback?.();
    };

    ws.onmessage = (event) => {
      try {
        const message: SystemMetricsWebSocketMessage = JSON.parse(event.data);
        this.onMessageCallback?.(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('System Metrics WebSocket error:', error);
      this.onErrorCallback?.(error);
    };

    ws.onclose = (event) => {
      console.log('System Metrics WebSocket closed:', event.code, event.reason);
      this.wsConnection = null;
      this.onCloseCallback?.(event);

      // Attempt reconnection unless it was a clean close
      if (event.code !== 1000 && this.shouldReconnect) {
        this.attemptReconnection();
      } else if (event.code === 1000) {
        console.log('System Metrics WebSocket closed cleanly');
      }
    };

    return ws;
  }

  /**
   * Attempt to reconnect with exponential backoff
   */
  private attemptReconnection(): void {
    if (!this.shouldReconnect) return;

    this.reconnectAttempts++;

    // Calculate exponential backoff with jitter
    const exponentialDelay = Math.min(
      this.currentReconnectInterval *
        Math.pow(2, Math.min(this.reconnectAttempts - 1, 6)),
      this.maxReconnectInterval,
    );
    const jitter = Math.random() * 1000; // Add up to 1 second of jitter
    const delay = exponentialDelay + jitter;

    this.currentReconnectInterval = delay;

    console.log(
      `System Metrics WebSocket disconnected. Attempting to reconnect in ${Math.round(delay)}ms (attempt ${this.reconnectAttempts})...`,
    );

    this.reconnectTimeoutId = setTimeout(() => {
      if (this.shouldReconnect) {
        this.establishConnection();
      }
    }, delay);
  }

  /**
   * Disconnect from WebSocket and stop reconnection attempts
   */
  disconnectWebSocket(): void {
    this.shouldReconnect = false;

    // Clear reconnection timeout
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    // Close connection
    if (this.wsConnection) {
      this.wsConnection.close();
      this.wsConnection = null;
    }

    // Clear callbacks
    this.onMessageCallback = null;
    this.onErrorCallback = null;
    this.onCloseCallback = null;
    this.onOpenCallback = null;
  }

  /**
   * Send a message through the WebSocket connection
   */
  sendWebSocketMessage(message: SystemMetricsWebSocketMessage): boolean {
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      this.wsConnection.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  /**
   * Check if WebSocket is connected
   */
  isWebSocketConnected(): boolean {
    return this.wsConnection?.readyState === WebSocket.OPEN;
  }

  /**
   * Refresh WebSocket connection with updated configuration
   * This should be called when configuration changes
   */
  refreshConnectionWithNewConfig(): void {
    console.log('[SystemMetricsService] Refreshing connection with new config');

    // If there's an active connection, disconnect first
    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
      console.log(
        '[SystemMetricsService] Disconnecting existing connection for config refresh',
      );
      this.disconnectWebSocket();

      // Reconnect with new config after a short delay
      setTimeout(() => {
        if (this.onMessageCallback) {
          console.log('[SystemMetricsService] Reconnecting with new config');
          this.connectWebSocket(
            this.onMessageCallback,
            this.onErrorCallback || undefined,
            this.onCloseCallback || undefined,
            this.onOpenCallback || undefined,
          );
        }
      }, 100);
    }
  }
}

// Singleton instance
export const systemMetricsService = new SystemMetricsService();
