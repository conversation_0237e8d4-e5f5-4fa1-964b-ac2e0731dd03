# Telesoft UI Monorepo

This is a monorepo built with [Turborepo](https://turbo.build/repo) and [pnpm](https://pnpm.io/).

## What's inside?

This monorepo includes the following packages/apps:

### Apps

- `@telesoft/intsoc-frontend`: a [Next.js](https://nextjs.org/) application for the intsoc platform
- `@telesoft/intsoc-backend`: a [Node.js](https://nodejs.org/) API server with [Express.js](https://expressjs.com/)

### Frontend Packages

- `@telesoft/ui`: a React component library with reusable UI components
- `@telesoft/branding`: brand assets and logo components
- `@telesoft/d3`: data visualization components built with D3.js
- `@telesoft/color-palette`: shared color system and Tailwind CSS configurations

### Common Packages

- `@telesoft/types`: shared TypeScript type definitions

### Configuration Packages

- `@telesoft/eslint-config`: ESLint configurations for different environments
- `@telesoft/typescript-config`: TypeScript configurations for various project types
- `@telesoft/jest-config`: Jest testing configurations

## Getting Started

### Prerequisites

- Node.js 24+
- pnpm 10+

### Installation

```bash
pnpm install
```

### Development

To develop all apps and packages:

```bash
pnpm dev
```

To develop a specific app:

```bash
pnpm --filter @telesoft/intsoc-frontend dev
pnpm --filter @telesoft/intsoc-backend dev
```

To watch and develop packages:

```bash
pnpm watch:packages
pnpm watch:apps
```

### Build

To build all apps and packages:

```bash
pnpm build
```

To build a specific app:

```bash
pnpm --filter @telesoft/intsoc-frontend build
pnpm --filter @telesoft/intsoc-backend build
```

### Lint

```bash
pnpm lint
```

### Test

```bash
pnpm test
```

### Type Check

```bash
pnpm type-check
```

### Format

```bash
pnpm format
```

### Clean

```bash
pnpm clean
```

## Project Structure

```
telesoft-ui/
├── apps/
│   ├── frontend/
│   │   └── intsoc/          # Next.js frontend application
│   └── backend/
│       └── intsoc/          # Express.js API server
├── packages/
│   ├── frontend/
│   │   ├── ui/              # React component library
│   │   ├── branding/        # Brand assets and logos
│   │   ├── d3/              # D3.js visualization components
│   │   └── color-palette/   # Color system and Tailwind configs
│   ├── common/
│   │   └── types/           # Shared TypeScript types
│   └── config/
│       ├── eslint-config/   # ESLint configurations
│       ├── typescript-config/ # TypeScript configurations
│       └── jest-config/     # Jest testing configurations
└── turbo.json              # Turborepo configuration
```

## Useful Links

Learn more about the power of Turborepo:

- [Tasks](https://turbo.build/repo/docs/core-concepts/monorepos/running-tasks)
- [Caching](https://turbo.build/repo/docs/core-concepts/caching)
- [Remote Caching](https://turbo.build/repo/docs/core-concepts/remote-caching)
- [Filtering](https://turbo.build/repo/docs/core-concepts/monorepos/filtering)
- [Configuration Options](https://turbo.build/repo/docs/reference/configuration)
- [CLI Usage](https://turbo.build/repo/docs/reference/command-line-reference)
