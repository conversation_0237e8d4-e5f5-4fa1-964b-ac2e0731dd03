{"name": "@telesoft/typescript-config", "version": "0.0.0", "private": true, "main": "./base.json", "exports": {".": "./base.json", "./base": "./base.json", "./react-library": "./react-library.json", "./nextjs": "./nextjs.json", "./node": "./node.json", "./strict": "./strict.json"}, "scripts": {"build": "pnpm run validate", "validate": "pnpm run validate:configs && pnpm run validate:syntax", "validate:configs": "node scripts/validate-configs.js", "validate:syntax": "pnpm run validate:json", "validate:json": "node -e \"const fs = require('fs'); const configs = ['base.json', 'react-library.json', 'nextjs.json', 'node.json', 'strict.json']; configs.forEach(file => { try { JSON.parse(fs.readFileSync(file, 'utf8')); console.log('✓', file, 'is valid JSON'); } catch (e) { console.error('✗', file, 'has invalid JSON:', e.message); process.exit(1); } }); console.log('All TypeScript configs have valid JSON syntax');\"", "clean": "echo 'No build artifacts to clean'", "dev": "echo 'Nothing to build'", "lint": "echo 'TypeScript configs validated'", "format": "prettier --write . --ignore-path ../../../.prettierignore"}, "files": ["base.json", "nextjs.json", "react-library.json", "react-library-jest.json", "node.json", "strict.json", "README.md", "scripts/"], "dependencies": {"@tsconfig/next": "^2.0.3", "@tsconfig/node20": "^20.1.4", "@tsconfig/recommended": "^1.0.8", "@tsconfig/strictest": "^2.0.5", "typescript": "^5"}, "devDependencies": {"@tsconfig/next": "^2.0.3", "@tsconfig/node20": "^20.1.5", "@tsconfig/recommended": "^1.0.8", "@tsconfig/strictest": "^2.0.5"}}