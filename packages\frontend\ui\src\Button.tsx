import * as React from 'react';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?:
    | 'primary'
    | 'secondary'
    | 'danger'
    | 'success'
    | 'warning'
    | 'ghost'
    | 'outline'
    | 'modern';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className = '',
      variant = 'primary',
      size = 'md',
      loading = false,
      children,
      disabled,
      ...props
    },
    ref,
  ) => {
    const baseClasses =
      'inline-flex items-center justify-center rounded-md font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:ring-offset-background-primary disabled:pointer-events-none disabled:opacity-50 cursor-pointer';

    const variantClasses = {
      primary:
        'bg-primary-500 text-white hover:bg-primary-400 active:bg-primary-600 shadow-sm hover:shadow-cyber',
      secondary:
        'bg-background-tertiary text-text-primary border border-border-primary hover:bg-background-hover hover:border-border-secondary',
      danger:
        'bg-cyber-danger-500 text-white hover:bg-cyber-danger-400 active:bg-cyber-danger-600 shadow-sm hover:shadow-glow-red',
      success:
        'bg-cyber-matrix-500 text-white hover:bg-cyber-matrix-400 active:bg-cyber-matrix-600 shadow-sm hover:shadow-glow',
      warning:
        'bg-cyber-warning-500 text-white hover:bg-cyber-warning-400 active:bg-cyber-warning-600 shadow-sm',
      ghost:
        'text-text-primary hover:bg-background-hover hover:text-text-primary',
      outline:
        'border border-border-primary text-text-primary hover:bg-background-hover hover:border-border-accent hover:text-primary-500',
      modern:
        'bg-gradient-to-r from-primary-500/10 to-primary-600/15 hover:from-primary-500/20 hover:to-primary-600/25 border border-primary-400/30 hover:border-primary-400/50 text-primary-400 hover:text-primary-300 shadow-sm hover:shadow-md backdrop-blur-sm',
    };

    const sizeClasses = {
      sm: 'h-8 px-3 text-sm',
      md: 'h-10 px-4 py-2',
      lg: 'h-12 px-8 text-lg',
    };

    const classes =
      `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`.trim();

    return (
      <button
        className={classes}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <svg
            className="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {children}
      </button>
    );
  },
);

Button.displayName = 'Button';
