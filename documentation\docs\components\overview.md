---
sidebar_position: 1
---

# UI Components Overview

Welcome to the Telesoft UI component library! This section provides interactive examples and documentation for all available components.

## Design System

Our components follow a consistent design system with:

- **Cyber-themed aesthetics** with neon accents and modern styling
- **Consistent spacing and typography** following our design tokens
- **Accessibility** built-in with proper ARIA labels and keyboard navigation
- **Theme support** with light and dark mode variants
- **TypeScript** first with full type safety

## Component Architecture

```mermaid
graph TB
    A[ThemeProvider] --> B[Button]
    A --> C[Card]
    A --> D[Input]
    A --> E[Badge]
    A --> F[Alert]
    A --> G[Progress]
    A --> H[Status]

    B --> I[User Interactions]
    C --> J[Content Layout]
    D --> K[Form Controls]
    E --> L[Data Display]
    F --> M[Notifications]
    G --> N[Progress Indication]
    H --> O[Status Display]

    style A fill:#0ea5e9,stroke:#0284c7,color:#fff
    style I fill:#22c55e,stroke:#16a34a,color:#fff
    style J fill:#f59e0b,stroke:#d97706,color:#fff
    style K fill:#8b5cf6,stroke:#7c3aed,color:#fff
    style L fill:#ef4444,stroke:#dc2626,color:#fff
    style M fill:#06b6d4,stroke:#0891b2,color:#fff
    style N fill:#84cc16,stroke:#65a30d,color:#fff
    style O fill:#f97316,stroke:#ea580c,color:#fff
```

## Available Components

| Component              | Purpose                        | Key Features                                     |
| ---------------------- | ------------------------------ | ------------------------------------------------ |
| [Button](./button)     | User actions and interactions  | Multiple variants, loading states, accessibility |
| [Card](./card)         | Content containers and layouts | Flexible layout, header/content/footer sections  |
| [Input](./input)       | Form data collection           | Validation, error states, accessibility          |
| [Badge](./badge)       | Status indicators and labels   | Color variants, size options                     |
| [Alert](./alert)       | Important notifications        | Multiple severity levels, dismissible            |
| [Progress](./progress) | Task completion indication     | Determinate and indeterminate modes              |
| [Status](./status)     | System state display           | Color-coded status indicators                    |

## Getting Started

To use these components in your project:

1. **Install the package:**

   ```bash
   pnpm add @telesoft/ui
   ```

2. **Import styles:**

   ```tsx
   import '@telesoft/ui/styles.css';
   ```

3. **Wrap your app with ThemeProvider:**

   ```tsx
   import { ThemeProvider } from '@telesoft/ui';

   function App() {
     return <ThemeProvider>{/* Your app components */}</ThemeProvider>;
   }
   ```

4. **Import and use components:**

   ```tsx
   import { Button, Card, Input } from '@telesoft/ui';

   function MyComponent() {
     return (
       <Card>
         <Input placeholder="Enter your name" />
         <Button variant="primary">Submit</Button>
       </Card>
     );
   }
   ```

## Interactive Examples

Each component page includes:

- 📊 **Live examples** with interactive controls
- 🔧 **Props documentation** with TypeScript definitions
- 📝 **Code snippets** ready to copy and paste
- ♿ **Accessibility guidelines** and best practices
- 🎨 **Design tokens** and customization options

Navigate through the sidebar to explore each component in detail!
