'use client';

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useRef,
  useCallback,
} from 'react';
import { ThreatIncident } from '@telesoft/types';
import { threatsService, ThreatsWebSocketMessage } from '../services/threats';
import { useAppConfig } from './config-provider';

interface ThreatsContextType {
  threats: ThreatIncident[];
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastUpdate: string | null;
  connect: () => void;
  disconnect: () => void;
  refetch: () => Promise<void>;
}

const ThreatsContext = createContext<ThreatsContextType | undefined>(undefined);

interface ThreatsProviderProps {
  children: React.ReactNode;
}

export function ThreatsProvider({ children }: ThreatsProviderProps) {
  const [threats, setThreats] = useState<ThreatIncident[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const { config, loading: configLoading } = useAppConfig();
  const wsRef = useRef<WebSocket | null>(null);
  const isConnectingRef = useRef(false);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastPongRef = useRef(Date.now());
  const hasInitialized = useRef(false); // Track if we've already tried to connect

  const refetch = useCallback(async () => {
    try {
      const data = await threatsService.getThreats();
      setThreats(data);
      setError(null);
    } catch (err) {
      let errorMessage = 'Failed to fetch threats data';

      if (err instanceof Error) {
        if (
          err.message.includes('Backend Unavailable') ||
          err.message.includes('Service Unavailable') ||
          err.message.includes('503')
        ) {
          errorMessage = 'Backend service is temporarily unavailable';
        } else if (
          err.message.includes('Failed to connect') ||
          err.message.includes('Connection refused') ||
          err.message.includes('ECONNREFUSED')
        ) {
          errorMessage = 'Unable to connect to backend service';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      console.error('Failed to fetch threats via REST API:', err);

      // Don't clear existing data on error
    }
  }, []);

  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }

    threatsService.disconnectWebSocket();
    wsRef.current = null;
    setIsConnected(false);
    setIsConnecting(false);
    isConnectingRef.current = false;
  }, []);

  const handleWebSocketMessage = useCallback(
    (message: ThreatsWebSocketMessage) => {
      if (message.type === 'pong') {
        lastPongRef.current = Date.now();
        return;
      }

      if (message.type === 'threats-initial' && message.data?.incidents) {
        // Initial full data load - replace everything
        console.log('WebSocket: Received threats-initial', {
          incidentsCount: message.data.incidents.length,
          timestamp: message.timestamp,
        });
        setThreats([...message.data.incidents]); // Create new array reference
        setLastUpdate(message.timestamp);
      } else if (message.type === 'threats-update' && message.data?.incidents) {
        // Append new incidents only
        const newIncidents = message.data.incidents;
        console.log('WebSocket: Received threats-update', {
          newIncidentsCount: newIncidents.length,
          timestamp: message.timestamp,
          action: message.data.action,
        });

        if (message.data.action === 'append' && newIncidents.length > 0) {
          setThreats((prev) => {
            // Simply append new incidents and sort by time
            const updated = [...prev, ...newIncidents];

            // Remove any duplicates based on uid
            const uniqueMap = new Map<string, ThreatIncident>();
            updated.forEach((incident) => {
              uniqueMap.set(incident.uid, incident);
            });

            const uniqueThreats = Array.from(uniqueMap.values());

            // Sort by time (newest first) for consistent ordering
            return uniqueThreats.sort((a, b) => (b.time || 0) - (a.time || 0));
          });
        }

        setLastUpdate(message.timestamp);
      } else if (message.type === 'ping') {
        // Respond to ping with pong
        const pongMessage: ThreatsWebSocketMessage = {
          type: 'pong',
          timestamp: new Date().toISOString(),
        };
        threatsService.sendWebSocketMessage(pongMessage);
      }
    },
    [],
  );

  const connect = useCallback(async () => {
    if (threatsService.isWebSocketConnected()) {
      console.log('[ThreatsProvider] WebSocket already connected, skipping...');
      return; // Already connected
    }

    if (isConnectingRef.current) {
      console.log(
        '[ThreatsProvider] Connection already in progress, skipping...',
      );
      return; // Connection in progress
    }

    try {
      setIsConnecting(true);
      isConnectingRef.current = true;
      setError(null);

      console.log('[ThreatsProvider] Creating new WebSocket connection...');

      const { ws } = await threatsService.connectWebSocket(
        handleWebSocketMessage,
        (error) => {
          console.error('[ThreatsProvider] WebSocket error:', error);
          setError('WebSocket connection error');
          setIsConnecting(false);
          isConnectingRef.current = false;
        },
        (event) => {
          console.log(
            '[ThreatsProvider] WebSocket disconnected:',
            event.code,
            event.reason,
          );
          setIsConnected(false);
          setIsConnecting(false);
          isConnectingRef.current = false;

          console.log(
            'Threats WebSocket disconnected, service will handle reconnection',
          );

          // Fallback to REST API on disconnect
          if (event.code !== 1000) {
            console.log('Falling back to REST API while reconnecting...');
            refetch();
          }
        },
        () => {
          console.log('[ThreatsProvider] WebSocket connected successfully');
          setIsConnected(true);
          setIsConnecting(false);
          isConnectingRef.current = false;
          setError(null);
        },
      );

      wsRef.current = ws;
    } catch (err) {
      console.error('Error creating threats WebSocket:', err);
      setError('Failed to create WebSocket connection');
      setIsConnecting(false);
      isConnectingRef.current = false;

      // Fallback to REST API if WebSocket creation fails
      refetch();
    }
  }, [handleWebSocketMessage, refetch]);

  const disconnect = useCallback(() => {
    cleanup();
  }, [cleanup]);

  // Auto-connect when configuration is loaded (only once)
  useEffect(() => {
    if (!configLoading && config && !hasInitialized.current) {
      if (!threatsService.isWebSocketConnected() && !isConnectingRef.current) {
        console.log('[ThreatsProvider] Configuration loaded, connecting...', {
          wsUrl: config.api.wsUrl,
          useProxy: config.api.useProxy,
        });
        hasInitialized.current = true;
        connect();
      }
    }
  }, [configLoading, config, connect]);

  // Ping/keepalive mechanism
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      if (threatsService.isWebSocketConnected()) {
        // Check if we haven't received a pong in too long
        const timeSinceLastPong = Date.now() - lastPongRef.current;
        if (timeSinceLastPong > 60000) {
          // 1 minute timeout
          console.warn(
            '[Threats WebSocket] No pong received for 60s, reconnecting...',
          );
          disconnect();
          connect();
          return;
        }

        const pingMessage: ThreatsWebSocketMessage = {
          type: 'ping',
          timestamp: new Date().toISOString(),
        };
        threatsService.sendWebSocketMessage(pingMessage);
      }
    }, 30000); // Ping every 30 seconds

    pingIntervalRef.current = pingInterval;

    return () => {
      if (pingIntervalRef.current) {
        clearInterval(pingIntervalRef.current);
        pingIntervalRef.current = null;
      }
    };
  }, [isConnected, connect, disconnect]);

  // NOTE: We don't cleanup on unmount to keep the connection persistent across pages
  // Only cleanup when the app is actually closing

  const value: ThreatsContextType = {
    threats,
    isConnected,
    isConnecting,
    error,
    lastUpdate,
    connect,
    disconnect,
    refetch,
  };

  return (
    <ThreatsContext.Provider value={value}>{children}</ThreatsContext.Provider>
  );
}

export function useThreatsContext(): ThreatsContextType {
  const context = useContext(ThreatsContext);
  if (context === undefined) {
    throw new Error('useThreatsContext must be used within a ThreatsProvider');
  }
  return context;
}

/**
 * Convenience hook to get threats data from the global context
 * This maintains backwards compatibility with the useThreats pattern
 */
export function useGlobalThreats() {
  const { threats, isConnecting, error, refetch } = useThreatsContext();

  // Map the context data to match the old useThreats interface
  return {
    threats,
    loading: isConnecting && threats.length === 0,
    error,
    refresh: refetch,
  };
}
