/* Scoped UI styles for component demos */
.ui-component-demo {
  /* Import UI component styles in a scoped manner */
  /* This prevents conflicts with Docusaurus styles */
}

/* Reset some Tailwind effects that might interfere with Docusaurus */
.ui-component-demo * {
  box-sizing: border-box;
}

/* Component showcase specific styles */
.ui-component-demo .showcase-container {
  position: relative;
  isolation: isolate;
}

/* Ensure UI components render correctly */
.ui-component-demo button,
.ui-component-demo input,
.ui-component-demo div[role="alert"] {
  font-family: ui-sans-serif, system-ui, sans-serif;
}

/* Color scheme for UI components in documentation */
.ui-component-demo {
  --background-primary: #0a0a0a;
  --background-secondary: #1a1a1a;
  --background-tertiary: #2a2a2a;
  --background-hover: #3a3a3a;
  --text-primary: #ffffff;
  --text-secondary: #a1a1aa;
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --border-accent: #6366f1;
  --primary-500: #6366f1;
  --primary-400: #818cf8;
  --primary-600: #4f46e5;
  --cyber-danger-500: #ef4444;
  --cyber-danger-400: #f87171;
  --cyber-danger-600: #dc2626;
  --cyber-matrix-500: #10b981;
  --cyber-matrix-400: #34d399;
  --cyber-matrix-600: #059669;
  --cyber-warning-500: #f59e0b;
  --cyber-warning-400: #fbbf24;
  --cyber-warning-600: #d97706;
}

/* Light mode adjustments for Docusaurus light theme */
[data-theme="light"] .ui-component-demo {
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --background-hover: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
}

/* Utility classes needed for UI components */
.ui-component-demo .inline-flex {
  display: inline-flex;
}

.ui-component-demo .items-center {
  align-items: center;
}

.ui-component-demo .justify-center {
  justify-content: center;
}

.ui-component-demo .rounded-md {
  border-radius: 0.375rem;
}

.ui-component-demo .rounded-lg {
  border-radius: 0.5rem;
}

.ui-component-demo .font-medium {
  font-weight: 500;
}

.ui-component-demo .transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.ui-component-demo .duration-200 {
  transition-duration: 200ms;
}

.ui-component-demo .focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.ui-component-demo .focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ui-component-demo .focus-visible\:ring-primary-500:focus-visible {
  --tw-ring-color: var(--primary-500);
}

.ui-component-demo .focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.ui-component-demo .disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.ui-component-demo .disabled\:opacity-50:disabled {
  opacity: 0.5;
}

/* Button size classes */
.ui-component-demo .h-8 {
  height: 2rem;
}

.ui-component-demo .h-10 {
  height: 2.5rem;
}

.ui-component-demo .h-12 {
  height: 3rem;
}

.ui-component-demo .px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.ui-component-demo .px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.ui-component-demo .px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.ui-component-demo .py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.ui-component-demo .text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.ui-component-demo .text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

/* Button variant styles */
.ui-component-demo .bg-primary-500 {
  background-color: var(--primary-500);
}

.ui-component-demo .text-white {
  color: rgb(255 255 255);
}

.ui-component-demo .hover\:bg-primary-400:hover {
  background-color: var(--primary-400);
}

.ui-component-demo .active\:bg-primary-600:active {
  background-color: var(--primary-600);
}

.ui-component-demo .bg-background-tertiary {
  background-color: var(--background-tertiary);
}

.ui-component-demo .text-text-primary {
  color: var(--text-primary);
}

.ui-component-demo .border {
  border-width: 1px;
}

.ui-component-demo .border-border-primary {
  border-color: var(--border-primary);
}

.ui-component-demo .hover\:bg-background-hover:hover {
  background-color: var(--background-hover);
}

.ui-component-demo .hover\:border-border-secondary:hover {
  border-color: var(--border-secondary);
}

.ui-component-demo .bg-cyber-danger-500 {
  background-color: var(--cyber-danger-500);
}

.ui-component-demo .hover\:bg-cyber-danger-400:hover {
  background-color: var(--cyber-danger-400);
}

.ui-component-demo .active\:bg-cyber-danger-600:active {
  background-color: var(--cyber-danger-600);
}

.ui-component-demo .bg-cyber-matrix-500 {
  background-color: var(--cyber-matrix-500);
}

.ui-component-demo .hover\:bg-cyber-matrix-400:hover {
  background-color: var(--cyber-matrix-400);
}

.ui-component-demo .active\:bg-cyber-matrix-600:active {
  background-color: var(--cyber-matrix-600);
}

.ui-component-demo .bg-cyber-warning-500 {
  background-color: var(--cyber-warning-500);
}

.ui-component-demo .hover\:bg-cyber-warning-400:hover {
  background-color: var(--cyber-warning-400);
}

.ui-component-demo .active\:bg-cyber-warning-600:active {
  background-color: var(--cyber-warning-600);
}

.ui-component-demo .hover\:text-text-primary:hover {
  color: var(--text-primary);
}

.ui-component-demo .hover\:border-border-accent:hover {
  border-color: var(--border-accent);
}

.ui-component-demo .hover\:text-primary-500:hover {
  color: var(--primary-500);
}

/* Shadow utilities */
.ui-component-demo .shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.ui-component-demo .hover\:shadow-cyber:hover {
  box-shadow: 0 0 20px var(--primary-500);
}

.ui-component-demo .hover\:shadow-glow-red:hover {
  box-shadow: 0 0 20px var(--cyber-danger-500);
}

.ui-component-demo .hover\:shadow-glow:hover {
  box-shadow: 0 0 20px var(--cyber-matrix-500);
}

/* Loading spinner */
.ui-component-demo .mr-2 {
  margin-right: 0.5rem;
}

.ui-component-demo .h-4 {
  height: 1rem;
}

.ui-component-demo .w-4 {
  width: 1rem;
}

.ui-component-demo .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ui-component-demo .opacity-25 {
  opacity: 0.25;
}

.ui-component-demo .opacity-75 {
  opacity: 0.75;
}

/* Card component classes */
.ui-component-demo .flex {
  display: flex;
}

.ui-component-demo .flex-col {
  flex-direction: column;
}

.ui-component-demo .space-y-1\.5 > * + * {
  margin-top: 0.375rem;
}

.ui-component-demo .p-6 {
  padding: 1.5rem;
}

.ui-component-demo .pt-0 {
  padding-top: 0;
}

/* Badge component classes */
.ui-component-demo .rounded-full {
  border-radius: 9999px;
}

.ui-component-demo .focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.ui-component-demo .focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ui-component-demo .focus\:ring-ring:focus {
  --tw-ring-color: var(--primary-500);
}

.ui-component-demo .focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.ui-component-demo .transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Badge variant classes */
.ui-component-demo .bg-primary-500 {
  background-color: var(--primary-500);
}

.ui-component-demo .hover\:bg-primary-600:hover {
  background-color: var(--primary-600);
}

.ui-component-demo .hover\:bg-cyber-matrix-600:hover {
  background-color: var(--cyber-matrix-600);
}

.ui-component-demo .hover\:bg-cyber-warning-600:hover {
  background-color: var(--cyber-warning-600);
}

.ui-component-demo .hover\:bg-cyber-danger-600:hover {
  background-color: var(--cyber-danger-600);
}

/* Badge size classes */
.ui-component-demo .px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.ui-component-demo .px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.ui-component-demo .py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.ui-component-demo .py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.ui-component-demo .text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.ui-component-demo .text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

/* Input component classes */
.ui-component-demo input {
  font-family: ui-sans-serif, system-ui, sans-serif;
}

/* Fix for variant="primary" on Badge */
.ui-component-demo .badge-primary,
.ui-component-demo [data-variant="primary"] {
  background-color: var(--primary-500);
  color: white;
}

.ui-component-demo .badge-primary:hover,
.ui-component-demo [data-variant="primary"]:hover {
  background-color: var(--primary-600);
}

/* Additional utility classes */
.ui-component-demo .flex {
  display: flex;
}

.ui-component-demo .flex-col {
  flex-direction: column;
}

.ui-component-demo .flex-wrap {
  flex-wrap: wrap;
}

.ui-component-demo .gap-1 {
  gap: 0.25rem;
}

.ui-component-demo .gap-2 {
  gap: 0.5rem;
}

.ui-component-demo .gap-4 {
  gap: 1rem;
}

.ui-component-demo .max-w-sm {
  max-width: 24rem;
}

.ui-component-demo .max-w-md {
  max-width: 28rem;
}

.ui-component-demo .max-w-lg {
  max-width: 32rem;
}

.ui-component-demo .w-full {
  width: 100%;
}

.ui-component-demo .space-y-4 > * + * {
  margin-top: 1rem;
}

/* Progress component classes */
.ui-component-demo .relative {
  position: relative;
}

.ui-component-demo .w-full {
  width: 100%;
}

.ui-component-demo .overflow-hidden {
  overflow: hidden;
}

.ui-component-demo .rounded-full {
  border-radius: 9999px;
}

.ui-component-demo .bg-slate-800 {
  background-color: #1e293b;
}

[data-theme="light"] .ui-component-demo .bg-slate-800 {
  background-color: #e2e8f0;
}

.ui-component-demo .h-2 {
  height: 0.5rem;
}

.ui-component-demo .h-3 {
  height: 0.75rem;
}

.ui-component-demo .h-4 {
  height: 1rem;
}

.ui-component-demo .transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.ui-component-demo .duration-500 {
  transition-duration: 500ms;
}

.ui-component-demo .ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.ui-component-demo .space-y-2 > * + * {
  margin-top: 0.5rem;
}

.ui-component-demo .justify-between {
  justify-content: space-between;
}

.ui-component-demo .text-text-secondary {
  color: var(--text-secondary);
}

/* Status component classes */
.ui-component-demo .w-2 {
  width: 0.5rem;
}

.ui-component-demo .w-3 {
  width: 0.75rem;
}

.ui-component-demo .bg-slate-500 {
  background-color: #64748b;
}

[data-theme="light"] .ui-component-demo .bg-slate-500 {
  background-color: #94a3b8;
}

.ui-component-demo .animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Additional spacing utilities */
.ui-component-demo .gap-0\.5 {
  gap: 0.125rem;
}

.ui-component-demo .gap-3 {
  gap: 0.75rem;
}

.ui-component-demo .space-y-0\.75 > * + * {
  margin-top: 0.1875rem;
}

.ui-component-demo .space-y-1\.5 > * + * {
  margin-top: 0.375rem;
}

.ui-component-demo .grid {
  display: grid;
}

.ui-component-demo .grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.ui-component-demo .p-4 {
  padding: 1rem;
}

.ui-component-demo .mb-2 {
  margin-bottom: 0.5rem;
}

.ui-component-demo .border-gray-200 {
  border-color: #e5e7eb;
}

[data-theme="dark"] .ui-component-demo .border-gray-200 {
  border-color: var(--border-primary);
}

.ui-component-demo .rounded-lg {
  border-radius: 0.5rem;
}
