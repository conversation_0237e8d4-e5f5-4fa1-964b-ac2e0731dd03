{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "extends": "@tsconfig/recommended/tsconfig.json", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "bundler", "preserveWatchOutput": true, "strictNullChecks": true, "target": "es2022", "module": "esnext", "lib": ["es2022", "dom", "dom.iterable"], "incremental": true, "tsBuildInfoFile": ".tsbuildinfo", "resolveJsonModule": true, "moduleDetection": "force"}, "exclude": ["node_modules", "dist"]}