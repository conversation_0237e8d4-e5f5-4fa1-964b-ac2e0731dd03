import React from 'react';

export interface TabButtonProps {
  id: string;
  label: string;
  isActive: boolean;
  onClick: () => void;
  disabled?: boolean;
  className?: string;
}

export const TabButton: React.FC<TabButtonProps> = ({
  id,
  label,
  isActive,
  onClick,
  disabled = false,
  className = '',
}) => {
  return (
    <button
      id={id}
      onClick={onClick}
      disabled={disabled}
      className={`relative px-6 py-3 text-sm font-medium transition-all duration-300 border-b-2 -mb-px cursor-pointer ${
        isActive
          ? 'border-primary-400 text-text-primary bg-gradient-to-t from-primary-500/10 to-transparent'
          : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border-primary/50 hover:bg-background-hover/20'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
    >
      <span className="relative z-10">{label}</span>
      {isActive && (
        <>
          <div className="absolute inset-0 bg-gradient-to-t from-background-secondary/20 to-transparent rounded-t-lg"></div>
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-400/60 via-primary-400 to-primary-400/60"></div>
        </>
      )}
    </button>
  );
};

export interface TabGroupProps {
  tabs: Array<{
    id: string;
    label: string;
    disabled?: boolean;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export const TabGroup: React.FC<TabGroupProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className = '',
}) => {
  return (
    <div className={`flex border-b border-border-primary/20 ${className}`}>
      {tabs.map((tab) => (
        <TabButton
          key={tab.id}
          id={tab.id}
          label={tab.label}
          isActive={activeTab === tab.id}
          onClick={() => onTabChange(tab.id)}
          disabled={tab.disabled}
        />
      ))}
    </div>
  );
};
