/**
 * Next.js API Route: /api/v1/machine-learning/deployments
 * Proxies requests to the backend deployments endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  backendApiProxy,
  BackendApiError,
} from '../../../../../lib/backend-proxy';

export async function GET(request: NextRequest) {
  try {
    console.log(
      '[API Route] GET /api/v1/machine-learning/deployments - Proxying to backend...',
    );

    // Check for batch request
    const { searchParams } = new URL(request.url);
    const batchEndpoints = searchParams.get('batch');

    if (batchEndpoints) {
      try {
        const endpoints = batchEndpoints.split(',');
        const results = await backendApiProxy.batchGet(endpoints);
        return NextResponse.json(
          { batch: results },
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'private, max-age=0, must-revalidate',
            },
          },
        );
      } catch (batchError) {
        console.error('[API Route] Batch request error:', batchError);
        return NextResponse.json(
          {
            error: 'Batch Request Failed',
            message: 'Failed to process batch request',
            timestamp: new Date().toISOString(),
          },
          { status: 500 },
        );
      }
    }

    // Forward the request to the backend with proper error handling
    let response;
    try {
      response = await backendApiProxy.forwardRequest(
        '/api/v1/machine-learning/deployments',
        {
          method: 'GET',
        },
      );
    } catch (proxyError) {
      console.error('[API Route] Backend proxy error:', proxyError);

      // Handle specific backend connectivity issues
      if (proxyError instanceof BackendApiError) {
        const isConnectivityIssue =
          proxyError.message.includes('ECONNREFUSED') ||
          proxyError.message.includes('Failed to connect to backend') ||
          proxyError.message.includes('fetch failed');

        if (isConnectivityIssue) {
          return NextResponse.json(
            {
              error: 'Backend Unavailable',
              message:
                'The backend service is currently unavailable. Please try again later.',
              details: 'Backend server connection failed',
              timestamp: new Date().toISOString(),
              retryAfter: 30, // Suggest retry after 30 seconds
            },
            {
              status: 503, // Service Unavailable
              headers: {
                'X-Error-Type': 'backend-unavailable',
                'Retry-After': '30',
              },
            },
          );
        }

        // Other backend API errors
        return NextResponse.json(
          {
            error: 'Backend API Error',
            message: proxyError.message,
            timestamp: new Date().toISOString(),
          },
          {
            status: proxyError.status || 500,
            headers: {
              'X-Error-Type': 'backend-error',
            },
          },
        );
      }

      // Unknown proxy errors
      throw proxyError;
    }

    // Get the response data
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('[API Route] JSON parsing error:', jsonError);
      return NextResponse.json(
        {
          error: 'Response Parse Error',
          message: 'Failed to parse backend response',
          timestamp: new Date().toISOString(),
        },
        { status: 502 }, // Bad Gateway
      );
    }

    // Add ETag for caching
    const etag = `"${Date.now()}"`;
    const ifNoneMatch = request.headers.get('if-none-match');

    if (ifNoneMatch === etag) {
      return new NextResponse(null, { status: 304 });
    }

    // Return the response with appropriate headers
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
        ETag: etag,
        'X-Response-Time': `${Date.now()}ms`,
      },
    });
  } catch (error) {
    console.error(
      '[API Route] Unhandled error in /api/v1/machine-learning/deployments:',
      error,
    );

    // Ensure we always return a proper JSON response
    const errorResponse = {
      error: 'Internal Server Error',
      message: 'An unexpected error occurred while processing the request',
      timestamp: new Date().toISOString(),
    };

    // Add more specific error information if available
    if (error instanceof Error) {
      // Check for specific error types
      if (
        error.message.includes('Backend Unavailable') ||
        error.message.includes('Service Unavailable') ||
        error.message.includes('503')
      ) {
        return NextResponse.json(
          {
            ...errorResponse,
            error: 'Backend Unavailable',
            message:
              'Backend service is temporarily unavailable. Please try again later.',
          },
          {
            status: 503,
            headers: {
              'X-Error-Type': 'backend-unavailable',
              'Retry-After': '30',
            },
          },
        );
      }

      if (
        error.message.includes('Failed to connect') ||
        error.message.includes('Connection refused') ||
        error.message.includes('ECONNREFUSED')
      ) {
        return NextResponse.json(
          {
            ...errorResponse,
            error: 'Backend Connection Failed',
            message:
              'Unable to connect to the backend service. Please check your connection and try again.',
          },
          {
            status: 503,
            headers: {
              'X-Error-Type': 'connection-failed',
              'Retry-After': '30',
            },
          },
        );
      }

      // Include error message for debugging in development
      if (process.env.NODE_ENV === 'development') {
        errorResponse.message = error.message;
      }
    }

    return NextResponse.json(errorResponse, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'X-Error-Type': 'internal-error',
      },
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log(
      '[API Route] POST /api/v1/machine-learning/deployments - Proxying to backend...',
    );

    // Get the request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('[API Route] Failed to parse request body:', parseError);
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'Invalid JSON in request body',
          timestamp: new Date().toISOString(),
        },
        { status: 400 },
      );
    }

    // Validate required fields
    if (!body.type || !body.name) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'Both "type" and "name" fields are required',
          timestamp: new Date().toISOString(),
        },
        { status: 400 },
      );
    }

    // Forward the request to the backend
    const response = await backendApiProxy.forwardRequest(
      '/api/v1/machine-learning/deployments',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    // Handle the response
    const responseData = await response.json();

    return NextResponse.json(responseData, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] POST deployment error:', error);

    if (error instanceof BackendApiError) {
      return NextResponse.json(
        {
          error: 'Backend API Error',
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        { status: error.status || 500 },
      );
    }

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to create deployment',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/v1/machine-learning/deployments
 * Delete multiple deployments
 */
export async function DELETE(request: NextRequest) {
  try {
    console.log(
      '[API Route] DELETE /api/v1/machine-learning/deployments - Proxying to backend...',
    );

    // Get the request body
    const body = await request.json().catch(() => null);

    if (!body) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'Request body is required for DELETE operation',
          timestamp: new Date().toISOString(),
        },
        { status: 400 },
      );
    }

    console.log('[API Route] DELETE request body:', body);

    // Forward the DELETE request to the backend
    const response = await backendApiProxy.forwardRequest(
      '/api/v1/machine-learning/deployments',
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    const responseData = await response.json();

    console.log(
      `[API Route] Backend DELETE response: status=${response.status}`,
      responseData,
    );

    // Return the backend response
    return NextResponse.json(responseData, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error(
      '[API Route] DELETE /api/v1/machine-learning/deployments error:',
      error,
    );

    if (error instanceof BackendApiError) {
      return NextResponse.json(
        {
          error: 'Backend Error',
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        { status: error.status || 500 },
      );
    }

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to delete deployments',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Health check endpoint
 */
export async function HEAD(_request: NextRequest) {
  try {
    // Perform a lightweight check to the backend
    const response = await backendApiProxy.forwardRequest(
      '/api/v1/machine-learning/deployments',
      {
        method: 'HEAD',
      },
    );

    return new NextResponse(null, {
      status: response.status,
      headers: {
        'X-Health-Check': 'ok',
        'X-Backend-Status': response.status.toString(),
      },
    });
  } catch (error) {
    console.error('[API Route] Health check failed:', error);

    return new NextResponse(null, {
      status: 503,
      headers: {
        'X-Health-Check': 'failed',
        'X-Backend-Status': 'unavailable',
      },
    });
  }
}
