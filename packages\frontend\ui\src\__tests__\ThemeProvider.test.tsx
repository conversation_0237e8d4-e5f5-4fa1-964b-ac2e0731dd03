import React from 'react';
import { render, screen, act, renderHook } from '@testing-library/react';
import { ThemeProvider, useTheme } from '../ThemeProvider';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    clear: () => {
      store = {};
    },
    removeItem: (key: string) => {
      delete store[key];
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Test component that uses the theme hook
const TestThemeConsumer: React.FC<{ testId?: string }> = ({
  testId = 'theme-consumer',
}) => {
  const { theme, setTheme } = useTheme();

  return (
    <div data-testid={testId}>
      <span data-testid="current-theme">{theme}</span>
      <button data-testid="set-light" onClick={() => setTheme('light')}>
        Set Light
      </button>
      <button data-testid="set-dark" onClick={() => setTheme('dark')}>
        Set Dark
      </button>
      <button data-testid="set-system" onClick={() => setTheme('system')}>
        Set System
      </button>
    </div>
  );
};

describe('ThemeProvider', () => {
  beforeEach(() => {
    localStorageMock.clear();
    // Reset document classes
    document.documentElement.className = '';
    jest.clearAllMocks();
  });

  describe('Basic rendering', () => {
    it('renders children correctly', () => {
      render(
        <ThemeProvider>
          <div>Test content</div>
        </ThemeProvider>,
      );
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });

    it('wraps children in ui-dark container', () => {
      render(
        <ThemeProvider>
          <div data-testid="child">Test</div>
        </ThemeProvider>,
      );
      const child = screen.getByTestId('child');
      expect(child.parentElement).toHaveClass('ui-dark');
    });

    it('applies default theme (dark)', () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>,
      );
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
    });
  });

  describe('Theme initialization', () => {
    it('uses defaultTheme prop', () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>,
      );
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
    });

    it('reads theme from localStorage', () => {
      localStorageMock.setItem('ui-theme', 'light');
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>,
      );
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
    });

    it('uses custom storage key', () => {
      localStorageMock.setItem('custom-theme-key', 'system');
      render(
        <ThemeProvider storageKey="custom-theme-key">
          <TestThemeConsumer />
        </ThemeProvider>,
      );
      expect(screen.getByTestId('current-theme')).toHaveTextContent('system');
    });

    it('falls back to defaultTheme when localStorage is empty', () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>,
      );
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
    });
  });

  describe('Theme switching', () => {
    it('changes theme when setTheme is called', () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');

      act(() => {
        screen.getByTestId('set-light').click();
      });

      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
    });

    it('saves theme to localStorage when changed', () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      act(() => {
        screen.getByTestId('set-light').click();
      });

      expect(localStorageMock.getItem('ui-theme')).toBe('light');
    });

    it('uses custom storage key for saving', () => {
      render(
        <ThemeProvider storageKey="custom-key">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      act(() => {
        screen.getByTestId('set-system').click();
      });

      expect(localStorageMock.getItem('custom-key')).toBe('system');
    });
  });

  describe('DOM class management', () => {
    it('applies dark class to document root', () => {
      render(
        <ThemeProvider defaultTheme="dark">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(document.documentElement).toHaveClass('dark');
      expect(document.documentElement).not.toHaveClass('light');
    });

    it('applies light class to document root', () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(document.documentElement).toHaveClass('light');
      expect(document.documentElement).not.toHaveClass('dark');
    });

    it('updates document classes when theme changes', () => {
      render(
        <ThemeProvider defaultTheme="dark">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(document.documentElement).toHaveClass('dark');

      act(() => {
        screen.getByTestId('set-light').click();
      });

      expect(document.documentElement).toHaveClass('light');
      expect(document.documentElement).not.toHaveClass('dark');
    });

    it('handles system theme with dark preference', () => {
      // Mock matchMedia to return dark preference
      (window.matchMedia as jest.Mock).mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      render(
        <ThemeProvider defaultTheme="system">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(document.documentElement).toHaveClass('dark');
      expect(document.documentElement).not.toHaveClass('light');
    });

    it('handles system theme with light preference', () => {
      // Mock matchMedia to return light preference
      (window.matchMedia as jest.Mock).mockImplementation((query) => ({
        matches: query !== '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      render(
        <ThemeProvider defaultTheme="system">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(document.documentElement).toHaveClass('light');
      expect(document.documentElement).not.toHaveClass('dark');
    });

    it('switches to system theme correctly', () => {
      // Mock matchMedia to return dark preference
      (window.matchMedia as jest.Mock).mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(document.documentElement).toHaveClass('light');

      act(() => {
        screen.getByTestId('set-system').click();
      });

      expect(document.documentElement).toHaveClass('dark');
      expect(document.documentElement).not.toHaveClass('light');
    });
  });

  describe('useTheme hook', () => {
    it('throws error when used outside ThemeProvider', () => {
      // Suppress console.error for this test since React will log the error
      const originalError = console.error;
      console.error = jest.fn();

      // This should throw when renderHook is called
      expect(() => {
        renderHook(() => useTheme());
      }).toThrow('useTheme must be used within a ThemeProvider');

      console.error = originalError;
    });

    it('provides theme and setTheme function', () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      // Verify theme value is available
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');

      // Verify setTheme function works
      expect(screen.getByTestId('set-light')).toBeInTheDocument();
      expect(screen.getByTestId('set-dark')).toBeInTheDocument();
      expect(screen.getByTestId('set-system')).toBeInTheDocument();
    });
  });

  describe('Multiple consumers', () => {
    it('provides same theme to multiple consumers', () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer testId="consumer1" />
          <TestThemeConsumer testId="consumer2" />
        </ThemeProvider>,
      );

      expect(
        screen
          .getByTestId('consumer1')
          .querySelector('[data-testid="current-theme"]'),
      ).toHaveTextContent('light');
      expect(
        screen
          .getByTestId('consumer2')
          .querySelector('[data-testid="current-theme"]'),
      ).toHaveTextContent('light');
    });

    it('updates all consumers when theme changes', () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer testId="consumer1" />
          <TestThemeConsumer testId="consumer2" />
        </ThemeProvider>,
      );

      act(() => {
        screen.getAllByTestId('set-light')[0].click();
      });

      expect(
        screen
          .getByTestId('consumer1')
          .querySelector('[data-testid="current-theme"]'),
      ).toHaveTextContent('light');
      expect(
        screen
          .getByTestId('consumer2')
          .querySelector('[data-testid="current-theme"]'),
      ).toHaveTextContent('light');
    });
  });

  describe('Edge cases', () => {
    it('handles invalid theme from localStorage', () => {
      localStorageMock.setItem('ui-theme', 'invalid-theme');
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>,
      );
      // Should fall back to defaultTheme
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
    });

    it('works without localStorage', () => {
      // Temporarily remove localStorage
      const originalLocalStorage = window.localStorage;
      delete (window as unknown as { localStorage?: unknown }).localStorage;

      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>,
      );

      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');

      // Restore localStorage
      Object.defineProperty(window, 'localStorage', {
        value: originalLocalStorage,
      });
    });
  });
});
