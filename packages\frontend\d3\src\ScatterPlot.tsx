import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface ScatterPlotData {
  x: number;
  y: number;
  label?: string;
}

export interface ScatterPlotProps {
  data: ScatterPlotData[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  color?: string;
  radius?: number;
  className?: string;
  xLabel?: string;
  yLabel?: string;
}

export const ScatterPlot: React.FC<ScatterPlotProps> = ({
  data,
  width = 400,
  height = 300,
  margin = { top: 20, right: 20, bottom: 40, left: 40 },
  color = '#3b82f6',
  radius = 4,
  className = '',
  xLabel,
  yLabel,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const xScale = d3
      .scaleLinear()
      .domain(d3.extent(data, (d: ScatterPlotData) => d.x) as [number, number])
      .range([0, innerWidth]);

    const yScale = d3
      .scaleLinear()
      .domain(d3.extent(data, (d: ScatterPlotData) => d.y) as [number, number])
      .range([innerHeight, 0]);

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add dots
    g.selectAll('.dot')
      .data(data)
      .enter()
      .append('circle')
      .attr('class', 'dot')
      .attr('cx', (d: ScatterPlotData) => xScale(d.x))
      .attr('cy', (d: ScatterPlotData) => yScale(d.y))
      .attr('r', radius)
      .attr('fill', color)
      .attr('opacity', 0.7)
      .style('cursor', 'pointer')
      .on('mouseover', function (event, d: ScatterPlotData) {
        d3.select(this)
          .attr('r', radius + 2)
          .attr('opacity', 1);

        // Add tooltip
        const tooltip = d3
          .select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        const content = d.label
          ? `${d.label}: (${d.x}, ${d.y})`
          : `(${d.x}, ${d.y})`;
        tooltip
          .html(content)
          .style('left', event.pageX + 10 + 'px')
          .style('top', event.pageY - 10 + 'px');
      })
      .on('mouseout', function () {
        d3.select(this).attr('r', radius).attr('opacity', 0.7);
        d3.selectAll('.tooltip').remove();
      });

    // Add x-axis
    const xAxis = g
      .append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    xAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    xAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add y-axis
    const yAxis = g.append('g').call(d3.axisLeft(yScale));

    yAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    yAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add x-axis label
    if (xLabel) {
      g.append('text')
        .attr(
          'transform',
          `translate(${innerWidth / 2}, ${innerHeight + margin.bottom - 5})`,
        )
        .style('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text(xLabel);
    }

    // Add y-axis label
    if (yLabel) {
      g.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('y', 0 - margin.left)
        .attr('x', 0 - innerHeight / 2)
        .attr('dy', '1em')
        .style('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text(yLabel);
    }
  }, [data, width, height, margin, color, radius, xLabel, yLabel]);

  return (
    <div className={className}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ display: 'block' }}
      />
    </div>
  );
};
