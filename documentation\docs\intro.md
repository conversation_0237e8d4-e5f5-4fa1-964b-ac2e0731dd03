---
sidebar_position: 1
---

# Welcome to Telesoft UI

Welcome to the **Telesoft UI** documentation! This is a comprehensive design system and component library built for modern intelligence and analytics applications.

## What is Telesoft UI?

Telesoft UI is a **monorepo** containing:

- 🎨 **Design System**: Consistent visual language and components
- ⚛️ **React Components**: Reusable UI components built with TypeScript
- 📊 **Data Visualization**: D3.js-based charts and visualization components
- 🏗️ **Applications**: Frontend and backend applications
- 🛠️ **Development Tools**: Shared configuration and build tools

## Getting Started

Get started by exploring our documentation:

### [🏛️ Architecture](./architecture/overview.md)

Learn about the system architecture, monorepo structure, and design principles.

### [📚 Tutorial Basics](./tutorial-basics/create-a-document.md)

Learn the **fundamentals** of working with this documentation system.

### [🔧 Tutorial Extras](./tutorial-extras/manage-docs-versions.md)

Discover **advanced features** and best practices.

## Key Features

```mermaid
mindmap
  root((Telesoft UI))
    Components
      React Components
      TypeScript Support
      Tailwind CSS
    Visualization
      D3.js Charts
      Interactive Dashboards
      Real-time Data
    Architecture
      Monorepo Structure
      Shared Packages
      Build Pipeline
    Applications
      Frontend Apps
      Backend Services
      API Integration
```

## Quick Overview

The project is organized into several key areas:

- **`apps/`** - Deployable applications (frontend & backend)
- **`packages/`** - Shared libraries and configuration
- **`documentation/`** - This documentation site

Each package is designed to be modular, reusable, and type-safe, enabling rapid development of intelligence and analytics applications.

---

Ready to dive in? Start with our [Architecture Overview](./architecture/overview.md) to understand the system design!
