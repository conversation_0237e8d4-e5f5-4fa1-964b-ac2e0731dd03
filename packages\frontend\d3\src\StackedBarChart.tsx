import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface StackedBarChartDataItem {
  label: string;
  value: number;
  color: string;
  category: string;
}

export interface StackedBarChartData {
  label: string; // Time label (e.g., "09:00")
  items: StackedBarChartDataItem[]; // Array of stacked items for this time
  total: number; // Total value for this time slot
}

export interface StackedBarChartProps {
  data: StackedBarChartData[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  className?: string;
  showLegend?: boolean;
  showYAxisLabel?: boolean;
}

export const StackedBarChart: React.FC<StackedBarChartProps> = ({
  data,
  width = 400,
  height = 300,
  margin = { top: 20, right: 20, bottom: 40, left: 40 },
  className = '',
  showLegend = true,
  showYAxisLabel = true,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // Create scales
    const xScale = d3
      .scaleBand()
      .domain(data.map((d) => d.label))
      .range([0, innerWidth])
      .padding(0.1);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(data, (d) => d.total) || 0])
      .range([innerHeight, 0]);

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Create stack data for each time slot
    data.forEach((timeSlot) => {
      const barGroup = g
        .append('g')
        .attr('class', 'bar-group')
        .attr('transform', `translate(${xScale(timeSlot.label)!}, 0)`);

      // If no items (empty hour), show a minimal placeholder bar
      if (timeSlot.items.length === 0 || timeSlot.total === 0) {
        barGroup
          .append('rect')
          .attr('class', 'bar-placeholder')
          .attr('x', 0)
          .attr('width', xScale.bandwidth())
          .attr('y', innerHeight - 2) // Very small height at bottom
          .attr('height', 2)
          .attr('fill', '#374151') // Gray color for empty hours
          .attr('opacity', 0.3)
          .style('cursor', 'pointer')
          .on('mouseover', function (event) {
            // Add tooltip for empty hours
            const tooltip = d3
              .select('body')
              .append('div')
              .attr('class', 'tooltip')
              .style('position', 'absolute')
              .style('background', 'rgba(0, 0, 0, 0.8)')
              .style('color', 'white')
              .style('padding', '8px')
              .style('border-radius', '4px')
              .style('font-size', '12px')
              .style('pointer-events', 'none')
              .style('opacity', 0)
              .style('z-index', '1000');

            tooltip.transition().duration(200).style('opacity', 1);
            tooltip
              .html(`${timeSlot.label}<br/>No incidents`)
              .style('left', event.pageX + 10 + 'px')
              .style('top', event.pageY - 10 + 'px');
          })
          .on('mouseout', function () {
            d3.selectAll('.tooltip').remove();
          });
      } else {
        // Draw stacked segments for hours with data
        let cumulativeValue = 0;

        timeSlot.items.forEach((item) => {
          const segmentHeight =
            yScale(cumulativeValue) - yScale(cumulativeValue + item.value);

          barGroup
            .append('rect')
            .attr('class', 'bar-segment')
            .attr('x', 0)
            .attr('width', xScale.bandwidth())
            .attr('y', yScale(cumulativeValue + item.value))
            .attr('height', Math.abs(segmentHeight))
            .attr('fill', item.color)
            .style('cursor', 'pointer')
            .on('mouseover', function (event) {
              d3.select(this).attr('opacity', 0.8);

              // Add tooltip
              const tooltip = d3
                .select('body')
                .append('div')
                .attr('class', 'tooltip')
                .style('position', 'absolute')
                .style('background', 'rgba(0, 0, 0, 0.8)')
                .style('color', 'white')
                .style('padding', '8px')
                .style('border-radius', '4px')
                .style('font-size', '12px')
                .style('pointer-events', 'none')
                .style('opacity', 0)
                .style('z-index', '1000');

              tooltip.transition().duration(200).style('opacity', 1);
              tooltip
                .html(`${timeSlot.label}<br/>${item.label}: ${item.value}`)
                .style('left', event.pageX + 10 + 'px')
                .style('top', event.pageY - 10 + 'px');
            })
            .on('mouseout', function () {
              d3.select(this).attr('opacity', 1);
              d3.selectAll('.tooltip').remove();
            });

          cumulativeValue += item.value;
        });
      }
    });

    // Add x-axis
    const xAxis = g
      .append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    xAxis
      .selectAll('text')
      .style('fill', 'var(--color-text-primary)')
      .style('font-size', '11px');
    xAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add y-axis
    const yAxis = g.append('g').call(d3.axisLeft(yScale));

    yAxis
      .selectAll('text')
      .style('fill', 'var(--color-text-primary)')
      .style('font-size', '11px');
    yAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add y-axis label (conditionally)
    if (showYAxisLabel) {
      g.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('y', 0 - margin.left)
        .attr('x', 0 - innerHeight / 2)
        .attr('dy', '1em')
        .style('text-anchor', 'middle')
        .style('fill', 'var(--color-text-primary)')
        .style('font-size', '12px')
        .text('Incident Count');
    }
  }, [data, width, height, margin, showYAxisLabel]);

  // Get unique categories for legend
  const legendItems = React.useMemo(() => {
    const uniqueCategories = new Map<
      string,
      { label: string; color: string }
    >();

    data.forEach((timeSlot) => {
      timeSlot.items.forEach((item) => {
        if (!uniqueCategories.has(item.category)) {
          uniqueCategories.set(item.category, {
            label: item.label,
            color: item.color,
          });
        }
      });
    });

    return Array.from(uniqueCategories.values());
  }, [data]);

  return (
    <div ref={containerRef} className={className}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ display: 'block' }}
      />
      {showLegend && legendItems.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2 justify-center">
          {legendItems.map((item, index) => (
            <div key={index} className="flex items-center gap-1 text-xs">
              <div
                className="w-3 h-3 rounded"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-text-primary">{item.label}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
