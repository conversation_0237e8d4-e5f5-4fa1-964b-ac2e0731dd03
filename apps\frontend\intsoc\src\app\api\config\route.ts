import { NextResponse } from 'next/server';

export interface ClientConfig {
  api: {
    baseUrl: string;
    wsUrl: string;
    timeout: number;
    useProxy: boolean;
    enableCache: boolean;
    cacheTimeout: number;
  };
  debug: {
    apiDebug: boolean;
  };
  app: {
    name: string;
  };
}

/**
 * API route to serve client configuration
 * This allows environment variables to be read at runtime instead of build time
 */
export async function GET() {
  try {
    // Read environment variables at runtime (server-side)
    const useProxy = process.env.USE_API_PROXY === 'true';

    const config: ClientConfig = {
      api: {
        // When using proxy, baseUrl should point to the frontend server (Next.js)
        // When not using proxy, baseUrl should point directly to backend
        baseUrl: useProxy
          ? process.env.API_BASE_URL || 'http://localhost:3000' // Frontend server for Next.js API routes
          : process.env.BACKEND_API_URL || 'http://localhost:4001', // Direct backend
        wsUrl: process.env.WS_URL || 'ws://localhost:4001/ws', // Fixed: include /ws path in fallback
        timeout: parseInt(process.env.API_TIMEOUT || '10000', 10),
        useProxy,
        enableCache: process.env.ENABLE_API_CACHE === 'true',
        cacheTimeout: parseInt(process.env.API_CACHE_TIMEOUT || '60000', 10),
      },
      debug: {
        apiDebug: process.env.DEBUG_API === 'true',
      },
      app: {
        name: process.env.APPLICATION_NAME || 'IntSOC',
      },
    };

    // Return configuration with appropriate cache headers
    return NextResponse.json(config, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error) {
    console.error('Error serving client configuration:', error);

    // Return default configuration on error
    return NextResponse.json(
      {
        api: {
          baseUrl: 'http://localhost:3000', // Default to frontend server
          wsUrl: 'ws://localhost:4001/ws', // Fixed: include /ws path in fallback
          timeout: 10000,
          useProxy: true, // Default to proxy mode
          enableCache: false,
          cacheTimeout: 60000,
        },
        debug: {
          apiDebug: false,
        },
        app: {
          name: 'IntSOC',
        },
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      },
    );
  }
}
