'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardContent,
  Badge,
  Alert,
  AlertTitle,
  AlertDescription,
  Progress,
  Status,
} from '@telesoft/ui';
import { User } from '@telesoft/types';
import { useThreatStats } from '../../lib/hooks/useThreats';
import { useThreatsContext } from '../../lib/contexts/ThreatsContext';
import { useSystemMetrics } from '../../lib/hooks/useSystemMetrics';

export default function Home() {
  const { threats, isConnecting } = useThreatsContext();
  const threatStats = useThreatStats(threats);
  const [lastLogin, setLastLogin] = useState<string>('--');

  const {
    metrics: systemMetrics,
    isConnected: wsConnected,
    isConnecting: wsConnecting,
    error: wsError,
  } = useSystemMetrics();

  // Map WebSocket states to the expected loading state
  const loading = isConnecting && threats.length === 0;

  useEffect(() => {
    const now = new Date();
    setLastLogin(now.toLocaleString());
  }, []);

  const mockUser: User = {
    id: '1',
    email: 'admin@telesoft',
    name: 'Security Administrator',
    role: 'admin',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Reusable class combinations to reduce duplication
  const cardPrimary = 'bg-surface-primary border-border-primary elevation-low';
  const cardGlass = 'glass-effect border-border-primary elevation-medium';
  const statusItem =
    'flex items-center justify-between p-3 glass-effect rounded-lg border border-border-muted';
  const metricCard =
    'glass-effect border border-border-primary rounded-lg p-4 elevation-low';
  const miniMetric =
    'text-center p-3 bg-surface-secondary rounded-lg border border-border-muted';

  return (
    <main className="min-h-screen bg-background-primary p-8 pt-24">
      <div className="mx-auto max-w-7xl">
        {/* Header Section - Enhanced with modern styling */}
        <div className={`mb-12 text-center ${cardGlass} rounded-2xl p-8`}>
          <h1 className="mb-4 text-6xl font-bold text-text-primary">
            INTSOC{' '}
            {/*<span className="gradient-cyber bg-clip-text text-transparent">
              Security Operations
            </span>*/}
          </h1>
          <p className="text-xl text-text-secondary mb-4">
            Security Operations
          </p>
          {/*<div className="flex justify-center gap-2 text-sm text-text-subtle">
            <span>🎨 Refined Colors</span>
            <span>•</span>
            <span>🔮 Glass Effects</span>
            <span>•</span>
            <span>✨ Enhanced Depth</span>
          </div> */}

          {/* WebSocket Error Alert */}
          {wsError && (
            <Alert variant="warning" className="mt-4">
              <AlertTitle>Real-time Data Connection Issue</AlertTitle>
              <AlertDescription>
                {wsError}. System metrics may not be current.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div className="mb-6">
          <div className="grid gap-6 md:grid-cols-1">
            {/* System Monitor - Enhanced with modern styling */}
            <Card className={`${cardGlass} md:col-span-2`}>
              <CardHeader>
                <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                  📊 System Monitor
                  <div className="ml-auto flex gap-3 items-center">
                    {/* WebSocket Status */}
                    <div className="flex items-center gap-2 text-xs">
                      {wsConnected ? (
                        <>
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          <span className="text-green-400">Live Data</span>
                        </>
                      ) : wsConnecting ? (
                        <>
                          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                          <span className="text-yellow-400">Connecting...</span>
                        </>
                      ) : (
                        <>
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          <span className="text-red-400">Offline</span>
                        </>
                      )}
                    </div>
                  </div>
                </h2>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-3">
                  <div className={metricCard}>
                    <div className="text-text-secondary text-sm mb-2 flex items-center gap-2">
                      <div className="w-3 h-3 bg-cyber-matrix-500 rounded-full glow-green"></div>
                      Network Security
                    </div>
                    <div className="text-3xl font-bold text-cyber-matrix-500 mb-2">
                      98.7%
                    </div>
                    <Progress value={98.7} variant="success" className="mb-2" />
                    <div className="text-xs text-text-subtle">
                      Excellent protection level
                    </div>
                  </div>

                  <div className={metricCard}>
                    <div className="text-text-secondary text-sm mb-2 flex items-center gap-2">
                      <div className="w-3 h-3 bg-cyber-warning-500 rounded-full animate-pulse"></div>
                      Threat Level
                    </div>
                    <div className="text-3xl font-bold text-cyber-warning-500 mb-2">
                      Medium
                    </div>
                    <Progress value={45} variant="warning" className="mb-2" />
                    <div className="text-xs text-text-subtle">
                      Monitoring active threats
                    </div>
                  </div>

                  <div className={metricCard}>
                    <div className="text-text-secondary text-sm mb-2 flex items-center gap-2">
                      <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
                      Active Connections
                    </div>
                    <div className="text-3xl font-bold text-primary-500 mb-2">
                      1,247
                    </div>
                    <div className="text-sm text-cyber-matrix-500 font-medium">
                      +12% from yesterday
                    </div>
                    <div className="text-xs text-text-subtle">
                      Peak performance
                    </div>
                  </div>
                </div>

                {/* Additional modern metrics */}
                <div className="mt-6 grid gap-4 md:grid-cols-4">
                  <div className={miniMetric}>
                    <div className="text-xs text-text-subtle mb-1 flex items-center gap-2">
                      System CPU
                      {wsConnected ? (
                        <div className="w-1.5 h-1.5 bg-cyber-matrix-500 rounded-full animate-pulse glow-green"></div>
                      ) : (
                        <div className="w-1.5 h-1.5 bg-gray-500 rounded-full"></div>
                      )}
                    </div>
                    <div className="text-lg font-bold text-primary-400">
                      {systemMetrics
                        ? `${systemMetrics.cpu.system.toFixed(1)}%`
                        : '--'}
                    </div>
                    {systemMetrics && (
                      <div className="text-xs text-text-subtle">
                        Node.js: {systemMetrics.cpu.process.toFixed(1)}%
                      </div>
                    )}
                  </div>
                  <div className={miniMetric}>
                    <div className="text-xs text-text-subtle mb-1 flex items-center gap-2">
                      Memory
                      {wsConnected ? (
                        <div className="w-1.5 h-1.5 bg-cyber-matrix-500 rounded-full animate-pulse glow-green"></div>
                      ) : (
                        <div className="w-1.5 h-1.5 bg-gray-500 rounded-full"></div>
                      )}
                    </div>
                    <div className="text-lg font-bold text-cyber-matrix-400">
                      {systemMetrics
                        ? `${systemMetrics.memory.usagePercent.toFixed(1)}%`
                        : '--'}
                    </div>
                    {systemMetrics && (
                      <div className="text-xs text-text-subtle">
                        {systemMetrics.memory.used.toFixed(0)}MB /{' '}
                        {systemMetrics.memory.total.toFixed(0)}MB
                      </div>
                    )}
                  </div>
                  <div className={miniMetric}>
                    <div className="text-xs text-text-subtle mb-1">
                      Bandwidth
                    </div>
                    <div className="text-lg font-bold text-purple-400">
                      1.2 GB/s
                    </div>
                  </div>
                  <div className={miniMetric}>
                    <div className="text-xs text-text-subtle mb-1">Uptime</div>
                    <div className="text-lg font-bold text-teal-400">
                      {systemMetrics
                        ? `${Math.floor(systemMetrics.system.uptime / 3600)}h ${Math.floor((systemMetrics.system.uptime % 3600) / 60)}m`
                        : '--'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mb-6">
          {/* Threat Incidents Overview and System Status */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Left column - Threats and Incidents stacked */}
            <div className="space-y-6">
              {/* Live Threat Dashboard Link */}
              <Link className="pb-5" href="/dashboard">
                <Card
                  className={`${cardGlass} hover:elevation-high transition-all duration-300 cursor-pointer hover:border-accent-primary mb-2`}
                >
                  <CardHeader>
                    <h3 className="text-xl font-semibold text-text-primary flex items-center gap-3">
                      🚨 Threat Intelligence Dashboard
                      <Badge
                        variant="success"
                        size="sm"
                        className="animate-pulse glow-red"
                      >
                        Live
                      </Badge>
                    </h3>
                  </CardHeader>
                  <CardContent>
                    <p className="text-text-secondary mb-4">
                      Monitor real-time security threats and incidents from
                      connected data sources.
                    </p>
                    {!loading && threatStats.total > 0 && (
                      <div className="flex items-center gap-4 text-sm mb-4">
                        <span className="text-text-primary font-medium">
                          Total Incidents:{' '}
                          <span className="text-accent-primary">
                            {threatStats.total}
                          </span>
                        </span>
                        {threatStats.critical > 0 && (
                          <Badge variant="danger" size="sm">
                            {threatStats.critical} Critical
                          </Badge>
                        )}
                        {threatStats.critical > 0 && (
                          <Badge variant="high" size="sm">
                            {threatStats.high} High
                          </Badge>
                        )}
                        {threatStats.medium > 0 && (
                          <Badge variant="warning" size="sm">
                            {threatStats.medium} Medium
                          </Badge>
                        )}
                        {threatStats.low > 0 && (
                          <Badge variant="success" size="sm">
                            {threatStats.low} Low
                          </Badge>
                        )}
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-sm text-accent-primary">
                      <span>View Full Dashboard</span>
                      <span>→</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/incidents">
                <Card
                  className={`${cardGlass} hover:elevation-high transition-all duration-300 cursor-pointer hover:border-accent-primary`}
                >
                  <CardHeader>
                    <h3 className="text-xl font-semibold text-text-primary flex items-center gap-3">
                      📋 Incident Management
                      <Badge
                        variant="success"
                        size="sm"
                        className="animate-pulse glow-red"
                      >
                        Live
                      </Badge>
                    </h3>
                  </CardHeader>
                  <CardContent>
                    <p className="text-text-secondary mb-4">
                      Track and manage security incidents with detailed
                      investigation workflows.
                    </p>
                    {!loading && threatStats.total > 0 && (
                      <div className="flex items-center gap-4 text-sm mb-4">
                        <span className="text-text-primary font-medium">
                          Active Cases:{' '}
                          <span className="text-accent-primary">
                            {threatStats.total}
                          </span>
                        </span>
                        {threatStats.critical > 0 && (
                          <Badge variant="danger" size="sm">
                            {threatStats.critical} Critical
                          </Badge>
                        )}
                        {threatStats.high > 0 && (
                          <Badge variant="warning" size="sm">
                            {threatStats.high} High
                          </Badge>
                        )}
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-sm text-accent-primary">
                      <span>View Incident Dashboard</span>
                      <span>→</span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>

            {/* Right column - System Status */}
            <Card className={cardPrimary}>
              <CardHeader>
                <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                  ⚡ System Status
                  <div className="ml-auto text-xs text-text-subtle">
                    Real-time
                  </div>
                </h2>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className={statusItem}>
                  <span className="text-text-secondary font-medium">
                    Firewall
                  </span>
                  <div className="flex items-center gap-3">
                    <Status variant="online" label="Active" />
                    <Badge variant="success" size="sm" className="glow-green">
                      Secure
                    </Badge>
                  </div>
                </div>

                <div className={statusItem}>
                  <span className="text-text-secondary font-medium">
                    Intrusion Detection
                  </span>
                  <div className="flex items-center gap-3">
                    <Status variant="processing" label="Scanning" />
                    <Badge
                      variant="default"
                      size="sm"
                      className="bg-purple-600 text-white"
                    >
                      Running
                    </Badge>
                  </div>
                </div>

                <div className={statusItem}>
                  <span className="text-text-secondary font-medium">
                    VPN Gateway
                  </span>
                  <div className="flex items-center gap-3">
                    <Status variant="warning" label="Limited" />
                    <Badge variant="warning" size="sm">
                      Degraded
                    </Badge>
                  </div>
                </div>

                <div className={statusItem}>
                  <span className="text-text-secondary font-medium">
                    Backup Systems
                  </span>
                  <div className="flex items-center gap-3">
                    <Status variant="error" label="Offline" />
                    <Badge variant="danger" size="sm" className="glow-red">
                      Failed
                    </Badge>
                  </div>
                </div>

                {/* Additional modern status items */}
                <div className="mt-6 space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-text-subtle">Data Encryption</span>
                    <span className="text-cyber-matrix-500 font-medium flex items-center gap-1">
                      <div className="w-1.5 h-1.5 bg-cyber-matrix-500 rounded-full glow-green"></div>
                      AES-256 Active
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-text-subtle">
                      AI Threat Detection
                    </span>
                    <span className="text-purple-400 font-medium flex items-center gap-1">
                      <div className="w-1.5 h-1.5 bg-purple-500 rounded-full glow-purple"></div>
                      ML Monitoring
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-text-subtle">Zero Trust Network</span>
                    <span className="text-teal-400 font-medium flex items-center gap-1">
                      <div className="w-1.5 h-1.5 bg-teal-500 rounded-full"></div>
                      Verified
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Security Alerts - Enhanced with modern styling */}
        <Card className={`${cardGlass} md:col-span-2`}>
          <CardHeader>
            <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
              🚨 Security Alerts
              <div className="ml-auto">
                <Badge
                  variant="default"
                  size="sm"
                  className="bg-surface-secondary text-text-subtle"
                >
                  Live Feed
                </Badge>
              </div>
            </h2>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert
              variant="success"
              className="border-l-4 border-l-cyber-matrix-500"
            >
              <AlertTitle className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyber-matrix-500 rounded-full glow-green"></div>
                System Secure
              </AlertTitle>
              <AlertDescription>
                All security protocols are functioning normally. No threats
                detected in the last 24 hours.
                <div className="text-xs text-text-subtle mt-1">
                  Last scan: {new Date().toLocaleTimeString()}
                </div>
              </AlertDescription>
            </Alert>

            <Alert
              variant="warning"
              className="border-l-4 border-l-cyber-warning-500"
            >
              <AlertTitle className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyber-warning-500 rounded-full animate-pulse"></div>
                Unusual Network Activity
              </AlertTitle>
              <AlertDescription>
                Detected increased traffic from IP range ***********/24.
                Monitoring in progress.
                <div className="flex gap-2 mt-2">
                  <Badge variant="warning" size="sm">
                    Medium Priority
                  </Badge>
                  <Badge
                    variant="default"
                    size="sm"
                    className="bg-surface-tertiary"
                  >
                    Auto-Monitoring
                  </Badge>
                </div>
              </AlertDescription>
            </Alert>

            <Alert
              variant="danger"
              className="border-l-4 border-l-cyber-danger-500"
            >
              <AlertTitle className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyber-danger-500 rounded-full animate-pulse glow-red"></div>
                Critical: Failed Login Attempts
              </AlertTitle>
              <AlertDescription>
                Multiple failed login attempts detected from external IP
                ************. Immediate attention required.
                <div className="flex gap-2 mt-2">
                  <Badge variant="danger" size="sm">
                    High Priority
                  </Badge>
                  <Badge
                    variant="default"
                    size="sm"
                    className="bg-surface-tertiary"
                  >
                    Requires Action
                  </Badge>
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Navigation Section */}
        <div className="mb-12">
          {/*<h2 className="mb-6 text-3xl font-semibold text-text-primary">
            Security Operations Center
          </h2>
          {/*<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Link href="/threats">
              <Card className={`${cardGlass} hover:elevation-high transition-all duration-300 cursor-pointer hover:border-accent-primary`}>
                <CardHeader>
                  <h3 className="text-xl font-semibold text-text-primary flex items-center gap-3">
                    🚨 Threat Intelligence
                    <Badge variant="destructive">Live</Badge>
                  </h3>
                </CardHeader>
                <CardContent>
                  <p className="text-text-secondary mb-4">
                    Monitor real-time security threats and incidents from connected data sources.
                  </p>
                  <div className="flex items-center gap-2 text-sm text-accent-primary">
                    <span>View Dashboard</span>
                    <span>→</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
            
            <Card className={`${cardGlass} opacity-75`}>
              <CardHeader>
                <h3 className="text-xl font-semibold text-text-primary flex items-center gap-3">
                  📊 Analytics
                  <Badge variant="default">Coming Soon</Badge>
                </h3>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary mb-4">
                  Advanced analytics and reporting for security metrics and trends.
                </p>
                <div className="flex items-center gap-2 text-sm text-text-subtle">
                  <span>In Development</span>
                </div>
              </CardContent>
            </Card>
            
            <Card className={`${cardGlass} opacity-75`}>
              <CardHeader>
                <h3 className="text-xl font-semibold text-text-primary flex items-center gap-3">
                  🔍 Investigation
                  <Badge variant="default">Coming Soon</Badge>
                </h3>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary mb-4">
                  Digital forensics and incident investigation workflows.
                </p>
                <div className="flex items-center gap-2 text-sm text-text-subtle">
                  <span>In Development</span>
                </div>
              </CardContent>
            </Card>

            {/*<Link href="/api-modes">
              <Card className={`${cardGlass} hover:elevation-high transition-all duration-300 cursor-pointer hover:border-accent-primary`}>
                <CardHeader>
                  <h3 className="text-xl font-semibold text-text-primary flex items-center gap-3">
                    🔄 API Modes
                    <Badge variant="default">New</Badge>
                  </h3>
                </CardHeader>
                <CardContent>
                  <p className="text-text-secondary mb-4">
                    Compare direct vs proxied API communication approaches and test both modes.
                  </p>
                  <div className="flex items-center gap-2 text-sm text-accent-primary">
                    <span>View Comparison</span>
                    <span>→</span>
                  </div>
                </CardContent>
              </Card>
            </Link>*/}
        </div>
        {/*</div>

        {/* Modern Color Palette Demo - Enhanced with new features */}
        {/*<div className="mb-12">
          <h2 className="mb-6 text-3xl font-semibold text-text-primary">
            Modern Color Palette
          </h2>

          {/* Primary Color Sections */}
        {/*<div className="grid gap-6 md:grid-cols-4 mb-8">
            {/* Primary Colors - Enhanced */}
        {/*<Card className={cardPrimary}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-primary-500 cyber-glow">
                  Primary Blue
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-10 bg-primary-500 rounded-lg flex items-center justify-center text-white text-sm font-medium cyber-glow">
                    #1AA1DC
                  </div>
                  <div className="h-6 bg-primary-400 rounded-md"></div>
                  <div className="h-6 bg-primary-600 rounded-md"></div>
                  <div className="text-xs text-text-subtle">
                    Enhanced vibrancy
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Success/Matrix Green - Updated */}
        {/*<Card className={cardPrimary}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-cyber-matrix-500 glow-green">
                  Success Green
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-10 bg-cyber-matrix-500 rounded-lg flex items-center justify-center text-white text-sm font-medium glow-green">
                    #10b981
                  </div>
                  <div className="h-6 bg-cyber-matrix-400 rounded-md"></div>
                  <div className="h-6 bg-cyber-matrix-600 rounded-md"></div>
                  <div className="text-xs text-text-subtle">Modern emerald</div>
                </div>
              </CardContent>
            </Card>

            {/* Warning - Enhanced */}
        {/*<Card className={cardPrimary}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-cyber-warning-500">
                  Warning Amber
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-10 bg-cyber-warning-500 rounded-lg flex items-center justify-center text-white text-sm font-medium">
                    #f59e0b
                  </div>
                  <div className="h-6 bg-cyber-warning-400 rounded-md"></div>
                  <div className="h-6 bg-cyber-warning-600 rounded-md"></div>
                  <div className="text-xs text-text-subtle">
                    Enhanced warmth
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Danger - Consistent */}
        {/* <Card className={cardPrimary}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-cyber-danger-500 glow-red">
                  Danger Red
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-10 bg-cyber-danger-500 rounded-lg flex items-center justify-center text-white text-sm font-medium glow-red">
                    #ef4444
                  </div>
                  <div className="h-6 bg-cyber-danger-400 rounded-md"></div>
                  <div className="h-6 bg-cyber-danger-600 rounded-md"></div>
                  <div className="text-xs text-text-subtle">
                    Maintained intensity
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* New Modern Accent Colors */}
        {/*<div className="grid gap-6 md:grid-cols-3 mb-8">
            <Card className={cardSecondary}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-text-primary">
                  Modern Accents
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-8 h-8 rounded-full glow-purple"
                      style={{ backgroundColor: '#8b5cf6' }}
                    ></div>
                    <div>
                      <div className="text-sm font-medium text-text-primary">
                        Purple
                      </div>
                      <div className="text-xs text-text-subtle">#8b5cf6</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div
                      className="w-8 h-8 rounded-full"
                      style={{
                        backgroundColor: '#14b8a6',
                        boxShadow: '0 0 16px rgba(20, 184, 166, 0.4)',
                      }}
                    ></div>
                    <div>
                      <div className="text-sm font-medium text-text-primary">
                        Teal
                      </div>
                      <div className="text-xs text-text-subtle">#14b8a6</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div
                      className="w-8 h-8 rounded-full"
                      style={{
                        backgroundColor: '#6366f1',
                        boxShadow: '0 0 16px rgba(99, 102, 241, 0.4)',
                      }}
                    ></div>
                    <div>
                      <div className="text-sm font-medium text-text-primary">
                        Indigo
                      </div>
                      <div className="text-xs text-text-subtle">#6366f1</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className={`${cardSecondary} gradient-cyber-subtle`}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-text-primary">
                  Modern Gradients
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-8 gradient-cyber rounded-lg flex items-center justify-center text-white text-sm font-medium">
                    Cyber Gradient
                  </div>
                  <div className="h-8 gradient-danger rounded-lg flex items-center justify-center text-white text-sm font-medium">
                    Danger Gradient
                  </div>
                  <div className="h-8 gradient-warning rounded-lg flex items-center justify-center text-white text-sm font-medium">
                    Warning Gradient
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className={cardGlassHigh}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-text-primary">
                  Glass Effects
                </h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="glass-effect rounded-lg p-3 text-center text-sm text-text-primary border border-border-muted">
                    Subtle Glass
                  </div>
                  <div className="glass-effect-strong rounded-lg p-3 text-center text-sm text-text-primary border border-border-secondary">
                    Strong Glass
                  </div>
                  <div className="text-xs text-text-subtle text-center">
                    Backdrop blur effects
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Modern Background Surface Examples */}
        {/*<div className="mb-12">
          <h2 className="mb-6 text-3xl font-semibold text-text-primary">
            Enhanced Background System
          </h2>
          <div className="grid gap-6 md:grid-cols-3">
            <Card className={cardPrimary}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-text-primary">
                  Primary Surface
                </h3>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-text-secondary mb-3">
                  Background Primary: #0a0d14
                </div>
                <div className="space-y-2">
                  <div className="p-3 bg-surface-secondary rounded border border-border-muted">
                    <div className="text-xs text-text-subtle">
                      Secondary Surface
                    </div>
                  </div>
                  <div className="p-3 bg-surface-tertiary rounded border border-border-muted">
                    <div className="text-xs text-text-subtle">
                      Tertiary Surface
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className={cardGlass}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-text-primary">
                  Glass Morphism
                </h3>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-text-secondary mb-3">
                  Backdrop blur with transparency
                </div>
                <div className="space-y-2">
                  <div className="glass-effect p-3 rounded border border-border-primary">
                    <div className="text-xs text-text-primary">
                      Subtle glass effect
                    </div>
                  </div>
                  <div className="glass-effect-strong p-3 rounded border border-border-secondary">
                    <div className="text-xs text-text-primary">
                      Strong glass effect
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className={`${cardPrimary} elevation-high`}>
              <CardHeader>
                <h3 className="text-lg font-semibold text-text-primary">
                  Elevation System
                </h3>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-text-secondary mb-3">
                  Multi-tier shadow system
                </div>
                <div className="space-y-2">
                  <div className="elevation-low p-3 bg-surface-secondary rounded">
                    <div className="text-xs text-text-subtle">
                      Low elevation
                    </div>
                  </div>
                  <div className="elevation-medium p-3 bg-surface-secondary rounded">
                    <div className="text-xs text-text-subtle">
                      Medium elevation
                    </div>
                  </div>
                  <div className="elevation-high p-3 bg-surface-secondary rounded">
                    <div className="text-xs text-text-subtle">
                      High elevation
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* User Profile Card - Enhanced with modern styling */}
          <Card className={cardGlass}>
            <CardHeader>
              <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                <div className="w-3 h-3 bg-cyber-matrix-500 rounded-full animate-pulse glow-green"></div>
                Security Administrator
              </h2>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-text-secondary">Name:</span>
                  <span className="text-text-primary">{mockUser.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">Email:</span>
                  <span className="text-text-accent">{mockUser.email}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">Role:</span>
                  <span className="bg-primary-500 text-white px-3 py-1 rounded-lg text-sm font-medium cyber-glow">
                    {mockUser.role}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">Status:</span>
                  <span className="text-cyber-matrix-500 flex items-center gap-2">
                    <div className="w-2 h-2 bg-cyber-matrix-500 rounded-full animate-pulse glow-green"></div>
                    <span className="font-medium">Online</span>
                  </span>
                </div>
                <div className="mt-4 p-3 glass-effect rounded-lg border border-border-muted">
                  <div className="text-xs text-text-subtle mb-1">
                    Last Login
                  </div>
                  <div className="text-sm text-text-primary">{lastLogin}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Actions - Enhanced with modern buttons */}
          <Card className={cardPrimary}>
            <CardHeader>
              <h2 className="text-xl font-semibold text-text-primary">
                Security Operations
              </h2>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                variant="primary"
                className="w-full bg-primary-500 hover:bg-primary-400 text-white transition-all duration-300 hover:shadow-[0_0_20px_rgba(26,161,220,0.4)]"
              >
                🔒 Initiate Security Scan
              </Button>
              <Button
                variant="secondary"
                className="w-full bg-cyber-matrix-500 hover:bg-cyber-matrix-400 text-white transition-all duration-300 hover:shadow-[0_0_20px_rgba(16,185,129,0.4)]"
              >
                ✅ System Status Check
              </Button>
              <Button
                variant="danger"
                className="w-full bg-cyber-danger-500 hover:bg-cyber-danger-400 text-white transition-all duration-300 hover:shadow-[0_0_20px_rgba(239,68,68,0.4)]"
              >
                🚨 Emergency Lockdown
              </Button>
              <Button className="w-full bg-cyber-warning-500 hover:bg-cyber-warning-400 text-white transition-all duration-300 hover:shadow-[0_0_20px_rgba(245,158,11,0.4)]">
                ⚠️ Generate Alert
              </Button>

              {/* Modern accent action buttons */}
              <div className="mt-6 grid grid-cols-3 gap-2">
                <Button className="bg-purple-600 hover:bg-purple-500 text-white text-xs p-2 transition-all duration-300 hover:shadow-[0_0_16px_rgba(139,92,246,0.4)]">
                  Analytics
                </Button>
                <Button className="bg-teal-600 hover:bg-teal-500 text-white text-xs p-2 transition-all duration-300 hover:shadow-[0_0_16px_rgba(20,184,166,0.4)]">
                  Reports
                </Button>
                <Button className="bg-indigo-600 hover:bg-indigo-500 text-white text-xs p-2 transition-all duration-300 hover:shadow-[0_0_16px_rgba(99,102,241,0.4)]">
                  Config
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer - Enhanced */}
        <div className="mt-12 text-center glass-effect rounded-xl p-6 border border-border-muted">
          <div className="flex justify-center items-center gap-4 mb-2">
            <div className="w-2 h-2 bg-cyber-matrix-500 rounded-full animate-pulse glow-green"></div>
            <p className="text-text-secondary">
              INTSOC Security Operations Center
            </p>
            <div className="w-2 h-2 bg-primary-500 rounded-full cyber-glow"></div>
          </div>
          {/*<p className="text-text-subtle text-sm">
            Modern Refined Dark Theme • Enhanced Color Palette • Powered by
            <span className="text-primary-500 font-medium cyber-glow mx-1">
              #1AA1DC
            </span>
            • Glass Morphism & Elevation System
          </p>
          {/*<div className="flex justify-center gap-4 mt-3 text-xs text-text-subtle">
            <span className="flex items-center gap-1">
              <div className="w-1 h-1 bg-cyber-matrix-500 rounded-full"></div>
              Success: #10b981
            </span>
            <span className="flex items-center gap-1">
              <div className="w-1 h-1 bg-cyber-warning-500 rounded-full"></div>
              Warning: #f59e0b
            </span>
            <span className="flex items-center gap-1">
              <div className="w-1 h-1 bg-cyber-danger-500 rounded-full"></div>
              Danger: #ef4444
            </span>
          </div>*/}
        </div>
      </div>
    </main>
  );
}
