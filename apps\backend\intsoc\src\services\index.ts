// New simplified WebSocket services
export { WebSocketConnectionManager } from './WebSocketConnectionManager';
export type {
  ConnectionConfig,
  ConnectionEvents,
} from './WebSocketConnectionManager';

export { ThreatsService } from './ThreatsService';
export type { ThreatsServiceConfig, ThreatsData } from './ThreatsService';

export { MachineLearningService } from './MachineLearningService';
export type {
  MachineLearningServiceConfig,
  MachineLearningData,
} from './MachineLearningService';

// Cache services (using redis package CacheService + local wrapper)
export { ThreatsCache } from './ThreatsCache';
export { MachineLearningCache } from './MachineLearningCache';

export { MessageParser } from '../utils/MessageParser';
export type { ParsedMessage } from '../utils/MessageParser';
