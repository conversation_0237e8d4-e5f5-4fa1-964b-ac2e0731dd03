import { CacheService } from '@telesoft-ui/redis';
import type { RedisConfig, RedisConnectionOptions } from '@telesoft-ui/redis';
import config from '../config';

interface TimeseriesData {
  [timestamp: string]: number;
}

interface PredictionData {
  "1min": TimeseriesData;
  "1min_lower": TimeseriesData;
  "1min_upper": TimeseriesData;
}

interface DeploymentData {
  timeseries: TimeseriesData;
  predictions: PredictionData;
  alerts: number[];
}

interface Deployment {
  data: DeploymentData;
  name: string;
  namespace: string;
  display_name: string;
}

interface MachineLearningData {
  deployments: Deployment[];
  [key: string]: unknown;
}

/**
 * Machine Learning-specific cache wrapper that manages its own CacheService instance
 */
export class MachineLearningCache {
  private cache: CacheService;
  private readonly ML_KEY = 'ml:deployments:latest';

  /**
   * Validate the structure of deployment data
   */
  private validateDeploymentData(deployment: any): deployment is Deployment {
    if (!deployment.name || !deployment.namespace || !deployment.data) {
      return false;
    }

    const data = deployment.data;
    if (!data.timeseries || typeof data.timeseries !== 'object') {
      return false;
    }

    if (!data.predictions || typeof data.predictions !== 'object') {
      return false;
    }

    const predictions = data.predictions;
    if (!predictions['1min'] || typeof predictions['1min'] !== 'object' ||
      !predictions['1min_lower'] || typeof predictions['1min_lower'] !== 'object' ||
      !predictions['1min_upper'] || typeof predictions['1min_upper'] !== 'object') {
      return false;
    }

    if (!Array.isArray(data.alerts)) {
      return false;
    }

    return true;
  }

  /**
   * Safely merge prediction data, handling missing properties
   */
  private mergePredictions(existing: PredictionData, incoming: PredictionData): PredictionData {
    return {
      "1min": {
        ...(existing["1min"] || {}),
        ...(incoming["1min"] || {}),
      },
      "1min_lower": {
        ...(existing["1min_lower"] || {}),
        ...(incoming["1min_lower"] || {}),
      },
      "1min_upper": {
        ...(existing["1min_upper"] || {}),
        ...(incoming["1min_upper"] || {}),
      },
    };
  }

  /**
   * Create a properly structured deployment object
   */
  static createDeployment(
    name: string,
    namespace: string,
    displayName: string,
    timeseries: TimeseriesData = {},
    predictions: Partial<PredictionData> = {},
    alerts: number[] = [],
  ): Deployment {
    return {
      name,
      namespace,
      display_name: displayName,
      data: {
        timeseries,
        predictions: {
          "1min": predictions["1min"] || {},
          "1min_lower": predictions["1min_lower"] || {},
          "1min_upper": predictions["1min_upper"] || {},
        },
        alerts,
      },
    };
  }

  /**
   * Transform old format data to new format
   * Converts { data: { timestamp: value } } to { data: { timeseries: {...}, predictions: {...}, alerts: [] } }
   */
  static transformLegacyData(legacyData: any): MachineLearningData | null {
    try {
      if (!legacyData || typeof legacyData !== 'object') {
        return null;
      }

      let deployments: any[] = [];

      // Handle different input formats
      if (Array.isArray(legacyData)) {
        deployments = legacyData;
      } else if (legacyData.deployments && Array.isArray(legacyData.deployments)) {
        deployments = legacyData.deployments;
      } else if (legacyData.data && Array.isArray(legacyData.data)) {
        deployments = legacyData.data;
      } else if (legacyData.data && legacyData.data.deployments && Array.isArray(legacyData.data.deployments)) {
        deployments = legacyData.data.deployments;
      } else {
        console.warn('MachineLearningCache: Unable to find deployments array in legacy data');
        return null;
      }

      const transformedDeployments: Deployment[] = deployments.map((deployment: any) => {
        // Check if already in new format
        if (deployment.data &&
          deployment.data.timeseries &&
          deployment.data.predictions &&
          deployment.data.alerts !== undefined) {
          return deployment as Deployment;
        }

        // Transform old format to new format
        const timeseries = deployment.data || {};
        const displayName = deployment.display_name || deployment.name || 'Unknown';

        return MachineLearningCache.createDeployment(
          deployment.name || 'unknown',
          deployment.namespace || 'default',
          displayName,
          timeseries, // Old data becomes timeseries
          {}, // Empty predictions for now
          [] // Empty alerts for now
        );
      });

      return {
        deployments: transformedDeployments,
      };
    } catch (error) {
      console.error('MachineLearningCache: Error transforming legacy data:', error);
      return null;
    }
  }

  constructor(connectionOptions?: RedisConnectionOptions) {
    const redisConfig: RedisConfig = {
      host: config.redis.host,
      port: config.redis.port,
      db: config.redis.db,
      password: config.redis.password,
      connectTimeout: config.redis.connectTimeout,
      commandTimeout: config.redis.commandTimeout,
    };
    this.cache = new CacheService(redisConfig, connectionOptions);
  }

  /**
   * Initialize the cache service
   */
  async initialize(): Promise<void> {
    await this.cache.initialize();
  }

  async getMachineLearningData(): Promise<unknown | null> {
    return this.cache.get(this.ML_KEY);
  }

  async storeMachineLearningData(
    data: unknown,
    ttlSeconds: number = 3600,
  ): Promise<boolean> {
    // Try to transform legacy data if needed
    const transformedData = MachineLearningCache.transformLegacyData(data);
    if (transformedData) {
      return this.cache.set(this.ML_KEY, transformedData, ttlSeconds);
    }

    // If transformation failed or data is already in new format, store as-is
    return this.cache.set(this.ML_KEY, data, ttlSeconds);
  }

  /**
   * Append new machine learning data to existing cache, merging deployments and avoiding duplicates.
   * This method is designed for incremental updates (e.g., from WebSocket streams)
   * where new data should be merged with existing data rather than replacing it.
   * Deduplication is performed based on the deployment's id field.
   */
  async appendMachineLearningData(
    newData: unknown,
    ttlSeconds: number = 3600,
  ): Promise<boolean> {
    try {
      // Get existing data
      const existingData = await this.cache.get(this.ML_KEY);

      // Try to transform legacy data if needed
      const transformedNewData = MachineLearningCache.transformLegacyData(newData);
      const dataToUse = transformedNewData || newData;

      // Validate new data structure
      if (!dataToUse || typeof dataToUse !== 'object') {
        console.warn(
          'MachineLearningCache: Invalid new data format for append',
        );
        return false;
      }

      const newDataObj = dataToUse as MachineLearningData;
      if (!newDataObj.deployments || !Array.isArray(newDataObj.deployments)) {
        console.warn(
          'MachineLearningCache: New data missing deployments array',
        );
        return false;
      }

      // Validate deployment structure
      for (const deployment of newDataObj.deployments) {
        if (!this.validateDeploymentData(deployment)) {
          console.warn(
            'MachineLearningCache: Invalid deployment structure',
            deployment,
          );
          return false;
        }
      }

      let mergedData: MachineLearningData;

      if (!existingData) {
        // No existing data, just store the new data
        mergedData = newDataObj;
      } else {
        // Merge with existing data
        const existingDataObj = existingData as MachineLearningData;

        if (
          !existingDataObj.deployments ||
          !Array.isArray(existingDataObj.deployments)
        ) {
          // Existing data is malformed, replace with new data
          mergedData = newDataObj;
        } else {
          // Create a map for deduplication based on name + namespace
          const deploymentsMap = new Map<string, Deployment>();

          // Add existing deployments to map
          existingDataObj.deployments.forEach((deployment: Deployment) => {
            if (deployment.name && deployment.namespace) {
              const key = `${deployment.name}:${deployment.namespace}`;
              deploymentsMap.set(key, deployment);
            }
          });

          // Add new deployments to map (will overwrite duplicates)
          newDataObj.deployments.forEach((deployment: Deployment) => {
            if (deployment.name && deployment.namespace) {
              const key = `${deployment.name}:${deployment.namespace}`;
              deploymentsMap.set(key, deployment);
            }
          });

          // Create merged data with deduplicated deployments
          mergedData = {
            ...existingDataObj,
            ...newDataObj,
            deployments: Array.from(deploymentsMap.values()),
          };
        }
      }

      // Store the merged data
      return await this.cache.set(this.ML_KEY, mergedData, ttlSeconds);
    } catch (error) {
      console.error(
        'MachineLearningCache: Error appending machine learning data:',
        error,
      );
      return false;
    }
  }

  /**
   * Merge new machine learning data with existing cache data.
   * This method merges the data objects (timestamps and values) for deployments
   * with matching name and namespace combinations. New timestamps are added
   * to existing deployments, and new deployments are added to the cache.
   */
  async mergeMachineLearningData(
    newData: unknown,
    ttlSeconds: number = 3600,
  ): Promise<boolean> {
    try {
      // Get existing data
      const existingData = await this.cache.get(this.ML_KEY);

      // Try to transform legacy data if needed
      const transformedNewData = MachineLearningCache.transformLegacyData(newData);
      const dataToUse = transformedNewData || newData;

      // Validate new data structure
      if (!dataToUse || typeof dataToUse !== 'object') {
        console.warn('MachineLearningCache: Invalid new data format for merge');
        return false;
      }

      const newDataObj = dataToUse as MachineLearningData;
      if (!newDataObj.deployments || !Array.isArray(newDataObj.deployments)) {
        console.warn(
          'MachineLearningCache: New data missing deployments array',
        );
        return false;
      }

      // Validate deployment structure
      for (const deployment of newDataObj.deployments) {
        if (!this.validateDeploymentData(deployment)) {
          console.warn(
            'MachineLearningCache: Invalid deployment structure',
            deployment,
          );
          return false;
        }
      }

      let mergedData: MachineLearningData;

      if (!existingData) {
        // No existing data, just store the new data
        mergedData = newDataObj;
      } else {
        // Merge with existing data
        const existingDataObj = existingData as MachineLearningData;

        if (
          !existingDataObj.deployments ||
          !Array.isArray(existingDataObj.deployments)
        ) {
          // Existing data is malformed, replace with new data
          mergedData = newDataObj;
        } else {
          // Create a map for merging deployments based on name + namespace
          const deploymentsMap = new Map<string, Deployment>();

          // Add existing deployments to map
          existingDataObj.deployments.forEach((deployment: Deployment) => {
            if (deployment.name && deployment.namespace) {
              const key = `${deployment.name}:${deployment.namespace}`;
              deploymentsMap.set(key, { ...deployment });
            }
          });

          // Merge new deployments with existing ones
          newDataObj.deployments.forEach((newDeployment: Deployment) => {
            if (newDeployment.name && newDeployment.namespace) {
              const key = `${newDeployment.name}:${newDeployment.namespace}`;
              const existingDeployment = deploymentsMap.get(key);

              if (existingDeployment) {
                // Deep merge the deployment data
                const mergedData: DeploymentData = {
                  // Merge timeseries data
                  timeseries: {
                    ...existingDeployment.data.timeseries,
                    ...newDeployment.data.timeseries,
                  },
                  // Merge predictions data using helper method
                  predictions: this.mergePredictions(
                    existingDeployment.data.predictions,
                    newDeployment.data.predictions,
                  ),
                  // Merge alerts (combine and deduplicate)
                  alerts: Array.from(new Set([
                    ...existingDeployment.data.alerts,
                    ...newDeployment.data.alerts,
                  ])).sort((a, b) => a - b),
                };

                // Update the deployment with merged data
                deploymentsMap.set(key, {
                  ...existingDeployment,
                  ...newDeployment, // This will update display_name if it changed
                  data: mergedData,
                });
              } else {
                // New deployment, add it to the map
                deploymentsMap.set(key, { ...newDeployment });
              }
            }
          });

          // Create merged data with updated deployments
          mergedData = {
            ...existingDataObj,
            ...newDataObj,
            deployments: Array.from(deploymentsMap.values()),
          };
        }
      }

      // Store the merged data
      return await this.cache.set(this.ML_KEY, mergedData, ttlSeconds);
    } catch (error) {
      console.error(
        'MachineLearningCache: Error merging machine learning data:',
        error,
      );
      return false;
    }
  }

  async hasMachineLearningData(): Promise<boolean> {
    return this.cache.exists(this.ML_KEY);
  }

  /**
   * Check if machine learning data exists and is not expired
   * Returns true only if data exists and has not expired
   * Returns false if cache is disconnected, key doesn't exist, or key is expired
   */
  async hasValidMachineLearningData(): Promise<boolean> {
    try {
      // First check if cache is connected
      const connectionStatus = this.cache.getStatus();
      if (connectionStatus !== 'connected') {
        console.log(
          'MachineLearningCache: Cache not connected, cannot check for valid data',
        );
        return false;
      }

      const redisClient = this.cache.getClient();
      const ttl = await redisClient.ttl(this.ML_KEY);
      // TTL > 0 means key exists and has time left
      // TTL = -1 means key exists but has no expiration (shouldn't happen with our setup)
      // TTL = -2 means key doesn't exist
      const hasValidData = ttl > 0 || ttl === -1;

      if (!hasValidData) {
        console.log(
          'MachineLearningCache: No valid cached data (TTL:',
          ttl,
          ')',
        );
      }

      return hasValidData;
    } catch (error) {
      console.error('MachineLearningCache: Error checking TTL:', error);
      return false;
    }
  }

  async clearMachineLearningData(): Promise<boolean> {
    return this.cache.delete(this.ML_KEY);
  }

  /**
   * Remove specific deployments from the cache by name and namespace
   * @param deploymentsToRemove Array of deployments to remove (with name and namespace)
   * @returns Promise<boolean> - Success status
   */
  async removeDeployments(
    deploymentsToRemove: Array<{ name: string; namespace: string }>,
  ): Promise<boolean> {
    try {
      // Get current data
      const currentData =
        (await this.getMachineLearningData()) as MachineLearningData | null;

      if (!currentData || !Array.isArray(currentData.deployments)) {
        console.log('MachineLearningCache: No deployments data to update');
        return true; // Nothing to remove, consider it successful
      }

      // Create a set of identifiers to remove for efficient lookup
      const toRemoveSet = new Set(
        deploymentsToRemove.map((d) => `${d.name}:${d.namespace}`),
      );

      // Filter out deployments that match the removal criteria
      const filteredDeployments = currentData.deployments.filter(
        (deployment) => {
          const identifier = `${deployment.name}:${deployment.namespace}`;
          return !toRemoveSet.has(identifier);
        },
      );

      const removedCount =
        currentData.deployments.length - filteredDeployments.length;
      console.log(
        `MachineLearningCache: Removing ${removedCount} deployment(s) from cache`,
      );

      if (removedCount === 0) {
        console.log(
          'MachineLearningCache: No matching deployments found to remove',
        );
        return true;
      }

      // Update the data with filtered deployments
      const updatedData = {
        ...currentData,
        deployments: filteredDeployments,
      };

      // Get the current TTL to preserve it
      const connectionStatus = this.cache.getStatus();
      if (connectionStatus !== 'connected') {
        console.warn(
          'MachineLearningCache: Cache not connected, cannot update data',
        );
        return false;
      }

      const redisClient = this.cache.getClient();
      const currentTtl = await redisClient.ttl(this.ML_KEY);
      const ttlToUse = currentTtl > 0 ? currentTtl : 3600; // Default to 1 hour if no TTL

      // Store the updated data back to cache
      const stored = await this.storeMachineLearningData(updatedData, ttlToUse);

      if (stored) {
        console.log(
          `MachineLearningCache: Successfully removed ${removedCount} deployment(s) from cache`,
        );
      } else {
        console.error(
          'MachineLearningCache: Failed to store updated data after removal',
        );
      }

      return stored;
    } catch (error) {
      console.error('MachineLearningCache: Error removing deployments:', error);
      return false;
    }
  }

  getConnectionStatus(): string {
    return this.cache.getStatus();
  }

  async healthCheck(): Promise<boolean> {
    return this.cache.healthCheck();
  }

  async getStats() {
    return this.cache.getStats();
  }

  cleanup(): void {
    this.cache.cleanup();
  }


}
