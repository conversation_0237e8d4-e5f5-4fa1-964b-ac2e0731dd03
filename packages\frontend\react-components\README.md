# @telesoft/react-components

Common React components for tabs, status indicators, stats cards, and interactive elements.

## Features

- **TabButton & TabGroup** - Reusable tab navigation components
- **ConnectionStatus** - Connection status indicator with timestamp
- **StatsCard & StatsGrid** - Statistical display components
- **Toggle** - Toggle switch component

## Installation

```bash
pnpm add @telesoft/react-components
```

## Usage

### Tab Navigation

```typescript
import { TabButton, TabGroup } from '@telesoft/react-components';

const MyComponent = () => {
  const [activeTab, setActiveTab] = useState('tab1');

  return (
    <TabGroup
      tabs={[
        { id: 'tab1', label: 'Settings' },
        { id: 'tab2', label: 'Data Sources' },
        { id: 'tab3', label: 'ML Models' }
      ]}
      activeTab={activeTab}
      onTabChange={setActiveTab}
    />
  );
};
```

### Connection Status

```typescript
import { ConnectionStatus } from '@telesoft/react-components';

const MyComponent = () => {
  return (
    <ConnectionStatus
      isConnected={isConnected}
      lastUpdate={lastUpdate}
      label="WebSocket Connection"
      showLastUpdate={true}
    />
  );
};
```

### Stats Display

```typescript
import { StatsCard, StatsGrid } from '@telesoft/react-components';

const MyComponent = () => {
  const stats = [
    { title: 'Active Deployments', value: 12, variant: 'success' },
    { title: 'Critical Alerts', value: 3, variant: 'danger' },
    { title: 'Total Threats', value: 156, variant: 'info' },
    { title: 'System Health', value: '98%', variant: 'success' }
  ];

  return (
    <StatsGrid stats={stats} columns={4} />
  );
};
```

### Toggle Switch

```typescript
import { Toggle } from '@telesoft/react-components';

const MyComponent = () => {
  const [enabled, setEnabled] = useState(false);

  return (
    <Toggle
      isActive={enabled}
      onChange={setEnabled}
      label="Auto-retrain Models"
      description="Automatically retrain with new data"
    />
  );
};
```

## Components

### TabButton

Individual tab button component with active states and hover effects.

**Props:**

- `id` - Unique identifier
- `label` - Tab display text
- `isActive` - Whether tab is currently active
- `onClick` - Click handler
- `disabled` - Whether tab is disabled
- `className` - Additional CSS classes

### TabGroup

Container component for managing multiple tabs.

**Props:**

- `tabs` - Array of tab configurations
- `activeTab` - Currently active tab ID
- `onTabChange` - Tab change handler
- `className` - Additional CSS classes

### ConnectionStatus

Displays connection status with colored indicator and optional timestamp.

**Props:**

- `isConnected` - Connection status
- `lastUpdate` - Last update timestamp
- `label` - Status label text
- `showLastUpdate` - Whether to show timestamp
- `className` - Additional CSS classes

### StatsCard

Individual statistics display card.

**Props:**

- `title` - Statistic title
- `value` - Statistic value (string or number)
- `subtitle` - Optional subtitle
- `variant` - Color variant (default, success, warning, danger, info)
- `className` - Additional CSS classes
- `children` - Optional child content

### StatsGrid

Grid container for multiple statistics cards.

**Props:**

- `stats` - Array of statistic configurations
- `columns` - Number of columns (2, 3, or 4)
- `className` - Additional CSS classes

### Toggle

Toggle switch component with label and description.

**Props:**

- `isActive` - Toggle state
- `onChange` - State change handler
- `label` - Toggle label
- `description` - Optional description text
- `disabled` - Whether toggle is disabled
- `className` - Additional CSS classes
