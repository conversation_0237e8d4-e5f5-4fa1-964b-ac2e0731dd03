import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Input, InputProps } from '../Input';

describe('Input', () => {
  const defaultProps: Partial<InputProps> = {};

  it('renders input element', () => {
    render(<Input {...defaultProps} />);
    const input = screen.getByRole('textbox');
    expect(input).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    render(<Input {...defaultProps} className="custom-class" />);
    const input = screen.getByRole('textbox');
    expect(input).toHaveClass('custom-class');
  });

  describe('label', () => {
    it('renders label when provided', () => {
      render(<Input {...defaultProps} label="Test Label" />);
      const label = screen.getByText('Test Label');
      expect(label).toBeInTheDocument();
    });

    it('associates label with input', () => {
      render(<Input {...defaultProps} label="Test Label" />);
      const input = screen.getByLabelText('Test Label');
      expect(input).toBeInTheDocument();
    });

    it('uses custom id when provided', () => {
      render(<Input {...defaultProps} label="Test Label" id="custom-id" />);
      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('id', 'custom-id');
    });

    it('generates unique id when not provided', () => {
      render(<Input {...defaultProps} label="Test Label" />);
      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('id');
      expect(input.getAttribute('id')).toBeTruthy();
    });
  });

  describe('error state', () => {
    it('displays error message', () => {
      render(<Input {...defaultProps} error="This field is required" />);
      const errorMessage = screen.getByText('This field is required');
      expect(errorMessage).toBeInTheDocument();
      expect(errorMessage).toHaveClass('text-cyber-danger-500');
    });

    it('applies error styles to input', () => {
      render(<Input {...defaultProps} error="Error message" />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('border-cyber-danger-500');
    });

    it('prioritizes error over helper text', () => {
      render(
        <Input
          {...defaultProps}
          error="Error message"
          helperText="Helper text"
        />,
      );

      expect(screen.getByText('Error message')).toBeInTheDocument();
      expect(screen.queryByText('Helper text')).not.toBeInTheDocument();
    });
  });

  describe('helper text', () => {
    it('displays helper text when provided', () => {
      render(<Input {...defaultProps} helperText="This is helper text" />);
      const helperText = screen.getByText('This is helper text');
      expect(helperText).toBeInTheDocument();
      expect(helperText).toHaveClass('text-text-muted');
    });

    it('does not display helper text when error is present', () => {
      render(
        <Input
          {...defaultProps}
          error="Error message"
          helperText="Helper text"
        />,
      );

      expect(screen.queryByText('Helper text')).not.toBeInTheDocument();
    });
  });

  describe('interactions', () => {
    it('handles input changes', async () => {
      const user = userEvent.setup();
      const onChange = jest.fn();
      render(<Input {...defaultProps} onChange={onChange} />);

      const input = screen.getByRole('textbox');
      await user.type(input, 'hello');

      expect(onChange).toHaveBeenCalledTimes(5); // One for each character
      expect(input).toHaveValue('hello');
    });

    it('handles focus and blur events', async () => {
      const user = userEvent.setup();
      const onFocus = jest.fn();
      const onBlur = jest.fn();
      render(<Input {...defaultProps} onFocus={onFocus} onBlur={onBlur} />);

      const input = screen.getByRole('textbox');

      await user.click(input);
      expect(onFocus).toHaveBeenCalledTimes(1);

      await user.tab(); // Move focus away
      expect(onBlur).toHaveBeenCalledTimes(1);
    });
  });

  describe('disabled state', () => {
    it('disables input when disabled prop is true', () => {
      render(<Input {...defaultProps} disabled />);
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('applies disabled styles', () => {
      render(<Input {...defaultProps} disabled />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass(
        'disabled:cursor-not-allowed',
        'disabled:opacity-50',
      );
    });
  });

  describe('accessibility', () => {
    it('forwards ref to input element', () => {
      const ref = React.createRef<HTMLInputElement>();
      render(<Input {...defaultProps} ref={ref} />);

      expect(ref.current).toBeInstanceOf(HTMLInputElement);
      expect(ref.current).toBe(screen.getByRole('textbox'));
    });

    it('supports custom attributes', () => {
      render(
        <Input
          {...defaultProps}
          data-testid="custom-input"
          aria-describedby="description"
        />,
      );

      const input = screen.getByTestId('custom-input');
      expect(input).toHaveAttribute('aria-describedby', 'description');
    });

    it('has focus styles', () => {
      render(<Input {...defaultProps} />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass(
        'focus-visible:outline-none',
        'focus-visible:ring-2',
      );
    });
  });

  describe('input types', () => {
    it('supports different input types', () => {
      render(<Input {...defaultProps} type="email" />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'email');
    });

    it('supports placeholder text', () => {
      render(<Input {...defaultProps} placeholder="Enter text here" />);
      const input = screen.getByPlaceholderText('Enter text here');
      expect(input).toBeInTheDocument();
    });
  });
});
