import React from 'react';
import { getToggleClasses } from '@telesoft/utils';

export interface ToggleProps {
  isActive: boolean;
  onChange: (isActive: boolean) => void;
  label?: string;
  description?: string;
  disabled?: boolean;
  className?: string;
}

export const Toggle: React.FC<ToggleProps> = ({
  isActive,
  onChange,
  label,
  description,
  disabled = false,
  className = '',
}) => {
  const { container, thumb } = getToggleClasses(isActive);

  const handleClick = () => {
    if (!disabled) {
      onChange(!isActive);
    }
  };

  return (
    <div className={`flex items-center justify-between w-full ${className}`}>
      {(label || description) && (
        <div className="flex-1 mr-4">
          {label && (
            <p className="text-sm font-medium text-text-primary">{label}</p>
          )}
          {description && (
            <p className="text-xs text-text-subtle">{description}</p>
          )}
        </div>
      )}
      <button
        onClick={handleClick}
        disabled={disabled}
        className={`${container} ${disabled ? 'opacity-50 cursor-not-allowed' : ''} flex-shrink-0`}
      >
        <span className={thumb} />
      </button>
    </div>
  );
};
