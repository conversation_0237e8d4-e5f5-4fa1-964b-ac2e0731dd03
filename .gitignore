# Dependencies
node_modules/
.npm
.pnpm-debug.log*
.pnpm-store/

# Build outputs
dist/
build/
.next/
out/

# TypeScript incremental compilation
*.tsbuildinfo
.tsbuildinfo

# Environment files
.env
.env*.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Tsup bundled files (temporary build artifacts)
*.bundled*.mjs
*.bundled*.js

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Turbo
.turbo/

# Docusaurus build output
.docusaurus
.cache-loader

dist/
