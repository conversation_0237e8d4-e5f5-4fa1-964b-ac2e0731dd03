'use client';

import { Button } from '@telesoft/ui';
import { TelesoftLogo } from '@telesoft/branding';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { ThemeToggle } from './ThemeToggle';
import { useAppConfig } from '../lib/contexts/config-provider';
import { useState, useRef, useEffect } from 'react';

export function Header() {
  const { config } = useAppConfig();
  const pathname = usePathname();
  const router = useRouter();
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isAlertsDropdownOpen, setIsAlertsDropdownOpen] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const alertsDropdownRef = useRef<HTMLDivElement>(null);

  // Fixed styling for consistently dark header background
  const getNavLinkClass = (path: string) => {
    const isActive = pathname === path;
    return `relative px-3 py-2 font-medium transition-all duration-200 ${
      isActive ? 'text-blue-400' : 'text-gray-300 hover:text-blue-400'
    }`;
  };

  const iconButtonClass = 'h-4 w-4';
  const svgProps = {
    fill: 'none',
    stroke: '#e5e7eb', // Fixed light gray color for dark header
    viewBox: '0 0 24 24',
  };

  // Use white logo variant for consistent dark header
  const logoVariant = 'full-logo-white-blue';

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userDropdownRef.current &&
        !userDropdownRef.current.contains(event.target as Node)
      ) {
        setIsUserDropdownOpen(false);
      }
      if (
        alertsDropdownRef.current &&
        !alertsDropdownRef.current.contains(event.target as Node)
      ) {
        setIsAlertsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle user dropdown toggle
  const toggleUserDropdown = () => {
    setIsUserDropdownOpen(!isUserDropdownOpen);
  };

  // Handle alerts dropdown toggle
  const toggleAlertsDropdown = () => {
    setIsAlertsDropdownOpen(!isAlertsDropdownOpen);
  };

  // Handle dropdown menu actions (placeholders)
  const handleManageUsers = () => {
    console.log('Manage Users clicked');
    setIsUserDropdownOpen(false);
    // TODO: Implement manage users functionality
  };

  const handleLogout = () => {
    console.log('Logout clicked');
    setIsUserDropdownOpen(false);
    // Redirect to login page
    router.push('/login');
  };

  // Generate fictional system stats matching the operations page
  const getSystemStats = () => {
    return {
      networkSecurity: 98.7,
      threatLevel: 'Medium',
      threatValue: 45,
      activeConnections: 1247,
      systemCpu: 23.4,
      processCpu: 5.2,
      memoryUsage: 67.8,
      memoryUsed: 2048,
      memoryTotal: 3024,
      bandwidth: 1.2,
      uptime: { hours: 72, minutes: 45 },
    };
  };

  // Fullscreen functionality
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }
  };

  return (
    <header
      className="border-b fixed top-0 left-0 right-0 z-50"
      style={{
        backgroundColor: '#0F1419',
        borderBottomColor: '#1e293b',
      }}
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo Section */}
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center space-x-3">
              <TelesoftLogo size="medium" variant={logoVariant} />
              <span className="text-xl font-semibold text-white">
                {config?.app.name || 'IntSOC'}
              </span>
            </Link>
          </div>

          {/* Navigation Links */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/dashboard" className={getNavLinkClass('/dashboard')}>
              Dashboard
            </Link>
            <Link href="/incidents" className={getNavLinkClass('/incidents')}>
              Incidents
            </Link>
            <Link
              href="/threat-hunting"
              className={getNavLinkClass('/threat-hunting')}
            >
              AI Threat Hunting
            </Link>
            <Link href="/settings" className={getNavLinkClass('/settings')}>
              Settings
            </Link>
          </nav>

          {/* Context Buttons */}
          <div className="flex items-center space-x-1">
            {/* Theme Toggle Button */}
            <ThemeToggle />

            {/* Fullscreen Toggle Button */}
            <Button
              variant="ghost"
              size="sm"
              className="relative"
              style={{
                color: '#d1d5db',
                backgroundColor: 'transparent',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#ffffff';
                e.currentTarget.style.backgroundColor = '#334155';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#d1d5db';
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
              onClick={toggleFullscreen}
              title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
            >
              <svg className={iconButtonClass} {...svgProps}>
                {isFullscreen ? (
                  // Exit fullscreen icon - all arrows pointing inward
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 3v3M8 6H5M16 3v3M16 6h3M8 21v-3M8 18H5M16 21v-3M16 18h3"
                  />
                ) : (
                  // Enter fullscreen icon - maximize/expand
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 8V5a2 2 0 012-2h3M21 8V5a2 2 0 00-2-2h-3M3 16v3a2 2 0 002 2h3M21 16v3a2 2 0 01-2 2h-3"
                  />
                )}
              </svg>
            </Button>

            {/* Alerts Button with Dropdown */}
            <div className="relative" ref={alertsDropdownRef}>
              <Button
                variant="ghost"
                size="sm"
                className="relative"
                style={{
                  color: '#d1d5db',
                  backgroundColor: 'transparent',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#ffffff';
                  e.currentTarget.style.backgroundColor = '#334155';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#d1d5db';
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onClick={toggleAlertsDropdown}
              >
                <svg className={iconButtonClass} {...svgProps}>
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                  />
                </svg>
                {/* Notification dot */}
                <span className="absolute top-0 right-0 h-2 w-2 bg-cyber-danger-500 rounded-full"></span>
              </Button>

              {/* Alerts Dropdown Menu */}
              {isAlertsDropdownOpen && (
                <div
                  className="absolute right-0 mt-2 w-80 rounded-lg shadow-lg z-50"
                  style={{
                    backgroundColor: '#1e293b',
                    borderColor: '#475569',
                    border: '1px solid',
                  }}
                >
                  <div className="p-4 space-y-4">
                    {/* Primary metrics */}
                    <div className="grid gap-3">
                      <div
                        className="flex items-center justify-between p-3 rounded-lg"
                        style={{ backgroundColor: '#334155' }}
                      >
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-cyber-matrix-500 rounded-full"></div>
                          <span className="text-sm text-gray-300">
                            Network Security
                          </span>
                        </div>
                        <span className="text-sm font-semibold text-cyber-matrix-500">
                          {getSystemStats().networkSecurity}%
                        </span>
                      </div>

                      <div
                        className="flex items-center justify-between p-3 rounded-lg"
                        style={{ backgroundColor: '#334155' }}
                      >
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-cyber-warning-500 rounded-full animate-pulse"></div>
                          <span className="text-sm text-gray-300">
                            Threat Level
                          </span>
                        </div>
                        <span className="text-sm font-semibold text-cyber-warning-500">
                          {getSystemStats().threatLevel}
                        </span>
                      </div>

                      <div
                        className="flex items-center justify-between p-3 rounded-lg"
                        style={{ backgroundColor: '#334155' }}
                      >
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                          <span className="text-sm text-gray-300">
                            Active Connections
                          </span>
                        </div>
                        <span className="text-sm font-semibold text-primary-500">
                          {getSystemStats().activeConnections.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    {/* Mini metrics */}
                    <div className="grid grid-cols-2 gap-2">
                      <div
                        className="p-2 rounded"
                        style={{ backgroundColor: '#334155' }}
                      >
                        <div className="text-xs text-gray-400">CPU</div>
                        <div className="text-sm font-semibold text-primary-400">
                          {getSystemStats().systemCpu}%
                        </div>
                      </div>
                      <div
                        className="p-2 rounded"
                        style={{ backgroundColor: '#334155' }}
                      >
                        <div className="text-xs text-gray-400">Memory</div>
                        <div className="text-sm font-semibold text-cyber-matrix-400">
                          {getSystemStats().memoryUsage}%
                        </div>
                      </div>
                      <div
                        className="p-2 rounded"
                        style={{ backgroundColor: '#334155' }}
                      >
                        <div className="text-xs text-gray-400">Bandwidth</div>
                        <div className="text-sm font-semibold text-purple-400">
                          {getSystemStats().bandwidth} GB/s
                        </div>
                      </div>
                      <div
                        className="p-2 rounded"
                        style={{ backgroundColor: '#334155' }}
                      >
                        <div className="text-xs text-gray-400">Uptime</div>
                        <div className="text-sm font-semibold text-teal-400">
                          {getSystemStats().uptime.hours}h{' '}
                          {getSystemStats().uptime.minutes}m
                        </div>
                      </div>
                    </div>

                    {/* Link to operations page */}
                    <div
                      className="pt-3"
                      style={{
                        borderTopColor: '#475569',
                        borderTopWidth: '1px',
                      }}
                    >
                      <Link
                        href="/operations"
                        className="flex items-center justify-center w-full px-4 py-2 text-sm text-blue-400 rounded-md transition-colors duration-150"
                        style={{
                          color: '#60a5fa',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.color = '#93c5fd';
                          e.currentTarget.style.backgroundColor = '#334155';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.color = '#60a5fa';
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                        onClick={() => setIsAlertsDropdownOpen(false)}
                      >
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="#e5e7eb"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                          />
                        </svg>
                        View Full Operations Dashboard
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Settings Button */}
            <Button
              variant="ghost"
              size="sm"
              style={{
                color: '#d1d5db',
                backgroundColor: 'transparent',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#ffffff';
                e.currentTarget.style.backgroundColor = '#334155';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#d1d5db';
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg className={iconButtonClass} {...svgProps}>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </Button>

            {/* User Profile Button with Dropdown */}
            <div className="relative" ref={userDropdownRef}>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center space-x-2"
                style={{
                  color: '#d1d5db',
                  backgroundColor: 'transparent',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#ffffff';
                  e.currentTarget.style.backgroundColor = '#334155';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#d1d5db';
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onClick={toggleUserDropdown}
              >
                <svg className={iconButtonClass} {...svgProps}>
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <span className="hidden lg:block text-sm text-gray-300">
                  Admin
                </span>
                {/* Dropdown Arrow */}
                <svg
                  className={`w-4 h-4 transition-transform duration-200 ${isUserDropdownOpen ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="#e5e7eb"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </Button>

              {/* Dropdown Menu */}
              {isUserDropdownOpen && (
                <div
                  className="absolute right-0 mt-2 w-48 rounded-lg shadow-lg z-50"
                  style={{
                    backgroundColor: '#1e293b',
                    borderColor: '#475569',
                    border: '1px solid',
                  }}
                >
                  <div className="py-1">
                    <button
                      onClick={handleManageUsers}
                      className="flex items-center w-full px-4 py-2 text-sm transition-colors duration-150"
                      style={{ color: '#e5e7eb' }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#334155';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <svg
                        className="w-4 h-4 mr-3"
                        fill="none"
                        stroke="#e5e7eb"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
                        />
                      </svg>
                      Manage Users
                    </button>
                    <hr style={{ borderColor: '#475569', margin: '4px 0' }} />
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm transition-colors duration-150"
                      style={{ color: '#e5e7eb' }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#334155';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <svg
                        className="w-4 h-4 mr-3"
                        fill="none"
                        stroke="#e5e7eb"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                        />
                      </svg>
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              style={{
                color: '#d1d5db',
                backgroundColor: 'transparent',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#ffffff';
                e.currentTarget.style.backgroundColor = '#334155';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#d1d5db';
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg className={iconButtonClass} {...svgProps}>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
