import Redis from 'ioredis';
import { RedisConfig, RedisConnectionOptions } from './types';

export class RedisClient {
  private client: Redis;
  private config: RedisConfig;
  private options: RedisConnectionOptions;

  constructor(config: RedisConfig, options: RedisConnectionOptions = {}) {
    this.config = config;
    this.options = options;
    this.client = this.createClient();
    this.setupEventHandlers();
  }

  private createClient(): Redis {
    /*return new Redis({
      host: this.config.host,
      port: this.config.port,
      username: 'default',
      password: this.config.password,
      db: this.config.db ?? 0,
      enableReadyCheck: this.config.enableReadyCheck ?? true,
      maxRetriesPerRequest: this.config.maxRetriesPerRequest ?? 3,
      lazyConnect: this.config.lazyConnect ?? false,
      connectTimeout: this.config.connectTimeout ?? 10000,
      commandTimeout: this.config.commandTimeout ?? 5000,
      family: this.config.family ?? 4,
      keepAlive: this.config.keepAlive ?? 30000,
      keyPrefix: this.config.keyPrefix,
    }); */
    const connection = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
    };

    return new Redis(connection);
  }

  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      console.log('Redis client connected');
      this.options.onConnect?.();
    });

    this.client.on('error', (error: Error) => {
      console.error('Redis client error:', error);
      this.options.onError?.(error);
    });

    this.client.on('reconnecting', () => {
      console.log('Redis client reconnecting');
      this.options.onReconnecting?.();
    });

    this.client.on('close', () => {
      console.log('Redis client connection closed');
      this.options.onClose?.();
    });
  }

  /**
   * Connect to Redis server
   */
  async connect(): Promise<void> {
    try {
      await this.client.connect();
    } catch (error) {
      throw new Error(
        `Failed to connect to Redis: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Disconnect from Redis server
   */
  async disconnect(): Promise<void> {
    await this.client.quit();
  }

  /**
   * Check if the client is connected
   */
  isConnected(): boolean {
    return this.client.status === 'ready';
  }

  /**
   * Get a value by key
   */
  async get<T = string>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      if (value === null) {
        return null;
      }

      // Try to parse as JSON, fallback to string
      try {
        return JSON.parse(value) as T;
      } catch {
        return value as T;
      }
    } catch (error) {
      throw new Error(
        `Failed to get key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Set a value by key
   */
  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    try {
      const stringValue =
        typeof value === 'string' ? value : JSON.stringify(value);

      if (ttlSeconds) {
        await this.client.setex(key, ttlSeconds, stringValue);
      } else {
        await this.client.set(key, stringValue);
      }
    } catch (error) {
      throw new Error(
        `Failed to set key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Delete a key
   */
  async delete(key: string): Promise<boolean> {
    try {
      const result = await this.client.del(key);
      return result > 0;
    } catch (error) {
      throw new Error(
        `Failed to delete key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Delete multiple keys
   */
  async deleteMany(keys: string[]): Promise<number> {
    try {
      if (keys.length === 0) {
        return 0;
      }
      return await this.client.del(...keys);
    } catch (error) {
      throw new Error(
        `Failed to delete keys: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Check if a key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      throw new Error(
        `Failed to check existence of key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Set expiration time for a key
   */
  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, ttlSeconds);
      return result === 1;
    } catch (error) {
      throw new Error(
        `Failed to set expiration for key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get time to live for a key
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      throw new Error(
        `Failed to get TTL for key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get all keys matching a pattern
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      throw new Error(
        `Failed to get keys with pattern "${pattern}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Increment a numeric value
   */
  async increment(key: string, amount: number = 1): Promise<number> {
    try {
      if (amount === 1) {
        return await this.client.incr(key);
      } else {
        return await this.client.incrby(key, amount);
      }
    } catch (error) {
      throw new Error(
        `Failed to increment key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Decrement a numeric value
   */
  async decrement(key: string, amount: number = 1): Promise<number> {
    try {
      if (amount === 1) {
        return await this.client.decr(key);
      } else {
        return await this.client.decrby(key, amount);
      }
    } catch (error) {
      throw new Error(
        `Failed to decrement key "${key}": ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get multiple values by keys
   */
  async getMany<T = string>(keys: string[]): Promise<(T | null)[]> {
    try {
      if (keys.length === 0) {
        return [];
      }

      const values = await this.client.mget(...keys);
      return values.map((value: string | null) => {
        if (value === null) {
          return null;
        }

        try {
          return JSON.parse(value) as T;
        } catch {
          return value as T;
        }
      });
    } catch (error) {
      throw new Error(
        `Failed to get multiple keys: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Set multiple key-value pairs
   */
  async setMany<T>(keyValuePairs: Record<string, T>): Promise<void> {
    try {
      const pipeline = this.client.pipeline();

      Object.entries(keyValuePairs).forEach(([key, value]) => {
        const stringValue =
          typeof value === 'string' ? value : JSON.stringify(value);
        pipeline.set(key, stringValue);
      });

      await pipeline.exec();
    } catch (error) {
      throw new Error(
        `Failed to set multiple keys: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Flush all data from the current database
   */
  async flushDb(): Promise<void> {
    try {
      await this.client.flushdb();
    } catch (error) {
      throw new Error(
        `Failed to flush database: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get the underlying ioredis client for advanced operations
   */
  getClient(): Redis {
    return this.client;
  }
}
