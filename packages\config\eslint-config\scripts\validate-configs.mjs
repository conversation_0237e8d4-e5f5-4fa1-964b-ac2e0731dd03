#!/usr/bin/env node

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

/**
 * Validates ESLint configuration files
 */

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const configDir = dirname(__dirname);

console.log('🔍 Validating ESLint configurations...\n');

const configs = [
  { file: 'index.mjs', name: 'Base ESLint Configuration' },
  { file: 'react-internal.mjs', name: 'React Internal Configuration' },
  { file: 'next.mjs', name: 'Next.js Configuration' },
];

async function validateConfigs() {
  let hasErrors = false;

  for (const { file, name } of configs) {
    console.log(`Validating ${name} (${file}):`);

    try {
      // Import the ESLint config to validate it
      const configPath = join(configDir, file);
      const configUrl = `file://${configPath}`;
      const config = await import(configUrl);

      console.log(`  ✓ Successfully imported as ES module`);

      // Validate config structure
      if (config.default) {
        const eslintConfig = config.default;

        if (Array.isArray(eslintConfig)) {
          console.log(
            `  ✓ Flat config format with ${eslintConfig.length} configuration objects`,
          );

          // Check each config object
          eslintConfig.forEach((configObj, index) => {
            if (configObj.files) {
              console.log(
                `  ✓ Config ${index + 1} has file patterns: ${configObj.files.join(', ')}`,
              );
            }
            if (configObj.languageOptions) {
              console.log(`  ✓ Config ${index + 1} has language options`);
            }
            if (configObj.plugins) {
              const pluginCount = Object.keys(configObj.plugins).length;
              console.log(`  ✓ Config ${index + 1} has ${pluginCount} plugins`);
            }
            if (configObj.rules) {
              const ruleCount = Object.keys(configObj.rules).length;
              console.log(`  ✓ Config ${index + 1} has ${ruleCount} rules`);
            }
          });
        } else if (typeof eslintConfig === 'object') {
          console.log(`  ✓ Single config object format`);
          if (eslintConfig.files) {
            console.log(
              `  ✓ Has file patterns: ${eslintConfig.files.join(', ')}`,
            );
          }
          if (eslintConfig.languageOptions) {
            console.log(`  ✓ Has language options`);
          }
          if (eslintConfig.plugins) {
            const pluginCount = Object.keys(eslintConfig.plugins).length;
            console.log(`  ✓ Has ${pluginCount} plugins`);
          }
          if (eslintConfig.rules) {
            const ruleCount = Object.keys(eslintConfig.rules).length;
            console.log(`  ✓ Has ${ruleCount} rules`);
          }
        } else {
          console.log(`  ✓ Config format detected`);
        }
      } else {
        console.error(`  ✗ No default export found`);
        hasErrors = true;
      }

      console.log(`  ✅ ${name} is valid\n`);
    } catch (error) {
      console.error(`  ✗ Validation failed: ${error.message}\n`);
      hasErrors = true;
    }
  }

  if (hasErrors) {
    console.error('❌ Some ESLint configurations have errors');
    process.exit(1);
  } else {
    console.log('✅ All ESLint configurations are valid');
  }
}

// Run the validation
validateConfigs().catch((error) => {
  console.error('Fatal error during validation:', error);
  process.exit(1);
});
