#!/bin/bash

# Start Next.js production server with proper port handling

# Load environment variables from .env.production.local if it exists
if [ -f ".env.production.local" ]; then
    echo "📋 Loading environment from .env.production.local..."
    set -a
    source .env.production.local
    set +a
fi

# Also check for .env.local as fallback
if [ -f ".env.local" ] && [ ! -f ".env.production.local" ]; then
    echo "📋 Loading environment from .env.local..."
    set -a
    source .env.local
    set +a
fi

# Get port from environment variable or default to 3000
PORT=${NEXT_PORT:-3000}

echo "🚀 Starting production server on port $PORT..."

# Start Next.js production server
next start -p "$PORT"
