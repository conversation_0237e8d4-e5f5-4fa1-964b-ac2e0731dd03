import { useState, useEffect, useRef, useCallback } from 'react';
import {
  systemMetricsService,
  SystemMetrics,
  SystemMetricsWebSocketMessage,
} from '../services/systemMetrics';
import { useAppConfig } from '../contexts/config-provider';

export interface UseSystemMetricsOptions {
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectInterval?: number; // Maximum interval between reconnection attempts
}

export interface UseSystemMetricsReturn {
  metrics: SystemMetrics | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connect: () => void;
  disconnect: () => void;
  lastUpdate: string | null;
}

export function useSystemMetrics(
  options: UseSystemMetricsOptions = {},
): UseSystemMetricsReturn {
  const { autoConnect = true } = options;

  // Get configuration state
  const { config, loading: configLoading } = useAppConfig();

  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const isConnectingRef = useRef(false);

  const cleanup = useCallback(() => {
    systemMetricsService.disconnectWebSocket();
    wsRef.current = null;
    setIsConnected(false);
    setIsConnecting(false);
    isConnectingRef.current = false;
  }, []);

  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback(
    (message: SystemMetricsWebSocketMessage) => {
      if (message.type === 'system-metrics' && message.data) {
        setMetrics(message.data);
        setLastUpdate(new Date().toISOString());
        setError(null);
      }
    },
    [],
  );

  const connect = useCallback(() => {
    if (systemMetricsService.isWebSocketConnected()) {
      return; // Already connected
    }

    if (isConnectingRef.current) {
      return; // Connection in progress
    }

    try {
      setIsConnecting(true);
      isConnectingRef.current = true;
      setError(null);

      const ws = systemMetricsService.connectWebSocket(
        handleWebSocketMessage,
        (_error) => {
          setError('WebSocket connection error');
          setIsConnecting(false);
          isConnectingRef.current = false;
        },
        (_event) => {
          setIsConnected(false);
          setIsConnecting(false);
          isConnectingRef.current = false;

          // Note: Reconnection is now handled automatically by systemMetricsService
          // Just log the disconnect
          console.log(
            'System Metrics WebSocket disconnected, service will handle reconnection',
          );
        },
        () => {
          setIsConnected(true);
          setIsConnecting(false);
          isConnectingRef.current = false;
          setError(null);
        },
      );

      wsRef.current = ws;
    } catch (err) {
      console.error('Error creating system metrics WebSocket:', err);
      setError('Failed to create WebSocket connection');
      setIsConnecting(false);
      isConnectingRef.current = false;
    }
  }, [handleWebSocketMessage]);

  const disconnect = useCallback(() => {
    cleanup();
  }, [cleanup]);

  // Auto-connect on mount if enabled, but wait for configuration to be loaded
  useEffect(() => {
    let isMounted = true;

    // Only connect when configuration is loaded and autoConnect is enabled
    if (autoConnect && !configLoading && config && isMounted) {
      console.log('[useSystemMetrics] Configuration loaded, connecting...', {
        wsUrl: config.api.wsUrl,
        useProxy: config.api.useProxy,
      });
      connect();
    }

    return () => {
      isMounted = false;
      cleanup();
    };
  }, [autoConnect, configLoading, config, connect, cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    metrics,
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    lastUpdate,
  };
}
