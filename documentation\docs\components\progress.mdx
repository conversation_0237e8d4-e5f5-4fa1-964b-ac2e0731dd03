---
sidebar_position: 6
---

# Progress

The Progress component displays the completion progress of a task, typically through a visual progress bar. It supports different variants, sizes, and can show labels and percentage values.

import { Progress } from '@telesoft/ui';
import { ComponentShowcase } from '../../src/components/ComponentShowcase';
import { UIThemeProvider } from '../../src/components/UIThemeProvider';

<UIThemeProvider>

## Basic Usage

Progress bars show completion status of tasks, processes, or forms.

<ComponentShowcase
  title="Basic Progress Bar"
  description="Simple progress bar showing task completion."
  component={<Progress value={65} max={100} />}
  code={`<Progress value={65} max={100} />`}
/>

## Variants

Progress components come in different variants to indicate different types of progress or status.

<ComponentShowcase
  title="Default Progress"
  description="Standard progress bar for general use cases."
  component={<Progress value={45} variant="default" />}
  code={`<Progress value={45} variant="default" />`}
/>

<ComponentShowcase
  title="Success Progress"
  description="Green progress bar for completed or successful operations."
  component={<Progress value={100} variant="success" />}
  code={`<Progress value={100} variant="success" />`}
/>

<ComponentShowcase
  title="Warning Progress"
  description="Yellow progress bar for caution or attention-needed states."
  component={<Progress value={85} variant="warning" />}
  code={`<Progress value={85} variant="warning" />`}
/>

<ComponentShowcase
  title="Danger Progress"
  description="Red progress bar for errors or critical states."
  component={<Progress value={25} variant="danger" />}
  code={`<Progress value={25} variant="danger" />`}
/>

## Sizes

Progress bars come in different sizes to fit various UI contexts.

<ComponentShowcase
  title="Progress Sizes"
  description="Different sizes for various use cases and layouts."
  component={
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '1.5rem',
        width: '100%',
      }}
    >
      <div>
        <div
          style={{
            marginBottom: '0.5rem',
            fontSize: '0.875rem',
            color: '#64748b',
          }}
        >
          Small
        </div>
        <Progress value={60} size="sm" />
      </div>
      <div>
        <div
          style={{
            marginBottom: '0.5rem',
            fontSize: '0.875rem',
            color: '#64748b',
          }}
        >
          Medium (Default)
        </div>
        <Progress value={60} size="md" />
      </div>
      <div>
        <div
          style={{
            marginBottom: '0.5rem',
            fontSize: '0.875rem',
            color: '#64748b',
          }}
        >
          Large
        </div>
        <Progress value={60} size="lg" />
      </div>
    </div>
  }
  code={`<Progress value={60} size="sm" />
<Progress value={60} size="md" />
<Progress value={60} size="lg" />`}
/>

## With Labels and Values

Progress bars can display labels and percentage values for better user understanding.

<ComponentShowcase
  title="Progress with Label"
  description="Progress bar with a descriptive label."
  component={<Progress value={75} label="Installation Progress" />}
  code={`<Progress value={75} label="Installation Progress" />`}
/>

<ComponentShowcase
  title="Progress with Value Display"
  description="Progress bar showing the percentage completion."
  component={<Progress value={42} showValue />}
  code={`<Progress value={42} showValue />`}
/>

<ComponentShowcase
  title="Progress with Label and Value"
  description="Progress bar with both label and percentage display."
  component={<Progress value={68} label="Upload Progress" showValue />}
  code={`<Progress value={68} label="Upload Progress" showValue />`}
/>

## Real-World Examples

<ComponentShowcase
  title="File Upload Progress"
  description="Progress bar for file upload operations."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Progress
        value={0}
        label="Preparing upload..."
        variant="default"
        showValue
      />
      <Progress
        value={35}
        label="Uploading document.pdf"
        variant="default"
        showValue
      />
      <Progress
        value={100}
        label="Upload complete!"
        variant="success"
        showValue
      />
    </div>
  }
  code={`<Progress value={0} label="Preparing upload..." variant="default" showValue />
<Progress value={35} label="Uploading document.pdf" variant="default" showValue />
<Progress value={100} label="Upload complete!" variant="success" showValue />`}
/>

<ComponentShowcase
  title="Form Completion Progress"
  description="Progress showing form completion status."
  component={<Progress value={66} label="Profile Setup" showValue size="lg" />}
  code={`<Progress value={66} label="Profile Setup" showValue size="lg" />`}
/>

<ComponentShowcase
  title="System Resource Usage"
  description="Progress bars showing system resource consumption."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Progress value={45} label="CPU Usage" showValue variant="default" />
      <Progress value={78} label="Memory Usage" showValue variant="warning" />
      <Progress value={92} label="Disk Space" showValue variant="danger" />
    </div>
  }
  code={`<Progress value={45} label="CPU Usage" showValue variant="default" />
<Progress value={78} label="Memory Usage" showValue variant="warning" />
<Progress value={92} label="Disk Space" showValue variant="danger" />`}
/>

<ComponentShowcase
  title="Multi-step Process"
  description="Progress showing completion of a multi-step process."
  component={
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Progress
        value={25}
        label="Step 1: Account Setup"
        showValue
        variant="success"
      />
      <Progress
        value={50}
        label="Step 2: Profile Information"
        showValue
        variant="success"
      />
      <Progress
        value={75}
        label="Step 3: Preferences"
        showValue
        variant="default"
      />
      <Progress
        value={0}
        label="Step 4: Verification"
        showValue
        variant="default"
      />
    </div>
  }
  code={`<Progress value={25} label="Step 1: Account Setup" showValue variant="success" />
<Progress value={50} label="Step 2: Profile Information" showValue variant="success" />
<Progress value={75} label="Step 3: Preferences" showValue variant="default" />
<Progress value={0} label="Step 4: Verification" showValue variant="default" />`}
/>

## Layout Examples

<ComponentShowcase
  title="Compact Progress Indicators"
  description="Small progress bars for dashboard widgets or compact layouts."
  component={
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: '1rem',
        maxWidth: '400px',
      }}
    >
      <div>
        <Progress
          value={85}
          label="Downloads"
          showValue
          size="sm"
          variant="success"
        />
      </div>
      <div>
        <Progress
          value={62}
          label="Uploads"
          showValue
          size="sm"
          variant="default"
        />
      </div>
      <div>
        <Progress
          value={40}
          label="Processing"
          showValue
          size="sm"
          variant="warning"
        />
      </div>
      <div>
        <Progress
          value={15}
          label="Queue"
          showValue
          size="sm"
          variant="danger"
        />
      </div>
    </div>
  }
  code={`<div className="grid grid-cols-2 gap-4">
  <Progress value={85} label="Downloads" showValue size="sm" variant="success" />
  <Progress value={62} label="Uploads" showValue size="sm" variant="default" />
  <Progress value={40} label="Processing" showValue size="sm" variant="warning" />
  <Progress value={15} label="Queue" showValue size="sm" variant="danger" />
</div>`}
/>

</UIThemeProvider>

## Props

### Progress Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'value',
      type: 'number',
      default: '0',
      description: 'Current progress value',
    },
    {
      name: 'max',
      type: 'number',
      default: '100',
      description: 'Maximum progress value',
    },
    {
      name: 'variant',
      type: "'default' | 'success' | 'warning' | 'danger'",
      default: "'default'",
      description: 'Visual style variant of the progress bar',
    },
    {
      name: 'size',
      type: "'sm' | 'md' | 'lg'",
      default: "'md'",
      description: 'Size of the progress bar',
    },
    {
      name: 'showValue',
      type: 'boolean',
      default: 'false',
      description: 'Whether to display the percentage value',
    },
    {
      name: 'label',
      type: 'string',
      description: 'Optional label to display above the progress bar',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
  ]}
/>

## Accessibility

The Progress component follows accessibility best practices:

- **ARIA attributes**: Uses `role="progressbar"`, `aria-valuenow`, `aria-valuemin`, and `aria-valuemax`
- **Screen reader support**: Progress state is announced to screen readers
- **Semantic structure**: Uses appropriate HTML elements and structure
- **Visual indicators**: Clear visual representation of progress state
- **Color independence**: Doesn't rely solely on color to convey progress

## Best Practices

### Do ✅

- Use appropriate variants to match the progress context
- Provide clear labels for what the progress represents
- Show percentage values when precision is important
- Use consistent sizing within the same interface section
- Update progress smoothly with transitions
- Provide feedback when progress is complete

### Don't ❌

- Don't use progress bars for indeterminate loading states
- Don't make progress bars too small to be clearly visible
- Don't rely solely on color to indicate different states
- Don't forget to handle edge cases (0%, 100%)
- Don't use progress bars for decorative purposes
- Don't make progress labels too verbose

## Common Patterns

### Animated Progress Updates

```tsx
const [progress, setProgress] = useState(0);

useEffect(() => {
  const timer = setInterval(() => {
    setProgress((prev) => {
      if (prev >= 100) {
        clearInterval(timer);
        return 100;
      }
      return prev + 1;
    });
  }, 100);

  return () => clearInterval(timer);
}, []);

return <Progress value={progress} showValue />;
```

### Step-based Progress

```tsx
const totalSteps = 4;
const currentStep = 2;
const progress = (currentStep / totalSteps) * 100;

return (
  <Progress
    value={progress}
    label={`Step ${currentStep} of ${totalSteps}`}
    showValue
  />
);
```

### Resource Usage Monitor

```tsx
const getVariant = (usage: number) => {
  if (usage >= 90) return 'danger';
  if (usage >= 70) return 'warning';
  if (usage >= 100) return 'success';
  return 'default';
};

return (
  <Progress
    value={cpuUsage}
    variant={getVariant(cpuUsage)}
    label="CPU Usage"
    showValue
  />
);
```
