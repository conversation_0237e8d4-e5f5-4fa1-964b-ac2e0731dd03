import helmet, { HelmetOptions } from 'helmet';

/**
 * Content Security Policy directives optimized for API servers
 * API servers typically don't serve HTML/CSS/JS, so CSP can be more restrictive
 */
export const apiCspDirectives = {
  // Default source - very restrictive for APIs
  defaultSrc: ["'none'"],

  // APIs typically don't serve scripts, so block all
  scriptSrc: ["'none'"],

  // APIs typically don't serve styles, so block all
  styleSrc: ["'none'"],

  // APIs typically don't serve fonts, so block all
  fontSrc: ["'none'"],

  // APIs might return images in responses or serve static assets
  imgSrc: [
    "'self'",
    'data:', // For base64 images in API responses
  ],

  // Connect sources - allow API calls to self and trusted external APIs
  connectSrc: [
    "'self'",
    // Add your external API endpoints here
    // "https://api.external-service.com",
  ],

  // Frame sources - APIs shouldn't be framed
  frameSrc: ["'none'"],

  // Object sources - not needed for APIs
  objectSrc: ["'none'"],

  // Media sources - only if API serves media files
  mediaSrc: ["'self'"],

  // Worker sources - not typically needed for APIs
  workerSrc: ["'none'"],

  // Child sources - deprecated but set to none
  childSrc: ["'none'"],

  // Frame ancestors - prevent API from being framed
  frameAncestors: ["'none'"],

  // Form action - restrict form submissions (if any)
  formAction: ["'self'"],

  // Base URI - restrict base element usage
  baseUri: ["'self'"],

  // Manifest source - not needed for APIs
  manifestSrc: ["'none'"],

  // Upgrade insecure requests - good for security
  upgradeInsecureRequests: [],
};

/**
 * Development CSP directives for API servers
 * Slightly more permissive for development tools and debugging
 */
export const apiCspDirectivesDev = {
  ...apiCspDirectives,
  connectSrc: [
    "'self'",
    'localhost:*', // For development servers
    '127.0.0.1:*', // For local development
    'ws:', // WebSocket connections for dev tools
    'wss:', // Secure WebSocket connections
    // Add your external API endpoints here
  ],
  // Allow some basic scripts for development tools if needed
  scriptSrc: [
    "'self'",
    "'unsafe-inline'", // Only for development debugging
  ],
};

/**
 * Helmet configuration optimized for API servers
 * Focuses on headers relevant for JSON/data APIs
 */
export const apiHelmetConfig = (
  isDevelopment: boolean = false,
): HelmetOptions => ({
  // Content Security Policy - very restrictive for APIs
  contentSecurityPolicy: {
    directives: isDevelopment ? apiCspDirectivesDev : apiCspDirectives,
    reportOnly: isDevelopment, // Only report violations in development
  },

  // Cross-Origin Embedder Policy - can be more relaxed for APIs
  crossOriginEmbedderPolicy: false, // Disable for APIs as it can interfere with CORS

  // Cross-Origin Opener Policy - not typically relevant for APIs
  crossOriginOpenerPolicy: false,

  // Cross-Origin Resource Policy - important for APIs
  crossOriginResourcePolicy: {
    policy: 'cross-origin', // Allow cross-origin requests for APIs
  },

  // DNS Prefetch Control - good security practice
  dnsPrefetchControl: {
    allow: false,
  },

  // Frameguard - prevent API responses from being framed
  frameguard: {
    action: 'deny',
  },

  // Hide Powered By header - good security practice
  hidePoweredBy: true,

  // HTTP Strict Transport Security - essential for APIs
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },

  // IE No Open - legacy but harmless
  ieNoOpen: true,

  // No Sniff - prevent MIME type sniffing, important for APIs
  noSniff: true,

  // Origin Agent Cluster - can help with security
  originAgentCluster: true,

  // Permitted Cross Domain Policies - restrict Flash/Silverlight
  permittedCrossDomainPolicies: {
    permittedPolicies: 'none',
  },

  // Referrer Policy - control referrer information
  referrerPolicy: {
    policy: ['strict-origin-when-cross-origin'],
  },

  // X-XSS-Protection - still useful even for APIs
  xssFilter: true,
});

/**
 * API-optimized Helmet middleware factory
 */
export const createApiHelmetMiddleware = (isDevelopment: boolean = false) => {
  return helmet(apiHelmetConfig(isDevelopment));
};

// Legacy web-app configuration for backward compatibility
export const cspDirectives = {
  // Default source - fallback for other directives
  defaultSrc: ["'self'"],

  // Script sources - JavaScript execution
  scriptSrc: [
    "'self'",
    "'unsafe-inline'", // Only if absolutely necessary, consider removing in production
    'https://cdn.jsdelivr.net', // For CDN scripts if needed
    'https://unpkg.com', // For npm packages via CDN
  ],

  // Style sources - CSS
  styleSrc: [
    "'self'",
    "'unsafe-inline'", // Often needed for inline styles, but try to minimize
    'https://fonts.googleapis.com', // Google Fonts
    'https://cdn.jsdelivr.net',
  ],

  // Font sources
  fontSrc: [
    "'self'",
    'https://fonts.gstatic.com', // Google Fonts
    'data:', // For inline fonts
  ],

  // Image sources
  imgSrc: [
    "'self'",
    'data:', // For base64 images
    'https:', // Allow HTTPS images
    'blob:', // For generated images
  ],

  // Connect sources - AJAX, WebSocket, EventSource
  connectSrc: [
    "'self'",
    'wss:', // WebSocket secure connections
    'ws:', // WebSocket connections (consider removing in production)
  ],

  // Frame sources - for iframes
  frameSrc: ["'none'"],

  // Object sources - for <object>, <embed>, <applet>
  objectSrc: ["'none'"],

  // Media sources - for <audio>, <video>
  mediaSrc: ["'self'"],

  // Frame ancestors - controls who can frame this page
  frameAncestors: ["'none'"],

  // Form action - controls where forms can be submitted
  formAction: ["'self'"],

  // Base URI - restricts URLs that can appear in <base> element
  baseUri: ["'self'"],

  // Upgrade insecure requests - automatically upgrade HTTP to HTTPS
  upgradeInsecureRequests: [],
};

export const cspDirectivesDev = {
  ...cspDirectives,
  scriptSrc: [
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'", // Needed for dev tools and hot reloading
    'https://cdn.jsdelivr.net',
    'https://unpkg.com',
    'localhost:*', // For development servers
    '127.0.0.1:*',
  ],
  connectSrc: [
    "'self'",
    'wss:',
    'ws:',
    'localhost:*', // For development servers
    '127.0.0.1:*',
  ],
  styleSrc: [
    "'self'",
    "'unsafe-inline'",
    'https://fonts.googleapis.com',
    'https://cdn.jsdelivr.net',
  ],
};

export const helmetConfig = (
  isDevelopment: boolean = false,
): HelmetOptions => ({
  contentSecurityPolicy: {
    directives: isDevelopment ? cspDirectivesDev : cspDirectives,
    reportOnly: isDevelopment,
  },
  crossOriginResourcePolicy: {
    policy: 'cross-origin',
  },
  dnsPrefetchControl: {
    allow: false,
  },
  frameguard: {
    action: 'deny',
  },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: {
    permittedPolicies: 'none',
  },
  referrerPolicy: {
    policy: ['strict-origin-when-cross-origin'],
  },
  xssFilter: true,
});

export const createHelmetMiddleware = (isDevelopment: boolean = false) => {
  return helmet(helmetConfig(isDevelopment));
};
