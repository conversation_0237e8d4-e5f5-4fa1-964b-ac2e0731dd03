# @telesoft/types

Shared TypeScript type definitions for the Telesoft application ecosystem. This package provides common interfaces, types, and type utilities used across all applications and packages in the monorepo.

## Features

- **Common Interfaces** for users, API responses, and data structures
- **Type Safety** across the entire monorepo
- **Consistent Data Models** for frontend and backend applications
- **Pagination Types** for data fetching and display
- **Authentication Types** for user management

## Installation

This package is part of the Telesoft UI monorepo and should be installed via workspace dependencies:

```json
{
  "dependencies": {
    "@telesoft/types": "workspace:*"
  }
}
```

## Usage

```typescript
import { User, ApiResponse, PaginationParams } from '@telesoft/types';

// Using User interface
const user: User = {
  id: '123',
  email: '<EMAIL>',
  name: '<PERSON>',
  role: 'admin',
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Using ApiResponse for API calls
const response: ApiResponse<User[]> = {
  success: true,
  data: [user],
  message: 'Users fetched successfully',
};

// Using pagination parameters
const params: PaginationParams = {
  page: 1,
  limit: 10,
};
```

## Available Types

### User

User interface for authentication and user management.

### ApiResponse<T>

Generic API response wrapper for consistent backend responses.

### PaginationParams

Common pagination parameters for data fetching.

## Development

### Type Checking

```bash
# From monorepo root
pnpm --filter @telesoft/types type-check

# Or from this directory
pnpm type-check
```

### Linting

```bash
# From monorepo root
pnpm --filter @telesoft/types lint

# Or from this directory
pnpm lint
```

### Watch Mode

```bash
# From this directory
pnpm watch
```

## Adding New Types

When adding new types to this package:

1. Define the type/interface in `index.ts`
2. Export it from the main export
3. Document the type with JSDoc comments
4. Run type checking to ensure compatibility
5. Update this README with the new type

## Usage in Other Packages

This package is used by:

- `@telesoft/intsoc-frontend` - Frontend application types
- `@telesoft/intsoc-backend` - Backend API types
- `@telesoft/ui` - Component prop types
- All other packages requiring shared type definitions

## TypeScript Configuration

This package uses the base TypeScript configuration from `@telesoft/typescript-config`.
