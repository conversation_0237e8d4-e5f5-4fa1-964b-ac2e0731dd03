{"$schema": "https://turbo.build/schema.json", "ui": "stream", "globalDependencies": ["**/.env.*local", "tsconfig.json", "package.json"], "concurrency": "15", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"], "env": ["NODE_ENV"], "inputs": ["src/**", "!**/*.test.*", "!**/*.spec.*"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "PORT", "API_URL", "PUBLIC_URL"]}, "watch": {"cache": false, "persistent": true}, "start": {"cache": false, "dependsOn": ["build"], "env": ["NODE_ENV", "PORT"]}, "package": {"dependsOn": ["build"], "outputs": ["dist/**"], "env": ["NODE_ENV"], "inputs": ["scripts/**", "package.json", ".next/**", "public/**"]}, "lint": {"cache": false, "dependsOn": ["^build"], "outputs": [], "inputs": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".eslintrc*", "eslint.config.*"]}, "type-check": {"dependsOn": ["^build"], "outputs": ["*.tsbuildinfo"], "inputs": ["**/*.ts", "**/*.tsx", "**/tsconfig.json"]}, "test": {"outputs": ["coverage/**"], "env": ["NODE_ENV"], "inputs": ["src/**", "**/*.test.*", "**/*.spec.*", "jest.config.*", "vitest.config.*"]}, "clean": {"cache": false}, "format": {"cache": false}, "validate": {"dependsOn": ["lint", "type-check", "test"], "outputs": []}, "test:watch": {"cache": false, "persistent": true}, "@telesoft/eslint-config#build": {"dependsOn": ["^build"], "outputs": [], "inputs": ["*.mjs", "scripts/**", "package.json"]}, "@telesoft/types#build": {"dependsOn": ["^build"], "outputs": [], "inputs": ["src/**", "tsconfig.json", "package.json"]}, "@telesoft/typescript-config#build": {"dependsOn": ["^build"], "outputs": [], "inputs": ["*.json", "scripts/**", "package.json"]}, "@telesoft/react-hooks#build": {"dependsOn": ["^build"], "outputs": ["dist/**"], "inputs": ["src/**", "tsconfig.json", "package.json", "tsup.config.ts"]}, "@telesoft/utils#build": {"dependsOn": ["^build"], "outputs": ["dist/**"], "inputs": ["src/**", "tsconfig.json", "package.json", "tsup.config.ts"]}, "@telesoft/react-components#build": {"dependsOn": ["^build", "@telesoft/utils#build", "@telesoft/ui#build"], "outputs": ["dist/**"], "inputs": ["src/**", "tsconfig.json", "package.json", "tsup.config.ts"]}, "@telesoft/intsoc-frontend#build": {"dependsOn": ["^build", "@telesoft/ui#build", "@telesoft/d3#build", "@telesoft/types#build", "@telesoft/react-hooks#build", "@telesoft/utils#build", "@telesoft/react-components#build"], "outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV"], "inputs": ["src/**", "!**/*.test.*", "!**/*.spec.*", "public/**", "next.config.*", "tailwind.config.*"]}}}