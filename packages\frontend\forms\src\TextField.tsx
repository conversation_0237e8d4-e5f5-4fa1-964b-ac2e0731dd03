import React from 'react';
import { Input } from '@telesoft/ui';

export interface TextFieldProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  helperText?: string;
  error?: string;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  type?: 'text' | 'email' | 'password' | 'url';
}

export const TextField: React.FC<TextFieldProps> = ({
  label,
  value,
  onChange,
  helperText,
  error,
  disabled,
  placeholder,
  className,
  type = 'text',
}) => (
  <Input
    type={type}
    label={label}
    value={value}
    onChange={(e) => onChange(e.target.value)}
    helperText={helperText}
    error={error}
    disabled={disabled}
    placeholder={placeholder}
    className={className}
  />
);
