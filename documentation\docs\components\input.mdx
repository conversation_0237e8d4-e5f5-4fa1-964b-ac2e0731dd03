---
sidebar_position: 3
---

# Input

The Input component provides form controls for user data entry with validation, error states, and accessibility features.

import { Input, <PERSON><PERSON>, Card, CardContent } from '@telesoft/ui';
import { ComponentShowcase } from '../../src/components/ComponentShowcase';
import { UIThemeProvider } from '../../src/components/UIThemeProvider';

<UIThemeProvider>

## Basic Usage

<ComponentShowcase
  title="Basic Input"
  description="A simple text input with placeholder text."
  component={
    <Input placeholder="Enter your name" style={{ maxWidth: '300px' }} />
  }
  code={`<Input placeholder="Enter your name" />`}
/>

## Input Types

The Input component supports various HTML input types for different data entry needs.

<ComponentShowcase
  title="Text Input"
  description="Standard text input for general text entry."
  component={
    <Input type="text" placeholder="Enter text" style={{ maxWidth: '300px' }} />
  }
  code={`<Input type="text" placeholder="Enter text" />`}
/>

<ComponentShowcase
  title="Email Input"
  description="Email input with built-in validation."
  component={
    <Input
      type="email"
      placeholder="Enter email address"
      style={{ maxWidth: '300px' }}
    />
  }
  code={`<Input type="email" placeholder="Enter email address" />`}
/>

<ComponentShowcase
  title="Password Input"
  description="Password input with hidden text display."
  component={
    <Input
      type="password"
      placeholder="Enter password"
      style={{ maxWidth: '300px' }}
    />
  }
  code={`<Input type="password" placeholder="Enter password" />`}
/>

<ComponentShowcase
  title="Number Input"
  description="Numeric input with step controls."
  component={
    <Input
      type="number"
      placeholder="Enter number"
      style={{ maxWidth: '300px' }}
    />
  }
  code={`<Input type="number" placeholder="Enter number" />`}
/>

## Sizes

Inputs come in different sizes to match various UI contexts.

<ComponentShowcase
  title="Input Sizes"
  description="Small, medium (default), and large input sizes."
  component={
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem',
        maxWidth: '300px',
      }}
    >
      <Input size="sm" placeholder="Small input" />
      <Input size="md" placeholder="Medium input" />
      <Input size="lg" placeholder="Large input" />
    </div>
  }
  code={`<Input size="sm" placeholder="Small input" />
<Input size="md" placeholder="Medium input" />
<Input size="lg" placeholder="Large input" />`}
/>

## States

Inputs support various states for different interaction scenarios.

<ComponentShowcase
  title="Disabled State"
  description="Disabled input prevents user interaction."
  component={
    <Input
      placeholder="Disabled input"
      disabled
      style={{ maxWidth: '300px' }}
    />
  }
  code={`<Input placeholder="Disabled input" disabled />`}
/>

<ComponentShowcase
  title="Required Input"
  description="Input marked as required for form validation."
  component={
    <Input
      placeholder="Required field"
      required
      style={{ maxWidth: '300px' }}
    />
  }
  code={`<Input placeholder="Required field" required />`}
/>

<ComponentShowcase
  title="Input with Default Value"
  description="Input with a pre-filled value."
  component={
    <Input defaultValue="Pre-filled value" style={{ maxWidth: '300px' }} />
  }
  code={`<Input defaultValue="Pre-filled value" />`}
/>

## Form Examples

<ComponentShowcase
  title="Login Form"
  description="Common login form pattern with email and password inputs."
  component={
    <Card style={{ maxWidth: '350px' }}>
      <CardContent>
        <h3 style={{ margin: '0 0 1.5rem 0' }}>Sign In</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <Input type="email" placeholder="Email address" required />
          <Input type="password" placeholder="Password" required />
          <Button variant="primary" style={{ marginTop: '0.5rem' }}>
            Sign In
          </Button>
        </div>
      </CardContent>
    </Card>
  }
  code={`<Card>
  <CardContent>
    <h3>Sign In</h3>
    <div className="form-fields">
      <Input 
        type="email" 
        placeholder="Email address" 
        required 
      />
      <Input 
        type="password" 
        placeholder="Password" 
        required 
      />
      <Button variant="primary">Sign In</Button>
    </div>
  </CardContent>
</Card>`}
/>

<ComponentShowcase
  title="Contact Form"
  description="Multi-field contact form with various input types."
  component={
    <Card style={{ maxWidth: '400px' }}>
      <CardContent>
        <h3 style={{ margin: '0 0 1.5rem 0' }}>Contact Us</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '1rem',
            }}
          >
            <Input placeholder="First name" required />
            <Input placeholder="Last name" required />
          </div>
          <Input type="email" placeholder="Email address" required />
          <Input type="tel" placeholder="Phone number" />
          <Input placeholder="Subject" required />
          <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.5rem' }}>
            <Button variant="outline">Cancel</Button>
            <Button variant="primary">Send Message</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  }
  code={`<Card>
  <CardContent>
    <h3>Contact Us</h3>
    <div className="form-grid">
      <Input placeholder="First name" required />
      <Input placeholder="Last name" required />
    </div>
    <Input type="email" placeholder="Email address" required />
    <Input type="tel" placeholder="Phone number" />
    <Input placeholder="Subject" required />
    <div className="form-actions">
      <Button variant="outline">Cancel</Button>
      <Button variant="primary">Send Message</Button>
    </div>
  </CardContent>
</Card>`}
/>

<ComponentShowcase
  title="Search Interface"
  description="Search input with integrated action button."
  component={
    <div style={{ display: 'flex', gap: '0.5rem', maxWidth: '400px' }}>
      <Input
        type="search"
        placeholder="Search products..."
        style={{ flex: 1 }}
      />
      <Button variant="primary">Search</Button>
    </div>
  }
  code={`<div className="search-container">
  <Input 
    type="search" 
    placeholder="Search products..." 
  />
  <Button variant="primary">Search</Button>
</div>`}
/>

</UIThemeProvider>

## Props

<ComponentShowcase
  title=""
  description=""
  component={null}
  code=""
  props={[
    {
      name: 'type',
      type: "'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'",
      default: "'text'",
      description: 'HTML input type for different data formats',
    },
    {
      name: 'size',
      type: "'sm' | 'md' | 'lg'",
      default: "'md'",
      description: 'Size of the input field',
    },
    {
      name: 'placeholder',
      type: 'string',
      description: 'Placeholder text displayed when input is empty',
    },
    {
      name: 'value',
      type: 'string',
      description: 'Controlled value of the input',
    },
    {
      name: 'defaultValue',
      type: 'string',
      description: 'Default value for uncontrolled input',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Disables the input and prevents interaction',
    },
    {
      name: 'required',
      type: 'boolean',
      default: 'false',
      description: 'Marks the input as required for form validation',
    },
    {
      name: 'className',
      type: 'string',
      description: 'Additional CSS classes to apply',
    },
    {
      name: 'onChange',
      type: '(event: ChangeEvent<HTMLInputElement>) => void',
      description: 'Change event handler',
    },
    {
      name: 'onFocus',
      type: '(event: FocusEvent<HTMLInputElement>) => void',
      description: 'Focus event handler',
    },
    {
      name: 'onBlur',
      type: '(event: FocusEvent<HTMLInputElement>) => void',
      description: 'Blur event handler',
    },
  ]}
/>

## Accessibility

The Input component includes comprehensive accessibility features:

- **Keyboard navigation**: Full keyboard support with Tab navigation
- **Screen reader support**: Proper labeling and announcements
- **Focus management**: Clear focus indicators and proper focus flow
- **ARIA attributes**: Appropriate ARIA labels and descriptions
- **Validation support**: Works with form validation and error messages

## Best Practices

### Do ✅

- Always provide meaningful placeholder text
- Use appropriate input types for different data formats
- Include proper labels for screen readers
- Implement form validation with clear error messages
- Use consistent sizing throughout your application

### Don't ❌

- Don't use placeholder text as the only label
- Don't forget to handle form validation
- Don't make inputs too narrow for their expected content
- Don't disable inputs without clear visual indication
- Don't forget to provide feedback for user actions

## Form Validation

When implementing form validation, consider these patterns:

```tsx
// Controlled input with validation
const [email, setEmail] = useState('');
const [error, setError] = useState('');

const validateEmail = (value: string) => {
  if (!value) {
    setError('Email is required');
  } else if (!/\S+@\S+\.\S+/.test(value)) {
    setError('Please enter a valid email');
  } else {
    setError('');
  }
};

<Input
  type="email"
  value={email}
  onChange={(e) => {
    setEmail(e.target.value);
    validateEmail(e.target.value);
  }}
  placeholder="Email address"
  aria-invalid={!!error}
  aria-describedby={error ? 'email-error' : undefined}
/>;
{
  error && <span id="email-error" role="alert">{error}</span>}
</UIThemeProvider>
```
