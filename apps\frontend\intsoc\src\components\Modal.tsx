'use client';

import { useEffect, useState, useCallback } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: string;
  children: React.ReactNode;
  severity?: string;
  time?: string;
  tabs?: Array<{
    id: string;
    label: string;
    active: boolean;
    onClick: () => void;
  }>;
}

export default function Modal({
  isOpen,
  onClose,
  type,
  children,
  severity,
  time,
  tabs,
}: ModalProps) {
  // Get active tab ID for animation key
  const activeTabId = tabs?.find((tab) => tab.active)?.id || 'default';

  // Animation state
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentTabId, setCurrentTabId] = useState(activeTabId);
  const [displayedChildren, setDisplayedChildren] = useState(children);
  const [scrollContainerRef, setScrollContainerRef] =
    useState<HTMLDivElement | null>(null);

  // Modal visibility animation
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(isOpen);

  // Handle modal opening/closing animation
  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Small delay to ensure the modal is rendered before starting animation
      setTimeout(() => {
        setIsModalVisible(true);
      }, 10);
    } else if (shouldRender) {
      // Only start fade out if modal is currently rendered
      setIsModalVisible(false);
      // Wait for fade out animation to complete before unmounting
      setTimeout(() => {
        setShouldRender(false);
      }, 300);
    }
  }, [isOpen, shouldRender]);

  // Handle close with animation
  const handleClose = useCallback(() => {
    if (isModalVisible) {
      setIsModalVisible(false);
      // Wait for animation to complete before calling onClose
      setTimeout(() => {
        onClose();
      }, 300);
    } else {
      onClose();
    }
  }, [isModalVisible, onClose]);

  // Handle tab changes with animation
  useEffect(() => {
    if (activeTabId !== currentTabId) {
      // Start fade out with current content
      setIsAnimating(true);

      // After fade out completes, switch content and fade in
      setTimeout(() => {
        setCurrentTabId(activeTabId);
        setDisplayedChildren(children);

        // Scroll to top when switching tabs
        if (scrollContainerRef) {
          scrollContainerRef.scrollTop = 0;
        }

        setTimeout(() => {
          setIsAnimating(false);
        }, 50); // Small delay to ensure content is updated before fade in
      }, 100); // Reduced fade out duration from 150ms to 100ms
    } else {
      // Update displayed children immediately if it's the same tab
      setDisplayedChildren(children);
    }
  }, [activeTabId, currentTabId, children, scrollContainerRef]);

  // Close modal on Escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleClose]);

  if (!shouldRender) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 transition-opacity duration-300"
      style={{
        opacity: isModalVisible ? 1 : 0,
      }}
    >
      {/* Enhanced Backdrop with Gradient */}
      <div
        className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/50 to-black/60 backdrop-blur-md transition-opacity duration-300"
        onClick={handleClose}
        style={{
          opacity: isModalVisible ? 1 : 0,
        }}
      />

      {/* Modal Container with Animation */}
      <div
        className="relative max-w-4xl w-full h-[80vh] flex flex-col transition-all duration-300"
        style={{
          opacity: isModalVisible ? 1 : 0,
          transform: isModalVisible
            ? 'scale(1) translateY(0)'
            : 'scale(0.95) translateY(20px)',
        }}
      >
        {/* Sophisticated Modal Content */}
        <div className="relative h-full flex flex-col overflow-hidden bg-gradient-to-br from-background-primary/95 via-background-primary/90 to-background-secondary/95 backdrop-blur-xl border border-border-primary/50 shadow-2xl rounded-3xl ring-1 ring-white/10">
          {/* Subtle Top Accent */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-primary-400/60 to-transparent rounded-b-full"></div>

          {/* Enhanced Close Button */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 z-30 group p-2.5 rounded-full bg-background-secondary/60 border border-border-primary/40 text-text-secondary hover:text-text-primary transition-all duration-300 backdrop-blur-sm hover:bg-background-secondary/80 hover:border-border-primary/60 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-400/50 cursor-pointer"
            aria-label="Close modal"
          >
            <svg
              className="w-5 h-5 transition-transform duration-300 group-hover:rotate-90"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          {/* Sophisticated Header */}
          <div className="relative border-b border-border-secondary/30 flex-shrink-0 bg-gradient-to-r from-background-secondary/20 via-background-secondary/30 to-background-secondary/20 backdrop-blur-sm">
            {/* Main Header Content */}
            <div className="flex items-center justify-between p-6 pr-20">
              {/* Left Section - Title and Time */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-3">
                  <h2 className="text-2xl font-bold text-text-primary capitalize tracking-tight">
                    {type}
                  </h2>
                  {time && (
                    <div className="flex items-center space-x-2 text-sm text-text-secondary/80">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <span className="font-medium">{time}</span>
                    </div>
                  )}
                </div>

                {/* Subtle Divider Line */}
                <div className="w-12 h-0.5 bg-gradient-to-r from-primary-400/60 to-transparent rounded-full"></div>
              </div>

              {/* Right Section - Severity Badge */}
              {severity && (
                <div className="flex items-center mr-4">
                  <div
                    className={`inline-flex items-center px-3 py-1.5 rounded-xl text-xs font-medium border backdrop-blur-sm transition-all duration-300 ${severity === 'critical'
                      ? 'bg-gradient-to-r from-red-500/15 to-red-600/10 text-red-300 border-red-500/30 shadow-red-500/20'
                      : severity === 'high'
                        ? 'bg-gradient-to-r from-orange-500/15 to-orange-600/10 text-orange-300 border-orange-500/30 shadow-orange-500/20'
                        : severity === 'medium'
                          ? 'bg-gradient-to-r from-yellow-500/15 to-yellow-600/10 text-yellow-300 border-yellow-500/30 shadow-yellow-500/20'
                          : severity === 'low'
                            ? 'bg-gradient-to-r from-green-500/15 to-green-600/10 text-green-300 border-green-500/30 shadow-green-500/20'
                            : severity === 'info'
                              ? 'bg-gradient-to-r from-blue-500/15 to-blue-600/10 text-blue-300 border-blue-500/30 shadow-blue-500/20'
                              : severity === 'unknown'
                                ? 'bg-gradient-to-r from-gray-900/15 to-gray-800/10 text-gray-300 border-gray-800/30 shadow-gray-800/20'
                                : 'bg-gradient-to-r from-blue-500/15 to-blue-600/10 text-blue-300 border-blue-500/30 shadow-blue-500/20'
                      } shadow-lg`}
                  >
                    <div
                      className={`w-1.5 h-1.5 rounded-full mr-2 animate-pulse ${severity === 'critical'
                        ? 'bg-red-400'
                        : severity === 'high'
                          ? 'bg-orange-400'
                          : severity === 'medium'
                            ? 'bg-yellow-400'
                            : severity === 'low'
                              ? 'bg-green-400'
                              : severity === 'info'
                                ? 'bg-blue-400'
                                : severity === 'unknown'
                                  ? 'bg-gray-600'
                                  : 'bg-blue-400'
                        }`}
                    ></div>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </div>
                </div>
              )}
            </div>

            {/* Enhanced Tab Navigation */}
            {tabs && tabs.length > 0 && (
              <div className="px-6">
                <div className="flex border-b border-border-primary/20">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={tab.onClick}
                      className={`relative px-6 py-3 text-sm font-medium transition-all duration-300 border-b-2 -mb-px cursor-pointer ${tab.active
                        ? 'border-primary-400 text-text-primary bg-gradient-to-t from-primary-500/10 to-transparent'
                        : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border-primary/50 hover:bg-background-hover/20'
                        }`}
                    >
                      <span className="relative z-10">{tab.label}</span>
                      {tab.active && (
                        <>
                          {/* Active tab background */}
                          <div className="absolute inset-0 bg-gradient-to-t from-background-secondary/20 to-transparent rounded-t-lg"></div>
                          {/* Glowing underline */}
                          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-400/60 via-primary-400 to-primary-400/60"></div>
                        </>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Body */}
          <div
            className="relative flex-1 overflow-y-auto min-h-0"
            ref={setScrollContainerRef}
          >
            {/* Content Area with Better Scrolling */}
            <div
              key={currentTabId}
              className="h-full transition-opacity duration-100 ease-in-out"
              style={{
                opacity: isAnimating ? 0 : 1,
              }}
            >
              <div className="prose prose-invert max-w-none h-full">
                {displayedChildren}
              </div>
            </div>
          </div>

          {/* Fixed Bottom Fade Effect */}
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-background-primary/95 via-background-primary/60 to-transparent pointer-events-none rounded-b-3xl z-20"></div>
        </div>
      </div>
    </div>
  );
}
