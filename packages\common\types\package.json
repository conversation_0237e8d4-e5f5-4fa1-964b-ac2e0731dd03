{"name": "@telesoft/types", "version": "0.0.0", "private": true, "type": "module", "main": "./index.ts", "types": "./index.ts", "exports": {".": "./index.ts"}, "scripts": {"build": "tsc --noEmit", "lint": "eslint .", "format": "prettier --write . --ignore-path ../../../.prettierignore", "type-check": "tsc --noEmit", "watch": "tsc --noEmit --watch"}, "devDependencies": {"@telesoft/eslint-config": "workspace:*", "@telesoft/typescript-config": "workspace:*", "eslint": "catalog:eslint", "typescript": "catalog:typescript-5"}}