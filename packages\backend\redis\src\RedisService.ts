import { RedisClient } from './RedisClient';

export class RedisService {
  constructor(private client: RedisClient) {}

  /**
   * Cache a function result with automatic serialization
   */
  async cacheFunction<T>(
    key: string,
    fn: () => Promise<T>,
    ttlSeconds?: number,
  ): Promise<T> {
    // Try to get cached result first
    const cached = await this.client.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    const result = await fn();
    await this.client.set(key, result, ttlSeconds);
    return result;
  }

  /**
   * Implement a distributed lock pattern
   */
  async withLock<T>(
    lockKey: string,
    fn: () => Promise<T>,
    lockTimeoutSeconds: number = 30,
  ): Promise<T> {
    const lockValue = `${Date.now()}-${Math.random()}`;
    const acquired = await this.acquireLock(
      lockKey,
      lockValue,
      lockTimeoutSeconds,
    );

    if (!acquired) {
      throw new Error(`Failed to acquire lock: ${lockKey}`);
    }

    try {
      return await fn();
    } finally {
      await this.releaseLock(lockKey, lockValue);
    }
  }

  private async acquireLock(
    key: string,
    value: string,
    timeoutSeconds: number,
  ): Promise<boolean> {
    const result = await this.client
      .getClient()
      .set(key, value, 'EX', timeoutSeconds, 'NX');
    return result === 'OK';
  }

  private async releaseLock(key: string, expectedValue: string): Promise<void> {
    const script = `
      if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
      else
        return 0
      end
    `;

    await this.client.getClient().eval(script, 1, key, expectedValue);
  }

  /**
   * Store an object with automatic versioning
   */
  async setVersioned<T>(
    baseKey: string,
    value: T,
    ttlSeconds?: number,
  ): Promise<string> {
    const version = Date.now().toString();
    const versionedKey = `${baseKey}:${version}`;

    await this.client.set(versionedKey, value, ttlSeconds);
    await this.client.set(`${baseKey}:latest`, version, ttlSeconds);

    return version;
  }

  /**
   * Get the latest version of a versioned object
   */
  async getLatestVersioned<T>(baseKey: string): Promise<T | null> {
    const latestVersion = await this.client.get<string>(`${baseKey}:latest`);
    if (!latestVersion) {
      return null;
    }

    return this.client.get<T>(`${baseKey}:${latestVersion}`);
  }

  /**
   * Get a specific version of a versioned object
   */
  async getVersioned<T>(baseKey: string, version: string): Promise<T | null> {
    return this.client.get<T>(`${baseKey}:${version}`);
  }

  /**
   * Batch operations for better performance
   */
  async batch<T>(operations: Array<() => Promise<T>>): Promise<T[]> {
    return Promise.all(operations.map((op) => op()));
  }

  /**
   * Atomic counter with bounds checking
   */
  async boundedIncrement(
    key: string,
    min: number = 0,
    max: number = Number.MAX_SAFE_INTEGER,
    ttlSeconds?: number,
  ): Promise<number> {
    const script = `
      local current = redis.call("get", KEYS[1])
      local value = tonumber(current) or 0
      local min = tonumber(ARGV[1])
      local max = tonumber(ARGV[2])
      local ttl = tonumber(ARGV[3])
      
      if value >= max then
        return value
      end
      
      local newValue = math.max(min, value + 1)
      newValue = math.min(max, newValue)
      
      if ttl and ttl > 0 then
        redis.call("setex", KEYS[1], ttl, newValue)
      else
        redis.call("set", KEYS[1], newValue)
      end
      
      return newValue
    `;

    const result = (await this.client
      .getClient()
      .eval(
        script,
        1,
        key,
        min.toString(),
        max.toString(),
        ttlSeconds?.toString() || '0',
      )) as number;

    return result;
  }

  /**
   * Rate limiting using sliding window
   */
  async isRateLimited(
    key: string,
    limit: number,
    windowSeconds: number,
  ): Promise<boolean> {
    const now = Date.now();

    const script = `
      local key = KEYS[1]
      local limit = tonumber(ARGV[1])
      local window = tonumber(ARGV[2])
      local now = tonumber(ARGV[3])
      
      -- Remove expired entries
      redis.call("zremrangebyscore", key, 0, now - window)
      
      -- Count current entries
      local current = redis.call("zcard", key)
      
      if current >= limit then
        return 1
      end
      
      -- Add current request
      redis.call("zadd", key, now, now)
      redis.call("expire", key, math.ceil(window / 1000))
      
      return 0
    `;

    const result = (await this.client
      .getClient()
      .eval(
        script,
        1,
        `rate_limit:${key}`,
        limit.toString(),
        (windowSeconds * 1000).toString(),
        now.toString(),
      )) as number;

    return result === 1;
  }
}
