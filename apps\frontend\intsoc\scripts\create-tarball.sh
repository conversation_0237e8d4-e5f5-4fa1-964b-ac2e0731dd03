#!/bin/bash

# Create production tarball for Intsoc Frontend
# Can be run from either the app directory or monorepo root

set -e

# Detect if we're running from monorepo root or app directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_DIR="$(dirname "$SCRIPT_DIR")"
MONOREPO_ROOT="$(cd "$APP_DIR/../../../" && pwd)"

# Determine working directory
if [[ "$PWD" == "$MONOREPO_ROOT" ]]; then
    echo "🏠 Running from monorepo root"
    WORKING_DIR="$APP_DIR"
    IS_MONOREPO_ROOT=true
else
    echo "📁 Running from app directory"
    WORKING_DIR="$PWD"
    IS_MONOREPO_ROOT=false
fi

APP_NAME="intsoc-frontend"
VERSION=$(node -p "require('$WORKING_DIR/package.json').version")
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
TARBALL_NAME="${APP_NAME}-${VERSION}-${TIMESTAMP}.tar.gz"
DIST_DIR="$WORKING_DIR/dist"

echo "📦 Creating production tarball for ${APP_NAME} v${VERSION}..."

# Create dist directory
mkdir -p "$DIST_DIR"

# Create temporary directory for packaging
TEMP_DIR=$(mktemp -d)
PACKAGE_DIR="$TEMP_DIR/$APP_NAME"
mkdir -p "$PACKAGE_DIR"

echo "🏗️  Preparing package contents..."

# Change to app directory for consistent file operations
cd "$WORKING_DIR"

# Verify that the build exists
if [ ! -d ".next" ]; then
    echo "❌ Error: .next directory not found in $WORKING_DIR"
    if [ "$IS_MONOREPO_ROOT" = true ]; then
        echo "💡 Hint: Run 'pnpm run build:intsoc' from monorepo root first"
    else
        echo "💡 Hint: Run 'pnpm build' first"
    fi
    exit 1
fi

# Verify essential build files exist
if [ ! -f ".next/BUILD_ID" ]; then
    echo "❌ Error: Build appears incomplete. BUILD_ID not found."
    exit 1
fi

echo "✅ Build verification passed"

# Copy essential files for production deployment
cp -r .next "$PACKAGE_DIR/"
cp -r public "$PACKAGE_DIR/"
cp package.json "$PACKAGE_DIR/"
cp next.config.mjs "$PACKAGE_DIR/"

# Copy scripts directory (needed for start-prod.sh)
mkdir -p "$PACKAGE_DIR/scripts"
cp scripts/start-prod.sh "$PACKAGE_DIR/scripts/"
chmod +x "$PACKAGE_DIR/scripts/start-prod.sh"

# Copy any other necessary config files
if [ -f "postcss.config.mjs" ]; then
    cp postcss.config.mjs "$PACKAGE_DIR/"
fi

if [ -f "tailwind.config.mjs" ]; then
    cp tailwind.config.mjs "$PACKAGE_DIR/"
fi

if [ -f ".env.example" ]; then
    cp .env.example "$PACKAGE_DIR/"
fi

# Create a simple README for the package
cat > "$PACKAGE_DIR/README.md" << EOF
# ${APP_NAME} Production Package

This package contains the built application ready for production deployment.

## Installation

1. Extract the tarball: \`tar -xzf ${TARBALL_NAME}\`
2. Move .env.example to .env and configure environment variables as needed
3. Install dependencies: \`pnpm install --prod\`
4. Start the application: \`pnpm start\`

## Alternative with npm

If pnpm is not available, you can use npm:
1. Extract the tarball: \`tar -xzf ${TARBALL_NAME}\`
2. Move .env.example to .env and configure environment variables as needed
3. Install dependencies: \`npm install\`
4. Start the application: \`npm start\`

## Environment

Make sure to set the appropriate environment variables before starting the application, either using a .env file or directly in your environment.

### Required Environment Variables

\`\`\`bash
# Backend API Configuration (REQUIRED)
NEXT_PUBLIC_API_URL=http://your-backend-server:4001
# or for production
NEXT_PUBLIC_API_URL=https://api.yourdomain.com

# Server Configuration
NEXT_PORT=3000
NODE_ENV=production

# Optional: Logging and debugging
DEBUG=false
LOG_LEVEL=info
\`\`\`

### Backend Connectivity & Error Handling

This frontend application requires a backend API to be available. The application includes robust error handling for backend unavailability:

**Common scenarios handled gracefully:**
- Backend service not running
- Network connectivity issues  
- Temporary backend outages
- Port conflicts or misconfigurations

**Error handling features:**
- Graceful degradation when backend is unavailable
- Automatic retry mechanisms
- Comprehensive error logging
- User-friendly error messages
- Application continues running even when backend is down

**To test backend connectivity:**
\`\`\`bash
# Test if backend is reachable
curl -f http://your-backend-url:4001/health || echo "Backend not available"

# Check application logs for connectivity issues
pm2 logs intsoc-frontend
# or
journalctl -u intsoc-frontend -f
\`\`\`

**Troubleshooting backend connectivity:**
1. Verify backend service is running: \`systemctl status your-backend-service\`
2. Check network connectivity: \`ping your-backend-server\`  
3. Verify port is accessible: \`telnet your-backend-server 4001\`
4. Check firewall rules and security groups
5. Review application logs for detailed error information

## Production Process Management

For reliable production deployment, consider using a process manager:

### PM2 (Recommended)

PM2 provides advanced process management with automatic restarts, load balancing, and monitoring.

\`\`\`bash
# Install PM2 globally
npm install -g pm2

# Start the application
pm2 start npm --name "intsoc-frontend" -- start

# Or create a PM2 ecosystem file (ecosystem.config.js):
module.exports = {
  apps: [{
    name: 'intsoc-frontend',
    script: './scripts/start-prod.sh',
    env: {
      NODE_ENV: 'production',
      NEXT_PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log'
  }]
};

# Then start with: pm2 start ecosystem.config.js
\`\`\`

### Forever

A simple process manager for continuous operation.

\`\`\`bash
# Install Forever globally
npm install -g forever

# Start the application
forever start --minUptime 1000 --spinSleepTime 1000 ./scripts/start-prod.sh

# List running processes
forever list

# Stop the application
forever stop ./scripts/start-prod.sh
\`\`\`

### systemd Service

For Linux systems, create a systemd service for automatic startup and management.

Create \`/etc/systemd/system/intsoc-frontend.service\`:

\`\`\`ini
[Unit]
Description=Intsoc Frontend Application
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/extracted/intsoc-frontend
ExecStart=/path/to/extracted/intsoc-frontend/scripts/start-prod.sh
Restart=always
RestartSec=5
Environment=NODE_ENV=production
Environment=NEXT_PORT=3000

[Install]
WantedBy=multi-user.target
\`\`\`

Then enable and start the service:
\`\`\`bash
sudo systemctl enable intsoc-frontend
sudo systemctl start intsoc-frontend
sudo systemctl status intsoc-frontend
\`\`\`

## Notes

- This package contains a pre-built Next.js application
- All workspace dependencies have been bundled into the build output
- Only runtime dependencies (Next.js, React) need to be installed
- For production environments, always use a process manager for reliability
- Consider setting up log rotation and monitoring for production deployments

Built on: $(date)
Version: ${VERSION}
EOF

# For Next.js apps, the build output is self-contained and doesn't need workspace deps
# But we need to resolve catalog dependencies to actual versions
echo "📋 Creating production package.json..."

# Function to get actual version from pnpm list
get_dependency_version() {
    local dep_name="$1"
    local version
    
    # Try different approaches based on context
    if [ "$IS_MONOREPO_ROOT" = true ]; then
        # From monorepo root, use workspace-specific query
        version=$(cd "$MONOREPO_ROOT" && pnpm list "$dep_name" --filter @telesoft/intsoc-frontend --depth=0 --json 2>/dev/null | node -e "
            try {
                const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                const version = data[0]?.dependencies?.['$dep_name']?.version;
                console.log(version || 'latest');
            } catch (e) {
                console.log('latest');
            }
        ")
    else
        # From app directory, use local query
        version=$(pnpm list "$dep_name" --depth=0 --json 2>/dev/null | node -e "
            try {
                const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                const version = data[0]?.dependencies?.['$dep_name']?.version;
                console.log(version || 'latest');
            } catch (e) {
                console.log('latest');
            }
        ")
    fi
    
    echo "$version"
}

# Get the actual resolved versions
NEXT_VERSION=$(get_dependency_version "next")
REACT_VERSION=$(get_dependency_version "react")
REACT_DOM_VERSION=$(get_dependency_version "react-dom")

# Fallback: if versions are still "latest", try to get from node_modules
if [ "$NEXT_VERSION" = "latest" ] && [ -f "node_modules/next/package.json" ]; then
    NEXT_VERSION=$(node -p "require('./node_modules/next/package.json').version")
elif [ "$NEXT_VERSION" = "latest" ] && [ -f "$MONOREPO_ROOT/node_modules/next/package.json" ]; then
    NEXT_VERSION=$(node -p "require('$MONOREPO_ROOT/node_modules/next/package.json').version")
fi

if [ "$REACT_VERSION" = "latest" ] && [ -f "node_modules/react/package.json" ]; then
    REACT_VERSION=$(node -p "require('./node_modules/react/package.json').version")
elif [ "$REACT_VERSION" = "latest" ] && [ -f "$MONOREPO_ROOT/node_modules/react/package.json" ]; then
    REACT_VERSION=$(node -p "require('$MONOREPO_ROOT/node_modules/react/package.json').version")
fi

if [ "$REACT_DOM_VERSION" = "latest" ] && [ -f "node_modules/react-dom/package.json" ]; then
    REACT_DOM_VERSION=$(node -p "require('./node_modules/react-dom/package.json').version")
elif [ "$REACT_DOM_VERSION" = "latest" ] && [ -f "$MONOREPO_ROOT/node_modules/react-dom/package.json" ]; then
    REACT_DOM_VERSION=$(node -p "require('$MONOREPO_ROOT/node_modules/react-dom/package.json').version")
fi

echo "📋 Resolved versions:"
echo "  - next: $NEXT_VERSION"
echo "  - react: $REACT_VERSION"
echo "  - react-dom: $REACT_DOM_VERSION"

# Create package.json for production (only runtime dependencies needed for Next.js)
cat > "$PACKAGE_DIR/package.json" << EOF
{
  "name": "@telesoft/intsoc-frontend",
  "version": "${VERSION}",
  "private": true,
  "type": "module",
  "scripts": {
    "start": "./scripts/start-prod.sh",
    "postinstall": "echo 'Production package installed successfully'"
  },
  "dependencies": {
    "next": "${NEXT_VERSION}",
    "react": "${REACT_VERSION}",
    "react-dom": "${REACT_DOM_VERSION}"
  },
  "packageManager": "pnpm@$(pnpm --version)"
}
EOF

# Note: Next.js build process bundles workspace dependencies into the .next directory
# so we don't need to include them as runtime dependencies

echo "🗜️  Creating tarball..."

# Create the tarball
cd "$TEMP_DIR"
tar -czf "$TARBALL_NAME" "$APP_NAME"

# Move tarball to dist directory
mv "$TARBALL_NAME" "$DIST_DIR/"

# Cleanup
cd "$WORKING_DIR"
rm -rf "$TEMP_DIR"

echo "✅ Production tarball created: $DIST_DIR/$TARBALL_NAME"
echo "📊 Size: $(du -h "$DIST_DIR/$TARBALL_NAME" | cut -f1)"

# List contents of dist directory
echo "📁 Contents of dist directory:"
ls -la "$DIST_DIR/"
