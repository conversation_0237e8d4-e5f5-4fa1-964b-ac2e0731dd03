import '@testing-library/jest-dom';

// Suppress ReactDOMTestUtils.act deprecation warning
// This warning is coming from @testing-library/react v16.3.0 using the deprecated act from react-dom/test-utils
// instead of the newer React.act. This can be safely suppressed as the functionality is equivalent.
const originalError = console.error;
console.error = (...args: unknown[]) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('ReactDOMTestUtils.act') &&
    args[0].includes('is deprecated')
  ) {
    // Suppress ReactDOMTestUtils.act deprecation warning
    return;
  }
  originalError.call(console, ...args);
};

// Mock IntersectionObserver which is used by some UI components
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(globalThis as any).IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver which might be used by UI components
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(globalThis as any).ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia for responsive design tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  // eslint-disable-next-line no-undef
  value: jest.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    // eslint-disable-next-line no-undef
    addListener: jest.fn(), // deprecated
    // eslint-disable-next-line no-undef
    removeListener: jest.fn(), // deprecated
    // eslint-disable-next-line no-undef
    addEventListener: jest.fn(),
    // eslint-disable-next-line no-undef
    removeEventListener: jest.fn(),
    // eslint-disable-next-line no-undef
    dispatchEvent: jest.fn(),
  })),
});
