{"name": "@telesoft/intsoc-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "./scripts/copy-branding-assets.sh && NODE_OPTIONS='--max-old-space-size=4096' NEXT_TELEMETRY_DISABLED=1 next build --no-lint", "build:package": "./scripts/copy-branding-assets.sh && next build && pnpm run package", "dev": "./scripts/copy-branding-assets.sh && ./scripts/start-dev.sh", "dev:legacy": "./scripts/copy-branding-assets.sh && next dev", "watch": "./scripts/copy-branding-assets.sh && ./scripts/start-dev.sh", "lint": "next lint", "format": "prettier --write . --ignore-path ../../../.prettierignore", "start": "./scripts/start-prod.sh", "type-check": "tsc --noEmit", "clean": "rm -rf .next", "copy-assets": "./scripts/copy-branding-assets.sh", "package": "./scripts/create-tarball.sh"}, "dependencies": {"@telesoft/branding": "workspace:*", "@telesoft/color-palette": "workspace:*", "@telesoft/d3": "workspace:*", "@telesoft/forms": "workspace:*", "@telesoft/react-components": "workspace:*", "@telesoft/react-hooks": "workspace:*", "@telesoft/types": "workspace:*", "@telesoft/ui": "workspace:*", "@telesoft/utils": "workspace:*", "next": "catalog:next-15", "react": "catalog:next-15", "react-dom": "catalog:next-15"}, "devDependencies": {"@eslint/eslintrc": "catalog:eslint", "@tailwindcss/cli": "catalog:next-15", "@tailwindcss/postcss": "catalog:next-15", "@telesoft/typescript-config": "workspace:*", "@typescript-eslint/eslint-plugin": "catalog:eslint", "@typescript-eslint/parser": "catalog:eslint", "@types/node": "catalog:node-24", "@types/react": "catalog:next-15", "@types/react-dom": "catalog:next-15", "autoprefixer": "catalog:next-15", "eslint": "catalog:eslint", "eslint-config-next": "catalog:eslint", "eslint-config-prettier": "catalog:eslint", "postcss": "catalog:next-15", "tailwindcss": "catalog:next-15", "typescript": "catalog:typescript-5"}}