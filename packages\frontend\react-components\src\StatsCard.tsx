import React from 'react';
import { Card, CardContent } from '@telesoft/ui';

export interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  className?: string;
  children?: React.ReactNode;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  variant = 'default',
  className = '',
  children,
}) => {
  const getValueColorClass = () => {
    switch (variant) {
      case 'success':
        return 'text-green-500';
      case 'warning':
        return 'text-yellow-500';
      case 'danger':
        return 'text-red-500';
      case 'info':
        return 'text-blue-500';
      default:
        return 'text-text-primary';
    }
  };

  return (
    <Card
      className={`glass-effect border-border-primary elevation-low ${className}`}
    >
      <CardContent className="p-4 text-center">
        <div className={`text-2xl font-bold ${getValueColorClass()}`}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        <div className="text-sm text-text-secondary">{title}</div>
        {subtitle && (
          <div className="text-xs text-text-subtle mt-1">{subtitle}</div>
        )}
        {children && <div className="mt-3">{children}</div>}
      </CardContent>
    </Card>
  );
};

export interface StatsGridProps {
  stats: Array<{
    title: string;
    value: string | number;
    subtitle?: string;
    variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  }>;
  columns?: 2 | 3 | 4;
  className?: string;
}

export const StatsGrid: React.FC<StatsGridProps> = ({
  stats,
  columns = 4,
  className = '',
}) => {
  const gridClass = `grid grid-cols-2 md:grid-cols-${columns} gap-4`;

  return (
    <div className={`${gridClass} ${className}`}>
      {stats.map((stat, index) => (
        <StatsCard
          key={index}
          title={stat.title}
          value={stat.value}
          subtitle={stat.subtitle}
          variant={stat.variant}
        />
      ))}
    </div>
  );
};
