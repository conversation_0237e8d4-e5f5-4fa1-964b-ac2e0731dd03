const nextConfig = {
  transpilePackages: [
    "@telesoft/ui",
    "@telesoft/types",
    "@telesoft/d3",
    "@telesoft/branding",
    "@telesoft/forms",
    "@telesoft/utils",
    "@telesoft/react-components",
  ],
 
  // Development optimizations
  experimental: {
    optimizePackageImports: [
      "@telesoft/ui",
      "@telesoft/d3",
      "@telesoft/branding",
      "@telesoft/forms",
      "@telesoft/utils",
      "@telesoft/react-components",
    ],
  },
 
  // Enable source maps in development
  productionBrowserSourceMaps: false,
 
  // Enable faster refresh
  reactStrictMode: true,
 
  // Ensure fast refresh is enabled in development
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };
    }
    return config;
  },
};
 
export default nextConfig;