# Monorepo Structure

This document explains the structure and organization of the Telesoft UI monorepo.

## Repository Layout

```mermaid
graph TD
    ROOT[telesoft-ui/]

    ROOT --> APPS[apps/]
    ROOT --> PACKAGES[packages/]
    ROOT --> DOCS[documentation/]
    ROOT --> CONFIG[*.config files]

    APPS --> FE[frontend/]
    APPS --> BE[backend/]

    FE --> FE_INTSOC[intsoc/]
    BE --> BE_INTSOC[intsoc/]

    PACKAGES --> COMMON[common/]
    PACKAGES --> PKG_CONFIG[config/]
    PACKAGES --> PKG_FRONTEND[frontend/]
    PACKAGES --> NODE[node/]

    COMMON --> TYPES[types/]
    PKG_CONFIG --> ESLINT[eslint-config/]
    PKG_CONFIG --> JEST[jest-config/]
    PKG_CONFIG --> TS[typescript-config/]

    PKG_FRONTEND --> UI[ui/]
    PKG_FRONTEND --> BRAND[branding/]
    PKG_FRONTEND --> COLORS[color-palette/]
    PKG_FRONTEND --> D3[d3/]
```

## Package Dependencies

```mermaid
graph LR
    subgraph "Applications"
        APP_FE[Frontend Apps]
        APP_BE[Backend Apps]
    end

    subgraph "Shared Libraries"
        UI[UI Components]
        TYPES[Common Types]
        BRAND[Branding]
        D3[D3 Components]
        COLORS[Color Palette]
    end

    subgraph "Configuration"
        ESLINT[ESLint Config]
        TS_CONFIG[TypeScript Config]
        JEST_CONFIG[Jest Config]
    end

    APP_FE --> UI
    APP_FE --> TYPES
    APP_FE --> BRAND
    APP_FE --> D3
    APP_FE --> COLORS

    APP_BE --> TYPES

    UI --> ESLINT
    UI --> TS_CONFIG
    UI --> JEST_CONFIG

    APP_FE --> ESLINT
    APP_FE --> TS_CONFIG
    APP_BE --> ESLINT
    APP_BE --> TS_CONFIG
```

## Build Pipeline

```mermaid
flowchart TD
    START([Developer Changes]) --> LINT{Lint Check}
    LINT -->|Pass| TYPE{Type Check}
    LINT -->|Fail| LINT_ERROR[❌ Lint Errors]

    TYPE -->|Pass| TEST{Run Tests}
    TYPE -->|Fail| TYPE_ERROR[❌ Type Errors]

    TEST -->|Pass| BUILD[Build Packages]
    TEST -->|Fail| TEST_ERROR[❌ Test Failures]

    BUILD --> DEPLOY{Deploy?}
    DEPLOY -->|Yes| PROD[🚀 Production]
    DEPLOY -->|No| DEV[💻 Development]

    LINT_ERROR --> FIX[Fix Issues]
    TYPE_ERROR --> FIX
    TEST_ERROR --> FIX
    FIX --> START
```

## Workspace Configuration

The monorepo uses several key configuration files:

- **`pnpm-workspace.yaml`**: Defines workspace packages and dependency catalogs
- **`turbo.json`**: Configures build pipeline and caching
- **`package.json`**: Root package with shared scripts and dependencies

### Package Patterns

| Pattern               | Purpose                    | Examples                   |
| --------------------- | -------------------------- | -------------------------- |
| `apps/*`              | Deployable applications    | Frontend/backend apps      |
| `packages/common/*`   | Shared utilities           | Types, constants           |
| `packages/config/*`   | Configuration packages     | ESLint, TypeScript configs |
| `packages/frontend/*` | Frontend-specific packages | UI components, theming     |
| `packages/node/*`     | Node.js specific packages  | Server utilities           |

### Naming Conventions

- **Applications**: `@telesoft-ui/app-{name}`
- **Shared packages**: `@telesoft-ui/{category}-{name}`
- **Configuration**: `@telesoft-ui/config-{tool}`

This structure ensures clear separation of concerns while enabling efficient code sharing and consistent development practices across the entire system.
