# @telesoft/eslint-config

Shared ESLint configurations for Telesoft projects.

## Available Configurations

- **Default** (`@telesoft/eslint-config`): Basic TypeScript and JavaScript linting
- **React Internal** (`@telesoft/eslint-config/react-internal`): React + TypeScript configuration with DOM globals
- **Next.js** (`@telesoft/eslint-config/next`): Next.js specific configuration

## Usage

```javascript
// Basic configuration
import config from "@telesoft/eslint-config";
export default config;

// React configuration
import reactConfig from "@telesoft/eslint-config/react-internal";
export default reactConfig;

// Next.js configuration
import nextConfig from "@telesoft/eslint-config/next";
export default nextConfig;
```

## Development

### Build Scripts

- `pnpm run build` - Validates all configurations
- `pnpm run validate` - Same as build
- `pnpm run validate:configs` - Validates that all config files can be imported
- `pnpm run validate:syntax` - Runs ESLint on the config files themselves
- `npm run lint` - Lints the configuration files

### Adding New Configurations

1. Create a new `.mjs` file with your configuration
2. Add it to the `exports` field in `package.json`
3. Add it to the `files` array in `package.json`
4. Update the validation script to include the new config
5. Run `npm run build` to validate

## Configuration Details

### Base Configuration

- TypeScript support with `@typescript-eslint`
- Modern JavaScript features
- Node.js globals
- Prettier integration

### React Internal Configuration

- All base features
- React and React Hooks support
- JSX accessibility rules
- Browser DOM globals and types
- SVG element types

### Next.js Configuration

- All React features
- Next.js specific rules and optimizations
