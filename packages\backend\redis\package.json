{"name": "@telesoft-ui/redis", "version": "1.0.0", "description": "Redis client wrapper using ioredis with dependency injection support", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src", "format": "prettier --write . --ignore-path ../../../.prettierignore", "test": "jest"}, "dependencies": {"ioredis": "^5.3.2"}, "devDependencies": {"@telesoft/typescript-config": "workspace:*", "@types/jest": "catalog:testing", "@types/node": "catalog:node-24", "@typescript-eslint/eslint-plugin": "catalog:eslint", "@typescript-eslint/parser": "catalog:eslint", "eslint": "catalog:eslint", "jest": "catalog:testing", "ts-jest": "catalog:testing", "typescript": "catalog:typescript-5"}, "files": ["dist"], "publishConfig": {"access": "restricted"}}