import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface AreaChartData {
  x: number | Date;
  y: number;
}

export interface AreaChartProps {
  data: AreaChartData[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  color?: string;
  opacity?: number;
  className?: string;
  xLabel?: string;
  yLabel?: string;
}

export const AreaChart: React.FC<AreaChartProps> = ({
  data,
  width = 400,
  height = 300,
  margin = { top: 20, right: 20, bottom: 40, left: 40 },
  color = '#3b82f6',
  opacity = 0.6,
  className = '',
  xLabel,
  yLabel,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // Clear previous render

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const xScale = d3
      .scaleLinear()
      .domain(
        d3.extent(data, (d: AreaChartData) =>
          typeof d.x === 'number' ? d.x : (d.x as Date).getTime(),
        ) as [number, number],
      )
      .range([0, innerWidth]);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(data, (d: AreaChartData) => d.y) || 0])
      .range([innerHeight, 0]);

    const area = d3
      .area<AreaChartData>()
      .x((d: AreaChartData) =>
        xScale(typeof d.x === 'number' ? d.x : (d.x as Date).getTime()),
      )
      .y0(innerHeight)
      .y1((d: AreaChartData) => yScale(d.y))
      .curve(d3.curveMonotoneX);

    const line = d3
      .line<AreaChartData>()
      .x((d: AreaChartData) =>
        xScale(typeof d.x === 'number' ? d.x : (d.x as Date).getTime()),
      )
      .y((d: AreaChartData) => yScale(d.y))
      .curve(d3.curveMonotoneX);

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add area
    g.append('path')
      .datum(data)
      .attr('fill', color)
      .attr('opacity', opacity)
      .attr('d', area);

    // Add line
    g.append('path')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', color)
      .attr('stroke-width', 2)
      .attr('d', line);

    // Add dots
    g.selectAll('.dot')
      .data(data)
      .enter()
      .append('circle')
      .attr('class', 'dot')
      .attr('cx', (d: AreaChartData) =>
        xScale(typeof d.x === 'number' ? d.x : (d.x as Date).getTime()),
      )
      .attr('cy', (d: AreaChartData) => yScale(d.y))
      .attr('r', 3)
      .attr('fill', color)
      .style('cursor', 'pointer')
      .on('mouseover', function (event, d: AreaChartData) {
        d3.select(this).attr('r', 5);

        // Add tooltip
        const tooltip = d3
          .select('body')
          .append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.transition().duration(200).style('opacity', 1);
        tooltip
          .html(`X: ${d.x}, Y: ${d.y}`)
          .style('left', event.pageX + 10 + 'px')
          .style('top', event.pageY - 10 + 'px');
      })
      .on('mouseout', function () {
        d3.select(this).attr('r', 3);
        d3.selectAll('.tooltip').remove();
      });

    // Add x-axis
    const xAxis = g
      .append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale));

    xAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    xAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add y-axis
    const yAxis = g.append('g').call(d3.axisLeft(yScale));

    yAxis.selectAll('text').style('fill', 'var(--color-text-primary)');
    yAxis.selectAll('path, line').style('stroke', 'var(--color-text-primary)');

    // Add x-axis label
    if (xLabel) {
      g.append('text')
        .attr(
          'transform',
          `translate(${innerWidth / 2}, ${innerHeight + margin.bottom - 5})`,
        )
        .style('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text(xLabel);
    }

    // Add y-axis label
    if (yLabel) {
      g.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('y', 0 - margin.left)
        .attr('x', 0 - innerHeight / 2)
        .attr('dy', '1em')
        .style('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', 'var(--color-text-primary)')
        .text(yLabel);
    }
  }, [data, width, height, margin, color, opacity, xLabel, yLabel]);

  return (
    <div className={className}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ display: 'block' }}
      />
    </div>
  );
};
