/**
 * Next.js API Route: /api/v1/threats
 * Proxies requests to the backend threats endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  backendApiProxy,
  BackendApiError,
} from '../../../../lib/backend-proxy';

export async function GET(request: NextRequest) {
  try {
    console.log('[API Route] GET /api/v1/threats - Proxying to backend...');

    // Check for batch request
    const { searchParams } = new URL(request.url);
    const batchEndpoints = searchParams.get('batch');

    if (batchEndpoints) {
      try {
        const endpoints = batchEndpoints.split(',');
        const results = await backendApiProxy.batchGet(endpoints);
        return NextResponse.json(
          { batch: results },
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'private, max-age=0, must-revalidate',
            },
          },
        );
      } catch (batchError) {
        console.error('[API Route] Batch request error:', batchError);
        return NextResponse.json(
          {
            error: 'Batch Request Failed',
            message: 'Failed to process batch request',
            timestamp: new Date().toISOString(),
          },
          { status: 500 },
        );
      }
    }

    // Forward the request to the backend with proper error handling
    let response;
    try {
      response = await backendApiProxy.forwardRequest('/api/v1/threats', {
        method: 'GET',
      });
    } catch (proxyError) {
      console.error('[API Route] Backend proxy error:', proxyError);

      // Handle specific backend connectivity issues
      if (proxyError instanceof BackendApiError) {
        const isConnectivityIssue =
          proxyError.message.includes('ECONNREFUSED') ||
          proxyError.message.includes('Failed to connect to backend') ||
          proxyError.message.includes('fetch failed');

        if (isConnectivityIssue) {
          return NextResponse.json(
            {
              error: 'Backend Unavailable',
              message:
                'The backend service is currently unavailable. Please try again later.',
              details: 'Backend server connection failed',
              timestamp: new Date().toISOString(),
              retryAfter: 30, // Suggest retry after 30 seconds
            },
            {
              status: 503, // Service Unavailable
              headers: {
                'X-Error-Type': 'backend-unavailable',
                'Retry-After': '30',
              },
            },
          );
        }

        // Other backend API errors
        return NextResponse.json(
          {
            error: 'Backend API Error',
            message: proxyError.message,
            timestamp: new Date().toISOString(),
          },
          {
            status: proxyError.status || 500,
            headers: {
              'X-Error-Type': 'backend-error',
            },
          },
        );
      }

      // Unknown proxy errors
      throw proxyError;
    }

    // Get the response data
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('[API Route] JSON parsing error:', jsonError);
      return NextResponse.json(
        {
          error: 'Response Parse Error',
          message: 'Failed to parse backend response',
          timestamp: new Date().toISOString(),
        },
        { status: 502 }, // Bad Gateway
      );
    }

    // Add ETag for caching
    const etag = `"${Date.now()}"`;
    const ifNoneMatch = request.headers.get('if-none-match');

    if (ifNoneMatch === etag) {
      return new NextResponse(null, { status: 304 });
    }

    // Return the response with appropriate headers
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
        ETag: etag,
        'X-Response-Time': `${Date.now()}ms`,
      },
    });
  } catch (error) {
    console.error('[API Route] Unhandled error in /api/v1/threats:', error);

    // Ensure we always return a proper JSON response
    const errorResponse = {
      error: 'Internal Server Error',
      message: 'An unexpected error occurred while processing the request',
      timestamp: new Date().toISOString(),
    };

    // Add more specific error information if available
    if (error instanceof Error) {
      // Check for specific error types
      if (
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('Failed to connect to backend') ||
        error.message.includes('fetch failed')
      ) {
        errorResponse.error = 'Backend Connection Failed';
        errorResponse.message =
          'Unable to connect to the backend service. The service may be down or unreachable.';

        return NextResponse.json(errorResponse, {
          status: 503, // Service Unavailable
          headers: {
            'X-Error-Type': 'connection-failed',
            'Retry-After': '30',
          },
        });
      }

      // Log detailed error information
      console.error('[API Route] Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
    }

    // Handle BackendApiError specifically
    if (error instanceof BackendApiError) {
      return NextResponse.json(
        {
          error: 'Backend API Error',
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        {
          status: error.status || 500,
          headers: {
            'X-Error-Type': 'backend-error',
          },
        },
      );
    }

    return NextResponse.json(errorResponse, {
      status: 500,
      headers: {
        'X-Error-Type': 'internal-error',
      },
    });
  }
}
