import * as React from 'react';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className = '', label, error, helperText, id, ...props }, ref) => {
    const generatedId = React.useId();
    const inputId = id || generatedId;

    return (
      <div className="space-y-2">
        {label && (
          <label
            htmlFor={inputId}
            className="text-sm font-medium leading-none text-text-primary peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </label>
        )}
        <input
          id={inputId}
          className={`flex h-10 w-full rounded-md border border-border-primary bg-background-tertiary px-3 py-2 text-sm text-text-primary ring-offset-background-primary file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:border-border-accent disabled:cursor-not-allowed disabled:opacity-50 transition-colors ${
            error
              ? 'border-cyber-danger-500 focus-visible:ring-cyber-danger-500'
              : ''
          } ${className}`.trim()}
          ref={ref}
          {...props}
        />
        {error && <p className="text-sm text-cyber-danger-500">{error}</p>}
        {helperText && !error && (
          <p className="text-sm text-text-muted">{helperText}</p>
        )}
      </div>
    );
  },
);

Input.displayName = 'Input';
