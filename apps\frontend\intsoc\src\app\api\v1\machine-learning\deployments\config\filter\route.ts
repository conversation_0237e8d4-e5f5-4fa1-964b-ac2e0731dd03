/**
 * Next.js API Route: /api/v1/machine-learning/deployments/config/filter
 * Proxies requests to the backend deployment filters endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  backendApiProxy,
  BackendApiError,
} from '../../../../../../../lib/backend-proxy';

export async function GET(_request: NextRequest) {
  try {
    console.log(
      '[API Route] GET /api/v1/machine-learning/deployments/config/filter - Proxying to backend...',
    );

    // Forward the request to the backend with proper error handling
    let response;
    try {
      response = await backendApiProxy.forwardRequest(
        '/api/v1/machine-learning/deployments/config/filter',
        {
          method: 'GET',
        },
      );
    } catch (proxyError: unknown) {
      console.error('[API Route] Backend proxy error:', proxyError);

      // Handle specific backend connectivity issues
      if (proxyError instanceof BackendApiError) {
        const isConnectivityIssue =
          proxyError.message.includes('ECONNREFUSED') ||
          proxyError.message.includes('Failed to connect to backend') ||
          proxyError.message.includes('fetch failed');

        if (isConnectivityIssue) {
          return NextResponse.json(
            {
              error: 'Backend Unavailable',
              message:
                'The backend service is currently unavailable. Please try again later.',
              details: 'Backend server connection failed',
              timestamp: new Date().toISOString(),
              retryAfter: 30, // Suggest retry after 30 seconds
            },
            {
              status: 503, // Service Unavailable
              headers: {
                'X-Error-Type': 'backend-unavailable',
                'Retry-After': '30',
              },
            },
          );
        }

        // Other backend API errors
        return NextResponse.json(
          {
            error: 'Backend API Error',
            message: proxyError.message,
            timestamp: new Date().toISOString(),
          },
          {
            status: proxyError.status || 500,
            headers: {
              'X-Error-Type': 'backend-error',
            },
          },
        );
      }

      // Unknown proxy errors
      throw proxyError;
    }

    // Get the response data
    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      console.error(
        '[API Route] Failed to parse backend response:',
        parseError,
      );
      return NextResponse.json(
        {
          error: 'Invalid Response',
          message: 'Backend returned invalid JSON response',
          timestamp: new Date().toISOString(),
        },
        { status: 502 }, // Bad Gateway
      );
    }

    console.log(`[API Route] Backend response: status=${response.status}`);

    // Return the successful response
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=0, must-revalidate',
      },
    });
  } catch (error) {
    console.error('[API Route] GET deployment filters error:', error);

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to fetch deployment filters configuration',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Health check endpoint for deployment filters
 */
export async function HEAD(_request: NextRequest) {
  try {
    // Perform a lightweight check to the backend
    const response = await backendApiProxy.forwardRequest(
      '/api/v1/machine-learning/deployments/config/filter',
      {
        method: 'HEAD',
      },
    );

    return new NextResponse(null, {
      status: response.status,
      headers: {
        'X-Health-Check': 'ok',
        'X-Backend-Status': response.status.toString(),
      },
    });
  } catch (error) {
    console.error('[API Route] Health check failed:', error);

    return new NextResponse(null, {
      status: 503,
      headers: {
        'X-Health-Check': 'failed',
        'X-Backend-Status': 'unavailable',
      },
    });
  }
}
