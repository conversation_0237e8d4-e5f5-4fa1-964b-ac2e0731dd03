# @telesoft/color-palette

A **modern, refined** dark-themed color system designed specifically for cybersecurity applications, built around the primary brand color **#1AA1DC** with enhanced depth, sophistication, and contemporary design principles.

## 🎨 Color Philosophy

This color palette has been carefully crafted with modern design principles to:

- **Enhance readability** in low-light environments common in SOC (Security Operations Center) settings
- **Reduce eye strain** during extended monitoring sessions with refined contrast ratios
- **Convey trust and professionalism** through strategic use of contemporary blues and sophisticated neutrals
- **Provide clear visual hierarchy** with enhanced depth and modern surface treatments
- **Support accessibility** with WCAG AA compliance and improved color discrimination
- **Embrace modern aesthetics** with glass morphism effects, refined gradients, and contemporary color harmony

## 📦 Installation

This package is part of the Telesoft UI monorepo and should be installed via workspace dependencies:

```json
{
  "dependencies": {
    "@telesoft/color-palette": "workspace:*"
  }
}
```

## 🚀 Usage

### Basic Usage

```typescript
import { colors, semanticColors } from '@telesoft-ui/color-palette';

// Use brand colors
const primaryColor = colors.primary[500]; // #1AA1DC
const darkBackground = colors.background.primary; // #0a0f1c

// Use semantic colors
const successColor = semanticColors.status.online; // #22c55e
const warningColor = semanticColors.security.medium; // #f59e0b
```

### With Tailwind CSS v3

```javascript
// tailwind.config.js
import { tailwindPreset } from '@telesoft-ui/color-palette/tailwind';

export default {
  presets: [tailwindPreset],
  content: [
    // your content paths
  ],
  // ... rest of your config
};
```

Or extend your existing theme:

```javascript
// tailwind.config.js
import { tailwindColors } from '@telesoft-ui/color-palette/tailwind';

export default {
  theme: {
    extend: {
      colors: tailwindColors,
    },
  },
};
```

### With Tailwind CSS v4

For Tailwind CSS v4, use the CSS-first approach:

```css
/* globals.css or your main CSS file */
@import 'tailwindcss';
@import '@telesoft/color-palette/tailwind-v4.css';
```

With a minimal Tailwind config:

```javascript
// tailwind.config.js
export default {
  content: [
    // your content paths
  ],
};
```

The v4 approach automatically includes all color utilities and custom classes like `cyber-grid-bg`, `cyber-glow`, etc.

### With CSS Variables

```typescript
import {
  applyCssVariables,
  generateCssVariables,
} from '@telesoft-ui/color-palette/css';

// Apply variables to document (browser only)
applyCssVariables();

// Or generate CSS string for injection
const cssString = generateCssVariables();
```

```css
/* Use in your CSS */
.my-component {
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
  border-color: var(--color-border-accent);
}
```

## 🌈 Color Categories

### Primary Brand Colors

Built around the brand color **#1AA1DC** with a full spectrum from light to dark:

```typescript
colors.primary[50]; // #e6f7fd (lightest)
colors.primary[500]; // #1AA1DC (brand color)
colors.primary[950]; // #031016 (darkest)
```

### Cybersecurity Theme Colors

#### Matrix Green (`cyber.matrix`)

For success states, secure connections, valid data (refined with modern vibrancy):

```typescript
colors.cyber.matrix[500]; // #10b981 (enhanced emerald)
```

#### Warning Orange (`cyber.warning`)

For alerts, pending states, caution areas (warmer, more sophisticated):

```typescript
colors.cyber.warning[500]; // #f59e0b (refined amber)
```

#### Danger Red (`cyber.danger`)

For errors, critical threats, urgent alerts (maintained intensity):

```typescript
colors.cyber.danger[500]; // #ef4444 (consistent red)
```

### Modern Accent Colors

New sophisticated accent colors for enhanced visual appeal:

```typescript
colors.accent.purple; // #8b5cf6 - Creative accents
colors.accent.teal; // #14b8a6 - Fresh highlights
colors.accent.indigo; // #6366f1 - Professional accents
```

### Background System

Modern layered dark backgrounds with enhanced depth and sophistication:

```typescript
colors.background.primary; // #0a0d14 - Main app background (refined)
colors.background.secondary; // #0f1419 - Cards, panels (enhanced depth)
colors.background.tertiary; // #161b22 - Elevated surfaces (modern treatment)
colors.background.hover; // #1c212b - Interactive states (refined)
colors.background.active; // #21262d - Active states (new)
colors.background.elevated; // #262c36 - Highest elevation (new)
```

### Text Hierarchy

Optimized contrast ratios with modern readability enhancements:

```typescript
colors.text.primary; // #f6f8fa - Main content, headings (enhanced contrast)
colors.text.secondary; // #d1d9e0 - Supporting text, labels (refined)
colors.text.muted; // #8b949e - Subtle text, metadata (improved)
colors.text.subtle; // #6e7681 - Ultra-subtle content (new)
colors.text.accent; // #1AA1DC - Links, highlighted content
```

### Modern Surface Colors

New surface treatments for components and UI elements:

```typescript
colors.surface.primary; // #161b22 - Primary surfaces
colors.surface.secondary; // #21262d - Secondary surfaces
colors.surface.tertiary; // #30363d - Tertiary surfaces
colors.surface.hover; // #3d444d - Hover states
colors.surface.pressed; // #484f58 - Pressed states
```

### Enhanced Border System

Refined border colors with better visual definition:

```typescript
colors.border.primary; // #30363d - Primary borders (refined)
colors.border.secondary; // #3d444d - Secondary borders (enhanced)
colors.border.muted; // #21262d - Muted borders (new)
colors.border.subtle; // #161b22 - Subtle borders (new)
```

## 🔧 Semantic Colors

Pre-defined semantic color mappings with modern refinements:

```typescript
// Status indicators (refined for better accessibility)
semanticColors.status.online; // #10b981 (enhanced emerald)
semanticColors.status.offline; // #64748b (refined gray)
semanticColors.status.warning; // #f59e0b (consistent amber)
semanticColors.status.error; // #ef4444 (maintained red)

// Security levels (updated with modern palette)
semanticColors.security.high; // #10b981 (enhanced green)
semanticColors.security.medium; // #f59e0b (refined amber)
semanticColors.security.low; // #ef4444 (consistent red)
semanticColors.security.unknown; // #64748b (neutral gray)

// Interactive states (enhanced vibrancy)
semanticColors.interactive.default; // #1AA1DC (primary blue)
semanticColors.interactive.hover; // #3db5ff (brighter hover)
semanticColors.interactive.active; // #0d8bc9 (deeper active)
semanticColors.interactive.disabled; // #475569 (muted state)
```

## 🎨 Utility Functions

### Color Manipulation

```typescript
import { rgba, withOpacity } from '@telesoft-ui/color-palette';

// Convert hex to rgba
const transparentBlue = rgba('#1AA1DC', 0.5); // rgba(26, 161, 220, 0.5)

// Add opacity to any color
const semiTransparent = withOpacity('#22c55e', 0.8);
```

### Gradients

```typescript
import { gradients } from '@telesoft-ui/color-palette';

// Modern gradient definitions
gradients.cyber; // #1AA1DC to #10b981 (refined cyberpunk)
gradients.cyberSubtle; // Subtle cyber gradient with opacity
gradients.danger; // Red gradient for alerts
gradients.warning; // Amber gradient for warnings
gradients.background; // Dark background gradient
```

### Modern Effects

```typescript
// Glass morphism effects
.glass-effect        // Subtle backdrop blur with transparency
.glass-effect-strong // Strong backdrop blur for modals

// Enhanced elevation system
.elevation-low       // Subtle shadow for cards
.elevation-medium    // Medium shadow for dialogs
.elevation-high      // Strong shadow for overlays

// Refined glow effects
.cyber-glow-xl       // Extra large cyber glow
.glow-purple         // Purple accent glow
```

## 🎯 Usage Examples

### React Component

```tsx
import React from 'react';
import { colors, semanticColors } from '@telesoft-ui/color-palette';

const SecurityStatus = ({ level }: { level: 'high' | 'medium' | 'low' }) => {
  const statusColor = semanticColors.security[level];

  return (
    <div
      style={{
        backgroundColor: colors.background.secondary,
        color: colors.text.primary,
        border: `1px solid ${statusColor}`,
        padding: '12px',
        borderRadius: '8px',
      }}
    >
      <span style={{ color: statusColor }}>
        Security Level: {level.toUpperCase()}
      </span>
    </div>
  );
};
```

### Tailwind Classes

```html
<!-- Backgrounds -->
<div class="bg-background-primary">
  <div class="bg-background-secondary">
    <!-- Text -->
    <h1 class="text-text-primary">
      <p class="text-text-secondary">
        <span class="text-text-muted">
          <!-- Status colors -->
          <div class="bg-cyber-matrix-500">
            <!-- Success -->
            <div class="bg-cyber-warning-500">
              <!-- Warning -->
              <div class="bg-cyber-danger-500">
                <!-- Danger -->

                <!-- Enhanced effects and modern utilities -->
                <div class="cyber-grid-bg-dense elevation-medium glass-effect">
                  <div class="bg-cyber-matrix-500 glow-green">
                    <!-- Success with glow -->
                    <div class="bg-cyber-warning-500 gradient-warning">
                      <!-- Warning gradient -->
                      <div class="bg-accent-purple glow-purple">
                        <!-- Purple accent -->

                        <!-- Glass morphism components -->
                        <div class="glass-effect p-6">
                          <!-- Subtle transparency with backdrop blur -->
                        </div>

                        <!-- Modern gradients -->
                        <button
                          class="gradient-cyber text-white hover:glow-green"
                        ></button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div></div
        ></span>
      </p>
    </h1>
  </div>
</div>
```

## 🎨 Modern Design Features

### Glass Morphism Effects

```css
.glass-effect {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(22, 27, 34, 0.85);
  border: 1px solid rgba(48, 54, 61, 0.5);
}
```

### Enhanced Elevation System

```css
.elevation-low {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}
.elevation-medium {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}
.elevation-high {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}
```

### Modern Animations

```css
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## ♿ Accessibility

All color combinations meet WCAG AA standards:

- **Primary text on dark backgrounds**: 4.5:1+ contrast ratio
- **Secondary text on dark backgrounds**: 3:1+ contrast ratio
- **Interactive elements**: Clear focus indicators with 3:1+ contrast
- **Status colors**: Distinguishable by both color and additional visual cues

## 📊 Color Psychology

**Refined Modern Palette Psychology:**

- **Blue** (#1AA1DC): Trust, reliability, technology, communication - enhanced vibrancy for modern appeal
- **Emerald Green** (#10b981): Security, success, modern "go" signals, sophisticated safe states
- **Amber** (#f59e0b): Contemporary caution, refined attention, balanced priority
- **Red** (#ef4444): Danger, critical alerts, immediate action required - maintained intensity
- **Purple Accents** (#8b5cf6): Innovation, creativity, premium features, modern sophistication
- **Teal Accents** (#14b8a6): Fresh highlights, modern vitality, contemporary appeal
- **Sophisticated Grays**: Enhanced neutrality, refined disabled states, modern information hierarchy

## 🔄 Migration from Frontend App

If you're migrating from the frontend INTSOC app, here's how to update:

### Before

```typescript
// apps/frontend/intsoc/src/styles/colors.ts
import { colors } from '../styles/colors';
```

### After

```typescript
// Using the package
import { colors } from '@telesoft-ui/color-palette';
```

### Tailwind Config Update

```javascript
// Before
import colors from './src/styles/colors';

// After
import { tailwindPreset } from '@telesoft-ui/color-palette/tailwind';

export default {
  presets: [tailwindPreset],
  // ... rest of config
};
```

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests to our [GitHub repository](https://github.com/telesoft-ui/telesoft-ui).

## 📄 License

MIT © Telesoft UI Team

---

Built with ❤️ for cybersecurity professionals who deserve beautiful, functional interfaces.
