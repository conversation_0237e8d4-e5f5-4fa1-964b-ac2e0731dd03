import { useState, useEffect, useCallback, useRef } from 'react';

export interface DataFetchingConfig {
  autoFetch?: boolean;
  refetchInterval?: number;
  retryAttempts?: number;
  retryDelay?: number;
  cacheTime?: number;
}

export interface DataResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdate: string | null;
  refetch: () => Promise<void>;
  isStale: boolean;
}

export function useDataFetching<T>(
  fetchFn: () => Promise<T>,
  config: DataFetchingConfig = {},
): DataResult<T> {
  const {
    autoFetch = true,
    refetchInterval,
    retryAttempts = 3,
    retryDelay = 1000,
    cacheTime = 300000, // 5 minutes default
  } = config;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const retryCountRef = useRef(0);
  const refetchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const retryTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const mountedRef = useRef(true);

  const isStale = lastUpdate
    ? Date.now() - new Date(lastUpdate).getTime() > cacheTime
    : true;

  const handleError = useCallback((err: unknown): string => {
    if (err instanceof Error) {
      if (
        err.message.includes('Backend Unavailable') ||
        err.message.includes('Service Unavailable') ||
        err.message.includes('503')
      ) {
        return 'Backend service is temporarily unavailable. Please try again later.';
      } else if (
        err.message.includes('Failed to connect') ||
        err.message.includes('Connection refused') ||
        err.message.includes('ECONNREFUSED')
      ) {
        return 'Unable to connect to the backend service. Please check your connection and try again.';
      } else {
        return err.message;
      }
    }
    return 'An unexpected error occurred';
  }, []);

  const fetchData = useCallback(
    async (isRetry = false) => {
      if (!mountedRef.current) return;

      try {
        if (!isRetry) {
          setLoading(true);
          setError(null);
        }

        const result = await fetchFn();

        if (!mountedRef.current) return;

        setData(result);
        setError(null);
        setLastUpdate(new Date().toISOString());
        retryCountRef.current = 0;
      } catch (err) {
        if (!mountedRef.current) return;

        const errorMessage = handleError(err);
        console.error('Data fetching failed:', err);

        // Retry logic
        if (retryCountRef.current < retryAttempts) {
          retryCountRef.current++;

          retryTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              fetchData(true);
            }
          }, retryDelay * retryCountRef.current); // Exponential backoff

          return;
        }

        setError(errorMessage);
      } finally {
        if (mountedRef.current) {
          setLoading(false);
        }
      }
    },
    [fetchFn, handleError, retryAttempts, retryDelay],
  );

  const refetch = useCallback(async () => {
    retryCountRef.current = 0;
    await fetchData();
  }, [fetchData]);

  // Setup automatic refetching
  useEffect(() => {
    if (refetchInterval && refetchInterval > 0) {
      const setupRefetch = () => {
        if (refetchTimeoutRef.current) {
          clearTimeout(refetchTimeoutRef.current);
        }

        refetchTimeoutRef.current = setTimeout(() => {
          if (mountedRef.current) {
            refetch();
            setupRefetch(); // Schedule next refetch
          }
        }, refetchInterval);
      };

      setupRefetch();

      return () => {
        if (refetchTimeoutRef.current) {
          clearTimeout(refetchTimeoutRef.current);
        }
      };
    }
  }, [refetchInterval, refetch]);

  // Initial fetch
  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }

    return () => {
      mountedRef.current = false;
      if (refetchTimeoutRef.current) {
        clearTimeout(refetchTimeoutRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [autoFetch, fetchData]);

  return {
    data,
    loading,
    error,
    lastUpdate,
    refetch,
    isStale,
  };
}
